# Alterações - Exibição Condicional de Farol e SLA

## Resumo
Implementação de validação condicional para exibir/ocultar as colunas "Farol" e "SLA Hrs Restantes" na tabela de Dados Técnicos, baseado na configuração do campo adicional `habilitar_farol_dados_tecnicos` da empresa.

## Data
1 de outubro de 2025

## Arquivos Modificados

### 1. Controller: `application/controllers/atribuir_grupo.php`

#### 1.1. Método `index()` - Linha ~596-597
```php
$has_farol_sla = in_array('habilitar_farol_dados_tecnicos', explode('|', $empresa->campos_adicionais));
$data['has_farol_sla'] = $has_farol_sla;
```

**Alteração:** Adicionada variável `$has_farol_sla` que verifica se a empresa tem o campo adicional habilitado e passa para a view.

---

#### 1.2. <PERSON><PERSON><PERSON><PERSON> `prepare_filter_data()` - Lin<PERSON> ~100-107
```php
// Carregar dados da empresa para verificar configurações
$id_empresa = sess_user_company();
$empresa = $this->empresa_model->get_entry($id_empresa);
$campos_adicionais = explode('|', $empresa->campos_adicionais);

// Verificar se o Farol SLA está habilitado para a empresa
$has_farol_sla = in_array('habilitar_farol_dados_tecnicos', $campos_adicionais);
```

**Alteração:** Adicionada verificação da configuração da empresa no início do método para usar na lógica de filtros.

---

#### 1.3. Método `prepare_filter_data()` - Filtro Condicional (Linhas ~345-365)
```php
// Adicionar filtro Farol SLA somente se habilitado para a empresa
if ($has_farol_sla) {
    $advanced_filters[] = [
        'type' => 'select',
        'name' => 'farol_sla',
        'title' => 'Selecione...',
        'label' => 'Farol SLA',
        'options' => [
            ['value' => 'verde', 'label' => 'Verde'],
            ['value' => 'amarelo', 'label' => 'Amarelo'],
            ['value' => 'vermelho', 'label' => 'Vermelho']
        ],
        'value' => $filtros['farol_sla'] ?? [],
        'ajax_url' => '',
        'select_config' => [
            'multiple' => true,
            'data-live-search' => 'false',
            'data-actions-box' => 'true'
        ]
    ];
}
```

**Alteração:** O filtro "Farol SLA" agora é adicionado dinamicamente ao array `$advanced_filters` apenas se `$has_farol_sla` for `true`. Isso garante que o filtro só apareça no modal de filtros quando a empresa tem a funcionalidade habilitada.

---

### 2. View: `application/views/atribuir_grupo/index_new.php`

#### 2.1. CSS - Adicionado Classes Condicionais (Linhas ~228-237)
```css
/* Ajustes de largura quando Farol e SLA estão ocultos */
.sem-farol-sla .col-owner,
.sem-farol-sla .col-ncm {
    width: 19%;
}

.sem-farol-sla .col-descricao {
    width: 38%;
}
```

**Explicação:** Quando a classe `sem-farol-sla` é aplicada na tabela:
- **Owner/NCM:** Aumenta de 12% para 19% (+7%)
- **Descrição:** Aumenta de 28% para 38% (+10%)
- Total redistribuído: 17% (que corresponde aos 12% do SLA + 5% do Farol)

---

#### 2.2. JavaScript - Variável Global (Linha ~314)
```javascript
window.has_farol_sla = <?php echo json_encode($has_farol_sla); ?>;
```

**Explicação:** Disponibiliza a variável para uso em scripts JavaScript que possam manipular a tabela dinamicamente.

---

#### 2.3. HTML - Tabela com Classe Condicional (Linha ~473)
```php
<table class="table table-bordered table-hover table-layout-fixed <?php echo !$has_farol_sla ? 'sem-farol-sla' : ''; ?>">
```

**Explicação:** Aplica a classe `sem-farol-sla` quando `$has_farol_sla` é `false`, ativando os estilos CSS de redistribuição de largura.

---

#### 2.4. HTML - Cabeçalho da Tabela (Linhas ~487-492)
```php
<th class="text-primary col-prior">Prior.</th>
<?php if ($has_farol_sla) : ?>
    <th class="text-primary col-sla">SLA Hrs restantes</th>
    <th class="text-primary col-farol">Farol</th>
<?php endif; ?>
<th class="text-primary col-descricao">Descrição</th>
```

**Explicação:** Colunas de cabeçalho "SLA Hrs restantes" e "Farol" só aparecem quando `$has_farol_sla` é `true`.

---

#### 2.5. HTML - Corpo da Tabela (Linhas ~637-658)
```php
<td class="col-prior"><?php echo $item->empresa_prioridade ?? 'Normal'; ?></td>
<?php if ($has_farol_sla) : ?>
    <td class="col-sla">
        <?php
        $tempo_restante = $item->tempo_restante ?? 0;
        echo number_format($tempo_restante, 1, ',', '');
        ?>
    </td>
    <td class="col-farol">
        <?php
        // Lógica do farol (verde/amarelo/vermelho)
        $horas_sla_prioridade = $item->horas_sla_prioridade ?? 1;
        $tempo_consumido_total = ($item->tempo_consumido_becomex ?? 0) + ($item->tempo_ultimo_status_becomex ?? 0);
        
        if ($horas_sla_prioridade == 0) {
            $horas_sla_prioridade = 1;
        }
        
        $percentual_consumido = ($tempo_consumido_total / $horas_sla_prioridade) * 100;
        
        // Exibição de ícones baseado no percentual
        if ($percentual_consumido <= 75) {
            echo '<i class="fa fa-circle text-success" ...></i>';
        } elseif ($percentual_consumido < 100) {
            echo '<i class="fa fa-exclamation-triangle text-warning" ...></i>';
        } else {
            echo '<i class="fa fa-times-circle text-danger" ...></i>';
        }
        ?>
    </td>
<?php endif; ?>
<td class="col-descricao"><?php echo $item->descricao ?? 'N/A'; ?></td>
```

**Explicação:** Células de dados de SLA e Farol só são renderizadas quando `$has_farol_sla` é `true`.

---

## Lógica de Larguras das Colunas

### Com Farol e SLA Habilitados (`$has_farol_sla = true`)
| Coluna | Largura | Descrição |
|--------|---------|-----------|
| Checkbox | 4% | Seleção de item |
| Part Number | 15% | Código do item |
| Owner/NCM | 12% | Owner ou NCM (condicional) |
| Estab. | 8% | Estabelecimento |
| Peso | 7% | Peso do item |
| Prior. | 10% | Prioridade |
| **SLA Hrs restantes** | **12%** | **Horas restantes do SLA** |
| **Farol** | **5%** | **Indicador visual (verde/amarelo/vermelho)** |
| Descrição | 28% | Descrição do item |

### Sem Farol e SLA (`$has_farol_sla = false`)
| Coluna | Largura | Descrição | Variação |
|--------|---------|-----------|----------|
| Checkbox | 4% | Seleção de item | - |
| Part Number | 15% | Código do item | - |
| Owner/NCM | **19%** | Owner ou NCM (condicional) | **+7%** |
| Estab. | 8% | Estabelecimento | - |
| Peso | 7% | Peso do item | - |
| Prior. | 10% | Prioridade | - |
| ~~SLA Hrs restantes~~ | ~~0%~~ | ~~Oculta~~ | **-12%** |
| ~~Farol~~ | ~~0%~~ | ~~Oculta~~ | **-5%** |
| Descrição | **38%** | Descrição do item | **+10%** |

---

## Comportamento

### Quando `habilitar_farol_dados_tecnicos` está ATIVO na empresa:
✅ Exibe colunas "SLA Hrs restantes" e "Farol"  
✅ Usa larguras padrão: Owner/NCM (12%), Descrição (28%)  
✅ Mantém lógica de cálculo de SLA e exibição de farol colorido

### Quando `habilitar_farol_dados_tecnicos` está INATIVO na empresa:
❌ Oculta colunas "SLA Hrs restantes" e "Farol"  
✅ Redistribui espaço: Owner/NCM (19%), Descrição (38%)  
✅ Aplica classe CSS `sem-farol-sla` na tabela  
✅ Mantém todas as outras funcionalidades intactas

---

## Testes Recomendados

1. **Teste com campo habilitado:**
   - Verificar se as colunas SLA e Farol aparecem
   - Validar cálculo correto do SLA
   - Verificar cores do farol (verde ≤75%, amarelo <100%, vermelho ≥100%)

2. **Teste com campo desabilitado:**
   - Verificar se as colunas SLA e Farol estão ocultas
   - Validar que Owner/NCM e Descrição ocupam mais espaço
   - Confirmar que não há quebras de layout

3. **Teste de responsividade:**
   - Verificar comportamento em diferentes resoluções
   - Validar que o texto não transborda das células

4. **Teste de JavaScript:**
   - Verificar se `window.has_farol_sla` está acessível
   - Validar que scripts dependentes funcionam corretamente

---

## Observações Importantes

- ✅ Alteração é **retrocompatível** - não quebra funcionalidades existentes
- ✅ Utiliza validação PHP nativa - não adiciona dependências
- ✅ CSS usa seletores específicos - não afeta outras tabelas
- ✅ JavaScript tem variável global disponível para futuras extensões
- ⚠️ Redistribuição de largura é automática via CSS (sem JavaScript)

---

## Autor
Adriano de Oliveira  
Data: 1 de outubro de 2025
