/**
 * JS específico para o modal de filtros reutilizável
 * Gerencia carregamento dinâmico de selects e dependências
 */

var FilterModal = {
  base_url: "",
  optionsCache: {}, // Cache para armazenar options carregadas

  init: function () {
    this.modal = $("#filterModal");

    // Se o modal ainda não existe, tentar novamente em alguns instantes
    if (this.modal.length === 0) {
      setTimeout(() => {
        this.init();
      }, 500);
      return;
    }

    this.bindEvents();
    this.handleDependencies();
  },

  bindEvents: function () {
    let self = this;

    // Remover event listeners anteriores para evitar duplicação
    $(document).off("show.bs.select.filterModal");
    $(document).off("show.bs.modal.filterModal");

    // Evento para carregamento lazy dos selects (apenas quando clicado)
    $(document).on(
      "show.bs.select.filterModal",
      "#filterModal select[data-ajax-url]",
      function () {
        let select = $(this);
        let loaded = select.data("loaded");
        let dependsOn = select.data("depends-on");
        let selectId = select.attr("id");

        // Se já foi carregado, não fazer nada
        if (loaded) return;

        // Se tem dependência, verificar se o campo pai tem valor
        if (dependsOn) {
          var parentValue = $("#filterModal #" + dependsOn).val();
          if (!parentValue || parentValue === "-1") {
            // Se não tem valor no campo pai, mostrar mensagem
            select
              .empty()
              .append(
                '<option value="">Selecione ' +
                  dependsOn.replace("_", " ") +
                  " primeiro</option>"
              );
            select.selectpicker("refresh");
            return;
          }
        }

        // Para o select de pré-agrupamento, usar endpoint específico se disponível
        let ajaxUrl = select.data("ajax-url");
        if (
          selectId === "pre_agrupamento" &&
          self.modal.data("endpoint-tags")
        ) {
          ajaxUrl = self.modal.data("endpoint-tags");
        }

        self.loadSelectOptions(select, ajaxUrl, parentValue);
      }
    );

    // Reset do carregamento ao abrir o modal (apenas se não há cache)
    $(document).on("show.bs.modal.filterModal", "#filterModal", function () {
      $("#filterModal select[data-ajax-url]").each(function () {
        let select = $(this);
        let selectId = select.attr("id");
        let ajaxUrl = select.data("ajax-url");
        let filterOptions = select.data("filter-options") === "true";
        let cacheKey =
          selectId + "_" + ajaxUrl + "_" + "" + "_" + filterOptions;

        // Só resetar se não há cache para este select
        if (!self.optionsCache[cacheKey]) {
          select.data("loaded", false);
        }
      });
    });
  },

  initSelects: function () {
    // Inicializar selectpickers no modal
    $("#filterModal .selectpicker").selectpicker();
  },

  loadAjaxSelects: function () {
    // Método removido - carregamento agora é lazy
  },

  loadSelectOptions: function (select, ajaxUrl, parentValue = null) {
    let self = this;
    let selectId = select.attr("id");
    let filterOptions = select.data("filter-options") === "true";

    // Criar chave única para o cache baseada no select, url e dependências
    let cacheKey =
      selectId +
      "_" +
      ajaxUrl +
      "_" +
      (parentValue || "") +
      "_" +
      filterOptions;

    // Verificar se já temos os dados em cache
    if (this.optionsCache[cacheKey]) {
      this.populateSelectOptions(
        select,
        this.optionsCache[cacheKey],
        filterOptions
      );
      select.data("loaded", true);
      select.attr("disabled", false);
      select.selectpicker("refresh");
      // Disparar evento mesmo quando carrega do cache, para garantir que os badges sejam atualizados
      $(document).trigger('filterOptionsLoaded', [select.attr('id')]);
      return;
    }

    // Mostrar loading
    select.attr("disabled", "disabled").html("<option>Carregando...</option>");
    select.selectpicker("refresh");

    let postData = {
      filter_options: filterOptions,
    };

    // Se tem valor do campo pai, incluir no request
    if (parentValue !== null) {
      postData.usuario = parentValue;
    }

    $.post(ajaxUrl, postData, function (data) {
      let response = typeof data === "string" ? JSON.parse(data) : data;

      // Verificar se é resposta de erro
      if (response && response.error) {
        console.error("Erro ao carregar opções:", response.message);
        select.html("<option>Erro ao carregar</option>");
      } else {
        // Salvar no cache ANTES de popular
        self.optionsCache[cacheKey] = response;

        // Para o endpoint de tags, os dados vêm diretamente na resposta
        self.populateSelectOptions(select, response, filterOptions);
      }

      // Marcar como carregado e reabilitar
      select.data("loaded", true);
      select.attr("disabled", false);
      select.selectpicker("refresh");

      // Notificar que as opções para este select foram carregadas
      $(document).trigger('filterOptionsLoaded', [select.attr('id')]);

    }).fail(function (xhr, status, error) {
      console.error("Erro na requisição AJAX:", {
        url: ajaxUrl,
        status: status,
        error: error,
        response: xhr.responseText,
      });
      select.html("<option>Erro ao carregar</option>");
      select.selectpicker("refresh");
    });
  },

  // Método para limpar cache (útil se precisar forçar reload)
  clearOptionsCache: function (selectId = null) {
    if (selectId) {
      // Limpar cache específico de um select
      Object.keys(this.optionsCache).forEach((key) => {
        if (key.startsWith(selectId + "_")) {
          delete this.optionsCache[key];
        }
      });
    } else {
      // Limpar todo o cache
      this.optionsCache = {};
    }
  },

  populateSelectOptions: function (select, data, filterOptions) {
    let selectId = select.attr("id");

    // Se for o filtro de tags (pré-agrupamento), usar lógica específica
    if (selectId === "pre_agrupamento") {
      this.populateTagsOptions(select, data, filterOptions);
    } else if (selectId === "atribuido_para") {
      // Para o filtro de usuários, usar lógica específica com badges
      this.populateUsuariosOptions(select, data);
    } else {
      // Para outros selects, usar lógica genérica
      this.populateGenericOptions(select, data);
    }
  },

  populateTagsOptions: function (select, data, filterOptions) {
    select.empty();

    // Verificar se data é um objeto válido
    if (!data || typeof data !== "object") {
      return;
    }

    // Tags especiais: cliente e becomex
    if (data["cliente"]) {
      let clienteTotal = data["cliente"].total_itens_pendentes || 0;
      select.append(
        '<option value="cliente">CLIENTE – Dúvida no item (' +
          clienteTotal +
          " itens)</option>"
      );
    }

    if (data["becomex"]) {
      let becomexTotal = data["becomex"].total_itens_pendentes || 0;
      select.append(
        '<option value="becomex">BECOMEX – Grupo tarifário não encontrado (' +
          becomexTotal +
          " itens)</option>"
      );
    }

    // Outras tags
    $.each(data, function (tag, item) {
      // Pular tags especiais já processadas
      if (item.tag == "cliente" || item.tag == "becomex") {
        return true;
      }

      if (item.tag != null && item.tag != "") {
        let perc = Math.round((item.perc || 0) * 100) / 100;

        // Se filter_options é true, só mostrar tags com perc < 100
        if (!filterOptions || (filterOptions && item.perc < 100)) {
          select.append(
            '<option value="' +
              item.tag +
              '">' +
              item.tag +
              " (" +
              perc +
              "% identificado)</option>"
          );
        }
      }
    });

    // Aplicar valores salvos após carregar as opções
    this.applySavedValuesToSelect(select);
  },

  populateUsuariosOptions: function (select, response) {
    select.empty();
    let hasOptions = false;

    // Verificar se é a nova estrutura com success/data
    const data = response && response.data ? response.data : response;

    // Adicionar opções básicas (Todos, Meu usuário, Sem responsável)
    if (Array.isArray(data.options)) {
      data.options.forEach((opt) => {
        if (opt.divider) {
          select.append('<option data-divider="true"></option>');
        }
        select.append(
          '<option value="' + opt.value + '">' + opt.text + "</option>"
        );
        hasOptions = true;
      });
    }

    // Adicionar usuários ativos com badge (replicando estilo do index.php)
    if (
      data.usuarios_ativos &&
      data.usuarios_ativos.optgroups &&
      data.usuarios_ativos.optgroups.length > 0
    ) {
      select.append(`
        <option disabled style="margin-top: 5px; border-top: 1px solid #e5e5e5; padding-top: 5px;">
          <span class="label label-success" style="font-size: 12px; border-radius: 10px;">Usuários ativos</span>
        </option>
      `);

      data.usuarios_ativos.optgroups.forEach((group) => {
        const $optgroup = $("<optgroup>").attr("label", group.label);

        group.options.forEach((opt) => {
          $optgroup.append(`
            <option style="padding-left: 44px;" value="${opt.value}" data-subtext="${opt.subtext}">
              ${opt.text}
            </option>
          `);
          hasOptions = true;
        });

        select.append($optgroup);
      });
    }

    // Adicionar usuários inativos com badge (replicando estilo do index.php)
    if (
      data.usuarios_inativos &&
      data.usuarios_inativos.optgroups &&
      data.usuarios_inativos.optgroups.length > 0
    ) {
      select.append(`
        <option disabled>
          <span class="label label-danger" style="font-size: 12px; border-radius: 10px;">Usuários inativos</span>
        </option>
      `);

      data.usuarios_inativos.optgroups.forEach((group) => {
        const $optgroup = $("<optgroup>").attr("label", group.label);

        group.options.forEach((opt) => {
          $optgroup.append(`
            <option style="padding-left: 44px;" value="${opt.value}" data-subtext="${opt.subtext}">
              ${opt.text}
            </option>
          `);
          hasOptions = true;
        });

        select.append($optgroup);
      });
    }

    // Caso não tenha opções
    if (!hasOptions) {
      select.append('<option value="">Nenhuma opção encontrada</option>');
    }

    // Aplicar valores salvos após carregar as opções
    this.applySavedValuesToSelect(select);

    // Aplicar estilos customizados após o selectpicker ser atualizado (replicando do index.php)
    setTimeout(function () {
      $(".btn-group.bootstrap-select")
        .find(".dropdown-header")
        .css("margin-left", "12px");
      $(".btn-group.bootstrap-select").find("li.divider").remove(); // Remove todos os li.divider do dropdown gerado
    }, 100);
  },

  populateGenericOptions: function (select, response) {
    select.empty();
    let hasOptions = false;

    // Caso seja array simples de opções
    if (Array.isArray(response)) {
      response.forEach((opt) => {
        const value = opt.value || opt.codigo;
        const text = opt.text || opt.label || opt.descricao;
        if (opt.divider) {
          select.append('<option data-divider="true"></option>');
        } else {
          select.append('<option value="' + value + '">' + text + "</option>");
        }
        hasOptions = true;
      });
    } else if (response && response.data) {
      // Caso seja objeto com data.options/data.optgroups
      const data = response.data;
      if (Array.isArray(data.options)) {
        data.options.forEach((opt) => {
          if (opt.divider) {
            select.append('<option data-divider="true"></option>');
          } else {
            select.append(
              '<option value="' + opt.value + '">' + opt.text + "</option>"
            );
          }
          hasOptions = true;
        });
      }
      if (Array.isArray(data.optgroups)) {
        data.optgroups.forEach((group) => {
          const $optgroup = $(
            '<optgroup label="' + group.label + '"></optgroup>'
          );
          group.options.forEach((opt) => {
            const subtext = opt.subtext
              ? ' data-subtext="' + opt.subtext + '"'
              : "";
            $optgroup.append(
              '<option value="' +
                opt.value +
                '"' +
                subtext +
                ">" +
                opt.text +
                "</option>"
            );
            hasOptions = true;
          });
          select.append($optgroup);
        });
      }
    } else if (response && Array.isArray(response.options)) {
      // Caso seja objeto com options/optgroups direto
      response.options.forEach((opt) => {
        if (opt.divider) {
          select.append('<option data-divider="true"></option>');
        } else {
          select.append(
            '<option value="' + opt.value + '">' + opt.text + "</option>"
          );
        }
        hasOptions = true;
      });
      if (Array.isArray(response.optgroups)) {
        response.optgroups.forEach((group) => {
          const $optgroup = $(
            '<optgroup label="' + group.label + '"></optgroup>'
          );
          group.options.forEach((opt) => {
            const subtext = opt.subtext
              ? ' data-subtext="' + opt.subtext + '"'
              : "";
            $optgroup.append(
              '<option value="' +
                opt.value +
                '"' +
                subtext +
                ">" +
                opt.text +
                "</option>"
            );
            hasOptions = true;
          });
          select.append($optgroup);
        });
      }
    }

    // Caso não tenha opções
    if (!hasOptions) {
      select.append('<option value="">Nenhuma opção encontrada</option>');
    }

    // Aplicar valores salvos após carregar as opções
    this.applySavedValuesToSelect(select);
  },

  // Aplica valores salvos a um select específico após carregamento AJAX
  applySavedValuesToSelect: function (select) {
    // Verificar se FilterModalIntegration está disponível
    if (
      typeof FilterModalIntegration === "undefined" ||
      !FilterModalIntegration.getSavedFilters
    ) {
      return;
    }

    const selectId = select.attr("id");
    const savedFilters = FilterModalIntegration.getSavedFilters();

    // Mapear IDs dos selects para nomes dos filtros salvos
    const filterMapping = {
      pacotes_eventos: "pacotes_eventos",
      sistema_origem: "sistema_origem",
      owner: "owner",
      prioridade: "prioridade",
      atribuido_para: "atribuido_para",
      status_classificacao_fiscal: "status_classificacao_fiscal",
      pre_agrupamento: "pre_agrupamento",
    };

    const filterName = filterMapping[selectId];
    if (!filterName || !savedFilters[filterName]) {
      return;
    }

    const savedValue = savedFilters[filterName];

    // Aplicar valor salvo se não estiver vazio
    if (
      savedValue &&
      (Array.isArray(savedValue) ? savedValue.length > 0 : savedValue !== "")
    ) {
      // Pequeno delay para garantir que o DOM foi atualizado
      setTimeout(function () {
        if (Array.isArray(savedValue)) {
          select.selectpicker("val", savedValue);
        } else {
          select.selectpicker("val", [savedValue]);
        }
        select.selectpicker("refresh");
      }, 50);
    }
  },

  // Função para lidar com dependências entre filtros
  handleDependencies: function () {
    // Quando o filtro de usuário muda, resetar o carregamento do select dependente
    $("#atribuido_para")
      .off("change.filterModal")
      .on("change.filterModal", function () {
        const userId = $(this).val();

        // Resetar a flag loaded do select de tags para forçar recarregamento
        const tagsSelect = $("#pre_agrupamento");
        tagsSelect.data("loaded", false);

        // Se nenhum usuário selecionado, limpar o select
        if (!userId || userId === "-1") {
          tagsSelect.empty();
          tagsSelect.append('<option selected value="">Todos</option>');
          tagsSelect.selectpicker("refresh");
        }
        // O carregamento das tags acontecerá quando o usuário clicar no select
      });
  },

  reloadDependentSelect: function (dependentSelect, parentValue) {
    let ajaxUrl = dependentSelect.data("ajax-url");

    if (ajaxUrl) {
      // Resetar o select dependente
      dependentSelect.data("loaded", false);
      this.loadSelectOptions(dependentSelect, ajaxUrl, parentValue);
    }
  },

  // Método para submeter o formulário do modal
  submitFilters: function () {
    let formData = new FormData(document.getElementById("filterForm"));
    let endpoint = $("#filterForm").data("endpoint");

    // Coletar valores dos filtros aplicados
    let appliedFilters = this.collectAppliedFilters();

    // Adicionar ao formData o valor do input de pesquisa principal
    formData.append("search", $("#main_search_input").val());

    // Mostrar loading overlay
    this.showLoadingOverlay();

    // Submeter via reload da página (migração para padrão das outras telas)
    this.submitWithPageReload(formData, endpoint, appliedFilters);
  },

  // Método para submeter com reload da página
  submitWithPageReload: function (formData, endpoint, appliedFilters) {
    // Criar um formulário temporário para submissão
    let form = document.createElement("form");
    form.method = "POST";
    form.action = endpoint || window.location.href;
    form.style.display = "none";

    // Adicionar todos os dados do FormData ao formulário
    for (let pair of formData.entries()) {
      let input = document.createElement("input");
      input.type = "hidden";
      input.name = pair[0];
      input.value = pair[1];
      form.appendChild(input);
    }

    // Adicionar campo para indicar que é submissão de filtros
    let filteredInput = document.createElement("input");
    filteredInput.type = "hidden";
    filteredInput.name = "filtered";
    filteredInput.value = "1";
    form.appendChild(filteredInput);

    // Preservar página atual se existir
    let currentPage = new URLSearchParams(window.location.search).get(
      "per_page"
    );
    if (currentPage) {
      let pageInput = document.createElement("input");
      pageInput.type = "hidden";
      pageInput.name = "per_page";
      pageInput.value = currentPage;
      form.appendChild(pageInput);
    }

    // Adicionar ao documento e submeter
    document.body.appendChild(form);
    form.submit();
  },

  // Método para mostrar loading overlay
  showLoadingOverlay: function () {
    // Mostrar loading no botão do modal
    $("#btn-filtrar-modal")
      .prop("disabled", true)
      .html('<i class="fa fa-spinner fa-spin"></i> Filtrando...');

    // Mostrar overlay geral da página (similar ao mestre_itens)
    this.createLoadingOverlay();
  },

  // Método para criar overlay de loading
  createLoadingOverlay: function () {
    // Usar overlay existente se disponível
    if ($("#loading-overlay").length) {
      $("#loading-overlay").show();
      return;
    }

    // Criar overlay caso não exista (fallback)
    let overlay = $(
      '<div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 9999;">' +
        '<div id="loading-message" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 24px;">' +
        "Carregando..." +
        "</div>" +
        "</div>"
    );

    $("body").append(overlay);
  },

  // Método legado (manter para compatibilidade, mas não usar)
  submitFiltersAjax: function () {
    let formData = new FormData(document.getElementById("filterForm"));
    let endpoint = $("#filterForm").data("endpoint");

    // Pegar o valor do input de pesquisa principal pois quando esta ativo não está no formData
    let mainSearchValue = $("#main_search_input").val();

    // Coletar valores dos filtros aplicados
    let appliedFilters = this.collectAppliedFilters();

    // Mostrar loading
    $("#btn-filtrar-modal")
      .prop("disabled", true)
      .html('<i class="fa fa-spinner fa-spin"></i> Filtrando...');

    $.ajax({
      url: endpoint,
      type: "POST",
      data: { ...formData, search: mainSearchValue },
      processData: false,
      contentType: false,
      success: function (response) {
        // Salvar filtros aplicados no backend
        if (
          typeof FilterModalIntegration !== "undefined" &&
          FilterModalIntegration.saveFiltersToBackend
        ) {
          FilterModalIntegration.saveFiltersToBackend(appliedFilters);
        }

        // Fechar modal
        $("#filterModal").modal("hide");

        // Recarregar a tabela/itens com os novos filtros
        if (
          typeof atribuir_grupos !== "undefined" &&
          atribuir_grupos.get_itens
        ) {
          atribuir_grupos.get_itens();
        }

        // Resetar botão
        $("#btn-filtrar-modal").prop("disabled", false).html("Filtrar");
      },
      error: function (xhr, status, error) {
        console.error("Erro ao aplicar filtros:", error);
        alert("Erro ao aplicar filtros. Tente novamente.");

        // Resetar botão
        $("#btn-filtrar-modal").prop("disabled", false).html("Filtrar");
      },
    });
  },

  // Coletar valores dos filtros aplicados
  collectAppliedFilters: function () {
    let filters = {};

    // Coletar valores dos campos do formulário
    $("#filterForm")
      .find("input, select, textarea")
      .each(function () {
        let $field = $(this);
        let name = $field.attr("name");
        let type = $field.attr("type");
        let value = $field.val();

        if (name && value !== "" && value !== null) {
          if (type === "checkbox") {
            if ($field.is(":checked")) {
              filters[name] = "1";
            }
          } else if ($field.is("select[multiple]")) {
            let selectedValues = $field.val();
            if (selectedValues && selectedValues.length > 0) {
              filters[name] = selectedValues;
            }
          } else if (type === "date" || type === "text" || type === "textarea") {
            filters[name] = value;
          } else if ($field.is("select")) {
            if (value !== "" && value !== "-1") {
              filters[name] = value;
            }
          }
        }
      });

    return filters;
  },
};

(function ($) {
  function buildAndSubmitSearch() {
    var $btn = $("#btn-pesquisar");
    if ($btn.data("submitting")) return; // prevenir duplo clique
    $btn
      .data("submitting", true)
      .prop("disabled", true)
      .html(
        '<i class="fa fa-spinner fa-spin" style="margin-right: 5px;"></i> Pesquisando...'
      );

    var endpoint = (function () {
      var $form = $("#filterForm");
      if ($form.length) {
        return (
          $form.data("endpoint") || $form.attr("action") || window.location.href
        );
      }
      return window.location.href; // fallback
    })();

    var form = document.createElement("form");
    form.method = "POST";
    form.action = endpoint;
    form.style.display = "none";

    // Texto principal de pesquisa
    var mainValue = $("#main_search_input").val();
    var inputMain = document.createElement("input");
    inputMain.type = "hidden";
    inputMain.name = "search";
    inputMain.value = mainValue;
    form.appendChild(inputMain);

    var anyFilter = false;

    // Copiar campos do formulário de filtros existente (mesma lógica de submit do modal)
    var $filterForm = $("#filterForm");
    if ($filterForm.length) {
      $filterForm.find("input, select, textarea").each(function () {
        var $field = $(this);
        var name = $field.attr("name");
        if (!name || name === "search") return; // já adicionado

        // Ignorar elementos sem valor relevante (ex: botão)
        if ($field.is(":button") || $field.is(":submit")) return;

        // Multiplo select
        if ($field.is("select") && $field.prop("multiple")) {
          var values = $field.val();
          if (values && values.length) {
            anyFilter = true;
            values.forEach(function (v) {
              var h = document.createElement("input");
              h.type = "hidden";
              h.name = name.endsWith("[]") ? name : name + "[]";
              h.value = v;
              form.appendChild(h);
            });
          }
          return;
        }

        // Checkbox
        if ($field.attr("type") === "checkbox") {
          if (!$field.prop("checked")) return;
        }

        var val = $field.val();
        if (
          val !== null &&
          val !== "" &&
          !(Array.isArray(val) && val.length === 0)
        ) {
          anyFilter = true;
          var hidden = document.createElement("input");
          hidden.type = "hidden";
          hidden.name = name;
          hidden.value = val;
          form.appendChild(hidden);
        }
      });
    }

    // Caso deseje também incluir os filtros salvos em memória que ainda não possuem campos (backup)
    if (
      typeof FilterModalIntegration !== "undefined" &&
      FilterModalIntegration.getSavedFilters
    ) {
      var saved = FilterModalIntegration.getSavedFilters();
      Object.keys(saved || {}).forEach(function (key) {
        // já virão do formulário se existirem campos; só complementar se não existe
        if (
          form.querySelector('[name="' + key + '"]') ||
          form.querySelector('[name="' + key + '[]"]')
        )
          return;
        var value = saved[key];
        if (
          value === null ||
          value === undefined ||
          value === "" ||
          (Array.isArray(value) && value.length === 0)
        )
          return;
        anyFilter = true;
        if (Array.isArray(value)) {
          value.forEach(function (v) {
            var h = document.createElement("input");
            h.type = "hidden";
            h.name = key + "[]";
            h.value = v;
            form.appendChild(h);
          });
        } else {
          var h2 = document.createElement("input");
          h2.type = "hidden";
          h2.name = key;
          h2.value = value;
          form.appendChild(h2);
        }
      });
    }

    // Flag filtered se houver algum filtro aplicado
    if (anyFilter) {
      var filteredInput = document.createElement("input");
      filteredInput.type = "hidden";
      filteredInput.name = "filtered";
      filteredInput.value = "1";
      form.appendChild(filteredInput);
    }

    // Preservar página atual (per_page) se presente – ou resetar para primeira página?
    var perPage = new URLSearchParams(window.location.search).get("per_page");
    if (perPage) {
      var pp = document.createElement("input");
      pp.type = "hidden";
      pp.name = "per_page";
      pp.value = perPage;
      form.appendChild(pp);
    }

    // Overlay de loading reutilizando existente
    if ($("#loading-overlay").length) {
      $("#loading-overlay").fadeIn(150);
      $("#loading-message").text("Carregando...");
    }

    document.body.appendChild(form);
    form.submit();
  }

  $(document).on("click", "#btn-pesquisar", function (e) {
    e.preventDefault();
    buildAndSubmitSearch();
  });

  // Atalho: Ctrl+Enter dentro do textarea executa pesquisa
  $(document).on("keydown", "#main_search_input", function (e) {
    if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
      e.preventDefault();
      $("#btn-pesquisar").trigger("click");
    }
  });
})(jQuery);

// Inicializar quando o documento estiver pronto
$(document).ready(function () {
  // Assumir que base_url está disponível globalmente
  if (typeof base_url !== "undefined") {
    FilterModal.init(base_url);
  }
});
