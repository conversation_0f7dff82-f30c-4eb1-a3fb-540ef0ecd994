/**
 * Componente de seleção em massa para a tela de atribuir grupos
 * Implementação em JavaScript puro inspirada no BulkSelectionFooter.vue
 */

window.BulkSelectionFooter = {
  // Configurações
  config: {
    footerId: "bulk-selection-footer",
    containerClass: "bulk-selection-footer",
    selectedItems: [],
    totalItems: 0,
    isLoadingSelectAll: false,
    hasSelectedAll: false,
  },

  /**
   * Inicializar o componente
   */
  init: function () {
    this.createFooterElement();
    this.updateTotalItems();
    this.setupCheckboxListeners();
    this.setupMasterCheckbox();
    this.updateDisplay();
  },

  /**
   * Criar elemento do footer
   */
  createFooterElement: function () {
    // Remover footer existente se houver
    const existingFooter = document.getElementById(this.config.footerId);
    if (existingFooter) {
      existingFooter.remove();
    }

    // Criar novo footer
    const footer = document.createElement("div");
    footer.id = this.config.footerId;
    footer.className = this.config.containerClass;
    footer.style.cssText = `
      position: fixed;
      bottom: 0px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #3276b1;
      color: white;
      padding: 10px 25px;
      z-index: 1000;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
      border-radius: 6px 6px 0 0;
      white-space: nowrap;
      transition: bottom 0.2s;
      display: none;
    `;

    footer.innerHTML = `
      <div class="footer-content" style="display: flex; align-items: center; gap: 20px; font-size: 14px;">
        <span class="selection-summary" style="font-weight: 500;">
          Você selecionou: <span id="selected-count" style="font-weight: 600;">0</span> item(ns).
        </span>
        <div class="footer-actions" style="display: flex; align-items: center; gap: 12px;">
          <a href="#" id="select-all-link" class="footer-action-link" style="color: white; text-decoration: underline; cursor: pointer; font-weight: 600; font-size: 14px;">
            Selecionar todos os <span id="total-count">0</span> item(ns)
          </a>
          <span class="separator" style="color: #e9ecef; font-weight: 300;">|</span>
          <a href="#" id="clear-selection-link" class="footer-action-link" style="color: white; text-decoration: underline; cursor: pointer; font-weight:600; font-size: 14px;">
            Limpar seleção
          </a>
        </div>
      </div>
    `;

    document.body.appendChild(footer);
    this.setupFooterListeners();
  },

  /**
   * Configurar listeners do footer
   */
  setupFooterListeners: function () {
    const self = this;

    // Selecionar todos
    document
      .getElementById("select-all-link")
      .addEventListener("click", function (e) {
        e.preventDefault();
        self.handleSelectAll();
      });

    // Limpar seleção
    document
      .getElementById("clear-selection-link")
      .addEventListener("click", function (e) {
        e.preventDefault();
        self.handleClearSelection();
      });

    // Atribuir selecionados
    // document
    //   .getElementById("atribuir-selected-link")
    //   .addEventListener("click", function (e) {
    //     e.preventDefault();
    //     self.handleAtribuir();
    //   });
  },
  /**
   * Atualizar total de itens
   */
  updateTotalItems: function () {
    // Buscar total de itens do badge de resultado
    const badge = document.querySelector(".result-container-badge");
    if (badge) {
      this.config.totalItems = parseInt(badge.textContent) || 0;
    }

    // Fallback: contar checkboxes na página
    if (this.config.totalItems === 0) {
      this.config.totalItems = document.querySelectorAll(
        'input[name="item[]"]'
      ).length;
    }

    // Atualizar display
    const totalCountSpan = document.getElementById("total-count");
    if (totalCountSpan) {
      totalCountSpan.textContent = this.config.totalItems;
    }
  },

  /**
   * Configurar listeners para checkboxes
   */
  setupCheckboxListeners: function () {
    const self = this;

    // Listener para mudanças nos checkboxes individuais
    $(document).on("change", 'input[name="item[]"]', function () {
      self.updateSelectedItems();
      self.updateDisplay();
      self.updateMasterCheckboxState();
    });
  },

  /**
   * Configurar checkbox master (selecionar todos)
   */
  setupMasterCheckbox: function () {
    const self = this;
    const masterCheckbox = document.getElementById("select-all-checkbox");

    if (masterCheckbox) {
      masterCheckbox.addEventListener("change", function () {
        if (this.checked) {
          self.handleSelectAll();
        } else {
          self.handleClearSelection();
        }
      });
    }
  },

  /**
   * Atualizar estado do checkbox master baseado na seleção atual
   */
  updateMasterCheckboxState: function () {
    const masterCheckbox = document.getElementById("select-all-checkbox");
    if (!masterCheckbox) return;

    const allCheckboxes = document.querySelectorAll(
      'input[name="item[]"]:not(:disabled)'
    );
    const checkedCheckboxes = document.querySelectorAll(
      'input[name="item[]"]:checked'
    );

    if (checkedCheckboxes.length === 0) {
      // Nenhum selecionado
      masterCheckbox.checked = false;
      masterCheckbox.indeterminate = false;
    } else if (checkedCheckboxes.length === allCheckboxes.length) {
      // Todos selecionados
      masterCheckbox.checked = true;
      masterCheckbox.indeterminate = false;
    } else {
      // Alguns selecionados (estado indeterminado)
      masterCheckbox.checked = false;
      masterCheckbox.indeterminate = true;
    }
  },

  /**
   * Atualizar lista de itens selecionados
   */
  updateSelectedItems: function () {
    const checkboxes = document.querySelectorAll(
      'input[name="item[]"]:checked'
    );
    this.config.selectedItems = Array.from(checkboxes).map((checkbox) => ({
      part_number: checkbox.value,
      estabelecimento: checkbox.dataset.estabelecimento,
      tag: checkbox.dataset.tag,
      descricao: checkbox.dataset.descricao,
      ncm: checkbox.dataset.ncmItem,
    }));
  },

  /**
   * Atualizar display do footer
   */
  updateDisplay: function () {
    const footer = document.getElementById(this.config.footerId);
    const selectedCountSpan = document.getElementById("selected-count");

    if (footer && selectedCountSpan) {
      selectedCountSpan.textContent = this.config.selectedItems.length;

      // Mostrar/ocultar footer baseado na seleção
      if (this.config.selectedItems.length > 0) {
        footer.style.display = "block";
      } else {
        footer.style.display = "none";
      }
    }
  },

  /**
   * Selecionar todos os itens
   */
  handleSelectAll: function () {
    if (this.config.isLoadingSelectAll) return;

    this.config.isLoadingSelectAll = true;

    // Selecionar todos os checkboxes visíveis
    const allCheckboxes = document.querySelectorAll(
      'input[name="item[]"]:not(:disabled)'
    );
    allCheckboxes.forEach((checkbox) => {
      checkbox.checked = true;
    });

    this.updateSelectedItems();
    this.updateDisplay();
    this.updateMasterCheckboxState();
    this.config.hasSelectedAll = true;

    // Disparar evento para o componente Diana quando selecionar todos
    this.triggerDianaPredictions();

    this.config.isLoadingSelectAll = false;
  },

  /**
   * Limpar seleção
   */
  handleClearSelection: function () {
    const allCheckboxes = document.querySelectorAll('input[name="item[]"]');
    allCheckboxes.forEach((checkbox) => {
      checkbox.checked = false;
    });

    this.config.selectedItems = [];
    this.config.hasSelectedAll = false;
    this.updateDisplay();
    this.updateMasterCheckboxState();
  },

  /**
   * Atribuir itens selecionados
   */
  handleAtribuir: function () {
    if (this.config.selectedItems.length === 0) {
      return;
    }

    // Usar a funcionalidade existente do botão "Atribuir"
    const atribuirBtn = document.querySelector(".btn.btn-primary.actions");
    if (atribuirBtn) {
      atribuirBtn.click();
    }
  },

  /**
   * Disparar evento para o componente Diana quando selecionar todos os itens
   */
  triggerDianaPredictions: function () {
    // Verificar se o componente Diana está disponível
    if (
      typeof window.Vue !== "undefined" &&
      window.Vue.config &&
      window.Vue.config.devtools !== undefined
    ) {
      // Tentar encontrar a instância do componente Diana
      const dianaApp = document.getElementById("atribuir-diana-app");
      if (dianaApp && dianaApp.__vue__) {
        // Chamar o método handleMultipleCheckboxes do componente Diana
        const dianaComponent = dianaApp.__vue__.$children.find(
          (child) =>
            child.$options.name === "Predictions" ||
            typeof child.handleMultipleCheckboxes === "function"
        );

        if (
          dianaComponent &&
          typeof dianaComponent.handleMultipleCheckboxes === "function"
        ) {
          // Aguardar um pouco para garantir que os checkboxes foram marcados
          setTimeout(() => {
            dianaComponent.handleMultipleCheckboxes();
          }, 100);
        }
      }
    }

    // Método alternativo: disparar evento customizado que o componente Diana pode escutar
    const event = new CustomEvent("selectAllItems", {
      detail: {
        selectedItems: this.config.selectedItems,
        timestamp: Date.now(),
      },
    });
    document.dispatchEvent(event);

    // Método adicional: simular clique no primeiro item selecionado para disparar as predições
    setTimeout(() => {
      const firstSelectedCheckbox = document.querySelector(
        'input[name="item[]"]:checked'
      );
      if (firstSelectedCheckbox) {
        // Disparar evento de change para garantir que o componente Diana detecte a seleção
        const changeEvent = new Event("change", { bubbles: true });
        firstSelectedCheckbox.dispatchEvent(changeEvent);

        // Também disparar evento de click para compatibilidade
        const clickEvent = new Event("click", { bubbles: true });
        firstSelectedCheckbox.dispatchEvent(clickEvent);
      }
    }, 200);
  },
};
