// JS para exibir badges dos filtros selecionados e contador no ícone do modal
$(function () {
  /**
   * Gera o HTML para um único badge de filtro.
   * @param {string} filterName - O nome/chave do filtro para o botão de remoção.
   * @param {string} textoCompleto - O texto a ser exibido no tooltip.
   * @param {string} textoCurto - O texto truncado a ser exibido no badge.
   * @returns {string} O HTML do badge.
   */
  function criarBadgeHTML(filterName, textoCompleto, textoCurto) {
    // Usando template literals (`) para um código mais limpo e seguro.
    const textoTooltip = textoCompleto.replace(/"/g, "&quot;");
    return `
      <span 
        class="badge badge-primary mr-1 d-flex align-items-center filter-badge"
        data-tooltip="${textoTooltip}" 
        style="max-width: 220px;"
      >
        <span class="badge-text" style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
          ${textoCurto}
        </span>
        <span 
          class="badge badge-light badge-remove ml-1" 
          data-name="${filterName}" 
          style="cursor: pointer;"
        >
          &times;
        </span>
      </span>
    `;
  }

  // Função para extrair filtros selecionados do formulário
  function getFiltrosSelecionados() {
    let filtros = [];
    $("#filterForm")
      .find("input, select")
      .each(function () {
        let $el = $(this);
        let name = $el.attr("name");
        let type = $el.attr("type");
        let label = $el.closest(".form-group").find("label").text();
        let value = $el.val();
        if (type === "checkbox") {
          if ($el.is(":checked")) {
            filtros.push({ name: name, label: label, value: "Sim" });
          }
        } else if ($el.is("select")) {
          let selected = $el
            .find("option:selected")
            .map(function () {
              return $(this).text();
            })
            .get();
          if (
            selected.length &&
            selected[0] !== "" &&
            selected[0] !== "Selecione..."
          ) {
            filtros.push({
              name: name,
              label: label,
              value: selected.join(", "),
            });
          }
        } else if (type === "date" || type === "text") {
          if (value) {
            filtros.push({ name: name, label: label, value: value });
          }
        }
      });
    return filtros;
  }

  // Renderiza os badges dos filtros selecionados
  function renderBadges() {
    let filtros = getFiltrosSelecionados();
    let $container = $("#active-filters");
    $container.empty();
    filtros.forEach(function (filtro) {
      const texto = filtro.label + ": " + filtro.value;
      const textoCurto =
        texto.length > 32 ? texto.substring(0, 29) + "..." : texto;

      const badgeHTML = criarBadgeHTML(filtro.name, texto, textoCurto);
      $container.append(badgeHTML);
    });

    // Atualiza contador no ícone do modal
    let $countBadge = $("#filter-count-badge");
    if (filtros.length > 0) {
      $countBadge.text(filtros.length).show();
    } else {
      $countBadge.hide();
    }
  }

  // Função utilitária para validar se um filtro tem valor válido
  function isValidFilterValue(value) {
    if (Array.isArray(value)) {
      // Para arrays, verificar se há valores não vazios
      const nonEmptyValues = value.filter(
        (v) => v && v !== "" && v !== "-1" && v !== null && v !== undefined
      );
      return nonEmptyValues.length > 0;
    } else {
      // Para valores simples, verificar se não são vazios/nulos
      return (
        value &&
        value !== "" &&
        value !== null &&
        value !== false &&
        value !== "0" &&
        value !== undefined &&
        !Array.isArray(value) // Garantir que não é array vazio
      );
    }
  }

  // Cache para labels de opções, para evitar buscas repetidas no DOM
  var optionLabelsCache = {};

  /**
   * Obtém o label de uma opção de um select a partir de seu valor.
   * Também trata valores especiais que não estão em selects.
   * @param {string} filterName - O nome do filtro (e do select).
   * @param {string|number} value - O valor da opção.
   * @returns {string} O texto (label) da opção ou o próprio valor como fallback.
   */
  function getOptionLabel(filterName, value) {
    // Tratar valores especiais que não estarão em selects
    if (filterName === 'atribuido_para') {
        if (value === 'sem_responsavel') return 'Sem responsável';
        if (value == window.sess_user_id) return 'Meu usuário';
    }
    if (filterName === 'triagem_diana_falha' && value) {
        return 'Sim';
    }

    const cacheKey = `${filterName}-${value}`;
    if (optionLabelsCache[cacheKey]) {
      return optionLabelsCache[cacheKey];
    }

    const fieldSelector = `#filterModal [name="${filterName}"], #filterModal [name="${filterName}[]"]`;
    const $select = $(fieldSelector);

    if ($select.is('select')) {
      const $option = $select.find('option').filter(function() { return this.value == value; });
      
      if ($option.length) {
        const label = $option.first().text();
        // Não armazenar em cache o texto de carregamento
        if (label !== 'Carregando...') {
            optionLabelsCache[cacheKey] = label;
        }
        return label;
      } else {
        // Se a opção não for encontrada, verificar se é um select dinâmico que ainda não carregou.
        if ($select.data('ajax-url') && !$select.data('loaded')) {
            return 'Carregando...';
        }
      }
    }
    
    // Fallback para campos estáticos ou se a opção realmente não for encontrada após o carregamento.
    return value;
  }

  // Função para renderizar badges baseado nos filtros salvos do backend
  function renderBadgesFromSavedFilters() {
    if (
      typeof FilterModalIntegration === "undefined" ||
      !FilterModalIntegration.getSavedFilters
    ) {
      return false;
    }

    const savedFilters = FilterModalIntegration.getSavedFilters();
    const $container = $("#active-filters");

    let hasValidFilters = false;
    Object.keys(savedFilters).forEach(function (filterName) {
      const value = savedFilters[filterName];
      if (isValidFilterValue(value)) {
        hasValidFilters = true;
      }
    });

    if (!hasValidFilters) {
      $container.empty();
      $("#filter-count-badge").hide();
      return false;
    }

    $container.empty();
    let totalFilters = 0;

    const filterLabels = {
      pacotes_eventos: "Pacotes / Eventos",
      sistema_origem: "Sistema de origem",
      owner: "Owner",
      prioridade: "Prioridade",
      atribuido_para: "Atribuídos para",
      status_classificacao_fiscal: "Status de classificação fiscal",
      pre_agrupamento: "Pré-agrupamento",
      triagem_diana_falha: "Falha na triagem",
      novo_material: "Novo material",
      estabelecimento: "Estabelecimento",
      importado: "Importado",
      farol_sla: "Farol SLA",
      data_criacao_from: "Data de criação (de)",
      data_criacao_to: "Data de criação (até)",
      data_modificacao_from: "Data de modificação (de)",
      data_modificacao_to: "Data de modificação (até)",
      data_importado_from: "Data que tornou-se importado (de)",
      data_importado_to: "Data que tornou-se importado (até)",
    };

    Object.keys(savedFilters).forEach(function (filterName) {
      const value = savedFilters[filterName];
      const label = filterLabels[filterName];

      if (!label || !isValidFilterValue(value)) return;

      let displayValue = "";
      if (Array.isArray(value)) {
        const nonEmptyValues = value.filter(
          (v) => v && v !== "" && v !== "-1" && v !== null && v !== undefined
        );
        displayValue = nonEmptyValues.map(val => getOptionLabel(filterName, val)).join(", ");
      } else {
        displayValue = getOptionLabel(filterName, value);
      }

      if (!displayValue) return;

      const texto = label + ": " + displayValue;
      const textoCurto =
        texto.length > 32 ? texto.substring(0, 29) + "..." : texto;

      const badgeHTML = criarBadgeHTML(filterName, texto, textoCurto);
      $container.append(badgeHTML);

      totalFilters++;
    });

    const $countBadge = $("#filter-count-badge");
    if (totalFilters > 0) {
      $countBadge.text(totalFilters).show();
    } else {
      $countBadge.hide();
    }

    return totalFilters > 0;
  }

  function preloadOptionLabels() {
    const $selects = $("#filterModal select");
    $selects.each(function() {
        const $select = $(this);
        const filterNameAttr = $select.attr('name');
        if (!filterNameAttr) return;

        const filterName = filterNameAttr.replace('[]', '');
        $select.find("option").each(function () {
            const $option = $(this);
            const value = $option.val();
            const text = $option.text();
            if (value && text && value !== "" && value !== "-1") {
                const cacheKey = `${filterName}-${value}`;
                optionLabelsCache[cacheKey] = text;
            }
        });
    });
  }

  // --- EVENT LISTENERS ---

  $("#filterForm").on("change.filter-badges", "input, select", renderBadges);

  $("#filterModal").on("shown.bs.modal", function () {
    setTimeout(preloadOptionLabels, 500);
  });

  $(document).on('filterOptionsLoaded.badges', function(event, selectId) {
    if (window.badgeRenderTimeout) {
        clearTimeout(window.badgeRenderTimeout);
    }
    window.badgeRenderTimeout = setTimeout(function() {
        renderBadgesFromSavedFilters();
    }, 150);
  });

  $("#btn-filtrar-modal").on("click.filter-badges", function (e) {
    e.preventDefault();
    if (typeof FilterModal !== "undefined" && FilterModal.submitFilters) {
      FilterModal.submitFilters();
    } else {
      $("#filterModal").modal("hide");
      renderBadges();
    }
  });

  $("#btn-clear-filters").on("click.filter-badges", function () {
    $("#filterForm")[0].reset();
    $("#filterForm")
      .find("select.selectpicker")
      .selectpicker("deselectAll")
      .selectpicker("refresh");
    $("#filterForm").find("input[type='date'], input[type='text']").val("");
    $("#filterForm").find("input[type='checkbox']").prop("checked", false);

    if (
      typeof FilterModalIntegration !== "undefined" &&
      FilterModalIntegration.clearSavedFilters
    ) {
      FilterModalIntegration.clearSavedFilters();
    }
    if (typeof FilterModal !== "undefined" && FilterModal.clearOptionsCache) {
      FilterModal.clearOptionsCache();
    }
    renderBadges();
    if ($("#loading-overlay").length) {
      $("#loading-overlay").show();
    }
    window.location.href = base_url + "atribuir_grupo?reset_filters=1";
  });

  $("#active-filters").on("click.filter-badges", ".badge-remove", function (e) {
    e.preventDefault();
    const filterName = $(this).data("name");

    const $form = $("#filterForm");
    if ($form.length) {
      let $fields = $form.find('[name="' + filterName + '"]');
      if ($fields.length === 0) {
        $fields = $form.find('[name="' + filterName + '[]"]');
      }

      $fields.each(function () {
        const $el = $(this);
        if ($el.is("select")) {
          $el.val([]);
          if ($el.hasClass("selectpicker")) $el.selectpicker("refresh");
        } else if ($el.is(":checkbox")) {
          $el.prop("checked", false);
        } else if ($el.is("input") || $el.is("textarea")) {
          $el.val("");
        }
      });
    }

    if (
      typeof FilterModalIntegration !== "undefined" &&
      FilterModalIntegration.getSavedFilters &&
      FilterModalIntegration.updateWindowVariables
    ) {
      const current = FilterModalIntegration.getSavedFilters();
      if (Object.prototype.hasOwnProperty.call(current, filterName)) {
        let clearedValue = "";
        if (Array.isArray(current[filterName])) clearedValue = [];
        if (filterName === "triagem_diana_falha") clearedValue = null;
        current[filterName] = clearedValue;
        FilterModalIntegration.updateWindowVariables({
          [filterName]: clearedValue,
        });
      }
    } else if (
      typeof FilterModalIntegration !== "undefined" &&
      FilterModalIntegration.getSavedFilters
    ) {
      const current = FilterModalIntegration.getSavedFilters();
      if (Object.prototype.hasOwnProperty.call(current, filterName)) {
        current[filterName] = Array.isArray(current[filterName]) ? [] : "";
        if (filterName === "triagem_diana_falha") current[filterName] = null;
      }
    }

    const $outerBadge = $(this).closest(".filter-badge");
    $outerBadge.remove();

    const remaining = $("#active-filters .filter-badge").length;
    const $countBadge = $("#filter-count-badge");
    if (remaining > 0) {
      $countBadge.text(remaining).show();
    } else {
      $countBadge.hide();
    }
  });

  // --- INICIALIZAÇÃO ---

  setTimeout(function () {
    renderBadgesFromSavedFilters();
  }, 200);
});
