<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class importar_paises extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->library('breadcrumbs');

        if (!is_logged()) {
            redirect('/login');
        }

        // if (!customer_can("importar_paises", false, false)) {
        //     show_permission();
        // }
    }

    public function index()
    {
        $this->load->library("Item/Status");
        $this->load->model('cad_item_model');
        $data = array();

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $id_empresa        = sess_user_company();

        if ($this->input->is_set("submit")) {
            $upload_path = config_item('upload_tmp_path');

            $this->load->model(
                array(
                    'empresa_model',
                    'item_pais_model',
                    'empresa_pais_model',
                    'item_model',
                    'cad_item_model'
                )
            );

            $config['upload_path'] = $upload_path;
            $config['allowed_types'] = 'zip|xlsx';
            $config['max_size'] = 2147483648;

            $this->load->library('unzip');
            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('arquivo')) {
                $data['errors'] = array('error' => $this->upload->display_errors('<p>', '</p>'));
            } else {
                $log_adicional = '';

                $log_atualizados = array();
                $log_com_erro = array();
                $log_debug = array(); // Array separado para logs de debug


                $paises = $this->empresa_pais_model->get_entries_by_empresa(sess_user_company());
                // Log para verificar países configurados
                $log_debug[] = "INFO: Encontrados " . count($paises) . " países configurados para a empresa " . sess_user_company();
                foreach ($paises as $pais_debug) {
                    $log_debug[] = "INFO: País configurado - Sigla: '{$pais_debug->sigla}', Nome: '{$pais_debug->nome}', Regex: '{$pais_debug->regex}', MaxLength: {$pais_debug->maxlength}";
                }

                $upload_data = $this->upload->data();

                $file_ext = strtolower($upload_data['file_ext']);

                if ($file_ext == '.xlsx') {

                    $msg = "Data: " . date('d/m/Y \à\s H:i:s') . " upload - INICIO " . " Empresa: " . sess_user_company() . " Usuario: " . sess_user_id() . " Nome do arquivo: " . $upload_data['file_name'] . " local: " . $upload_data['full_path'];


                    include APPPATH . 'libraries/xlsxreader.php';

                    $xlsx = new XLSXReader($upload_data['full_path']);
                    $sheetNames = $xlsx->getSheetNames();
                    $sheetActive = current($sheetNames);
                    $sheet = $xlsx->getSheet($sheetActive);

                    $i = 0;

                    $this->load->helper('text');

                    $validar_tag = array();
                    $count_register = 0;

                    $dbdata = array();
                    $k = 0;
                    foreach ($sheet->getData() as $row) {

                        if ($i > 0) {
                            // Log da linha sendo processada
                            $log_debug[] = "PROCESSANDO: Linha " . ($i + 1) . " - Dados: " . implode(' | ', array_slice($row, 0, 12));


                            $part_number            = trim($row[0]);
                            $estabelecimento        = trim($row[1]);
                            $sigla                  = trim($row[3]);
                            $codigo_classificacao   = trim($row[7]);
                            $descricao_curta        = trim($row[8]);
                            $descricao_completa     = trim($row[9]);
                            $li                     = trim($row[10]);
                            $informacao_adicional   = trim($row[11]);

                            // Log dos dados extraídos
                            $log_debug[] = "DADOS: Linha " . ($i + 1) . " - PN: '$part_number', Est: '$estabelecimento', Sigla: '$sigla', Código: '$codigo_classificacao'";


                            $chek_item = $this->item_model->get_entry($part_number, $id_empresa, $estabelecimento);

                            $chek_cad_item = $this->cad_item_model->get_entry_by_pn($part_number, $id_empresa, $estabelecimento, true);

                            // DEBUG: Log para verificar se item existe
                            if (empty($chek_cad_item)) {
                                $log_com_erro[] = "Item com part number '$part_number' não encontrado na tabela cad_item para empresa $id_empresa e estabelecimento '$estabelecimento'";
                                continue;
                            }

                            if (empty($chek_cad_item->ncm_proposto)) {
                                $log_com_erro[] = "Item com part number '$part_number' não possui NCM proposto";
                                continue;
                            }

                            if (!empty($chek_item)) {

                                // DEBUG: Log para confirmar que item foi encontrado
                                // $log_com_erro[] = "DEBUG: Item '$part_number' encontrado na tabela item";

                                $chek_pais = 0;
                                foreach ($paises as $p => $pais) {
                                    if ($pais->sigla == $sigla) {

                                        $id_pais                = $pais->id_pais;
                                        $regex_pais             = $pais->regex;
                                        $descricao_padrao_pais  = $pais->descricao;
                                        $maxlength              = $pais->maxlength;
                                        $chek_pais++;
                                    }
                                }

                                // Log para verificar se país foi encontrado
                                if ($chek_pais == 0) {
                                    $log_com_erro[] = "País com sigla '$sigla' não encontrado ou não configurado para a empresa $id_empresa";
                                    $log_debug[] = "❌ ERRO: Sigla '$sigla' não encontrada. Siglas disponíveis: " . implode(', ', array_column($paises, 'sigla'));
                                }

                                if ($chek_pais > 0) {
                                    if ($codigo_classificacao != "") {

                                        $check_codigo_class = preg_match_all($regex_pais, $codigo_classificacao) ? true : false;

                                        // Log para verificar validação do código
                                        if (!$check_codigo_class && strlen($codigo_classificacao) != $maxlength) {
                                            $log_debug[] = "VALIDAÇÃO: Código '$codigo_classificacao' para país '$sigla' não passou na validação. Regex: '$regex_pais', Tamanho esperado: $maxlength, Tamanho atual: " . strlen($codigo_classificacao);
                                        }

                                        //if( substr($chek_cad_item->ncm_proposto, 0, 6) == substr($codigo_classificacao, 0, 6)){

                                        if ($check_codigo_class || strlen($codigo_classificacao) == $maxlength) {
                                            $dbdata[$k] = [
                                                'part_number'           => $part_number,
                                                'estabelecimento'       => $estabelecimento,
                                                'id_empresa'            => $id_empresa,
                                                'id_pais'               => $id_pais,
                                                'codigo_classificacao'  => $codigo_classificacao,
                                                'descricao_curta'       => $descricao_curta,
                                                'descricao_completa'    => $descricao_completa,
                                                'li'                    => $li,
                                                'informacao_adicional'  => $informacao_adicional
                                            ];
                                            $log_debug[] = "✅ APROVADO: Item '$part_number' para país '$sigla' validado com sucesso";
                                            $count_register++;
                                            $k++;
                                        } else {
                                            $log_com_erro[] = "O código de classificação '" . $codigo_classificacao . "' não é válido, deve seguir o padrão: " . $descricao_padrao_pais . "";
                                        }
                                        // } else {
                                        //     $log_com_erro[] = "O código de classificação '".$codigo_classificacao."' não combina com NCM Proposto, os primeiros 6 digitos devem seguir o padrão: ".substr($chek_cad_item->ncm_proposto, 0, 6)."";
                                        // }
                                    } else {

                                        //so o codigo de classificaçao for igual a  verificar se existe algum partnumber com este pais na tabela item pais se horver configura para excluir
                                    }
                                }
                            } else {
                                $log_com_erro[] = "O Item com o part number: '" . $part_number . "' referente ao país: " . $sigla . " não existe na tabela item ou não está relacionado com a empresa $id_empresa e estabelecimento '$estabelecimento'.";
                            }
                        } else {
                            $part_number            = trim($row[0]);
                            $estabelecimento        = trim($row[1]);
                            $sigla                  = trim($row[3]);
                            $codigo_classificacao   = trim($row[7]);
                            $descricao_curta        = trim($row[8]);
                            $descricao_completa     = trim($row[9]);
                            $li                     = trim($row[10]);
                            $informacao_adicional   = trim($row[11]);

                            if ($part_number != 'Part Number Brasil' &&  $estabelecimento != 'Estabelecimento' &&  $sigla != 'Sigla do País' && $codigo_classificacao != 'Código Classificação' && $descricao_curta != 'Descrição Curta' && $descricao_completa != 'Descrição Completa' && $li  != 'L.I.' &&  $informacao_adicional != 'Informação Adicional') {
                                $log_com_erro[] = "O arquivo de importação não é compatível: colunas não correspondem com o padrão definido.";
                            }
                        }

                        $i++;
                    }

                    // Log final do processamento
                    $log_debug[] = "RESUMO: Processamento concluído - Total de linhas: $i, Itens aprovados: " . count($dbdata) . ", Erros: " . count($log_com_erro);

                    if (empty($log_com_erro)) {

                        $log_inseridos = 0;
                        $log_atualizados_count = 0;

                        foreach ($dbdata as $item) {
                            // Verifica se já existe registro para este item + país específico
                            $registro_existente = $this->item_pais_model->get_entries_by_item(
                                $item['part_number'],
                                $item['id_empresa'],
                                $item['estabelecimento'],
                                $item['id_pais']
                            );

                            if (!empty($registro_existente)) {
                                // ATUALIZA registro existente
                                $registro_id = $registro_existente[0]->id_item_pais;
                                $update_where = ['id_item_pais' => $registro_id];
                                $this->item_pais_model->save($item, $update_where);
                                $log_atualizados[] = 'Part number [<b>' . $item['part_number'] . '</b>] país [<b>' . $item['id_pais'] . '</b>] ATUALIZADO';
                                $log_debug[] = "🔄 ATUALIZADO: PN '{$item['part_number']}' para país ID {$item['id_pais']} (ID: $registro_id)";
                                $log_atualizados_count++;
                            } else {
                                // INSERE novo registro
                                $this->item_pais_model->save($item, []);
                                $log_atualizados[] = 'Part number [<b>' . $item['part_number'] . '</b>] país [<b>' . $item['id_pais'] . '</b>] INSERIDO';
                                $log_debug[] = "➕ INSERIDO: PN '{$item['part_number']}' para país ID {$item['id_pais']}";
                                $log_inseridos++;
                            }
                        }

                        $log_debug[] = "📊 RESULTADO: $log_inseridos novos registros inseridos, $log_atualizados_count registros atualizados";
                    }

                    $msg = '';

                    if (!empty($log_com_erro)) {
                        $type = 'error';

                        $msg .= '<h4>Erro</h4>';
                        $msg .= 'Arquivo XLSX <b>' . $upload_data['orig_name'] . '</b> recebido, mas ocorreram alguns erros.';
                    } else {
                        $type = 'success';

                        $msg .= '<h4>Sucesso</h4>';
                        $msg .= 'Arquivo XLSX <b>' . $upload_data['orig_name'] . '</b> recebido e processado com sucesso!';
                    }

                    $msg .= '<div id="log-accordion">';
                    $msg .= '<ul>';

                    // Itens atualizados
                    $msg .= '<li>';
                    if (!empty($log_atualizados)) {
                        $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#atualizados">';
                        $msg .= 'Itens atualizados: <b>' . count($log_atualizados) . '</b></a>';
                        $msg .= '<div id="atualizados" class="collapse">';
                        $msg .= format_log_as_html($log_atualizados);
                        $msg .= '</div>';
                    } else {
                        $msg .= 'Itens atualizados: <b>0</b>';
                    }
                    $msg .= '</li>';


                    // Itens com erro
                    $msg .= '<li>';
                    if (!empty($log_com_erro)) {
                        $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#com-erro">';
                        $msg .= 'Itens com erro: <b>' . count($log_com_erro) . '</b></a>';
                        $msg .= '<div id="com-erro" class="collapse">';
                        $msg .= format_log_as_html($log_com_erro);
                        $msg .= '</div>';
                    } else {
                        $msg .= 'Itens com erro: <b>0</b>';
                    }
                    $msg .= '</li>';

                    $msg .= '</ul>';
                    $msg .= '</div>';

                    // Adicionar logs informativos se existirem
                    if (!empty($log_debug)) {
                        $msg .= '<div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">';
                        $msg .= '<h5>📋 Detalhes do Processamento:</h5>';
                        $msg .= '<div style="max-height: 300px; overflow-y: auto; font-size: 12px;">';
                        foreach ($log_debug as $debug_msg) {
                            $msg .= '<div style="margin-bottom: 2px;">' . $debug_msg . '</div>';
                        }
                        $msg .= '</div>';
                        $msg .= '</div>';
                    }



                    //$message_on_render = $this->message_config($msg, $type);

                    //$this->message_on_render($message_on_render, NULL, TRUE);

                    if ($type == 'error') {
                        $data['errors'] = $msg;
                    } else {
                        $data['success'] = $msg;
                    }
                }
                if (isset($file_data)) {
                    unlink($upload_path . $file_data['file_name']);
                }
            }
        }

        $this->title = "Importar Itens";
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Importar Países', '/importar_paises/');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css'
        ));

        $this->render('importar_paises', $data);
    }

    public function exportar()
    {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        $this->load->model(array('item_model', "cad_item_model", "ex_tarifario_model", 'empresa_model', 'item_pais_model'));

        if (!empty($this->input->post('reset_filters'))) {
            $this->item_model->clear_states();
        }
        $data = array();

        $status = $this->input->post('status') ? $this->input->post('status') : $this->input->get('status');
        $search = $this->input->post('search') ? $this->input->post('search') : $this->input->get('search');
        $evento = $this->input->post('evento') ? $this->input->post('evento') : $this->input->get('evento');
        $statusExportacao = $this->input->post('statusExportacao') ? $this->input->post('statusExportacao') : $this->input->get('statusExportacao');
        $owner_filter = $this->input->post('owner') ? $this->input->post('owner') : $this->input->get('owner');


        $this->item_model->set_state('filter.id_empresa', sess_user_company());

        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $data['lista_status'] = $this->item_model->get_status();

        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasDescricaoGlobal = in_array('descricao_global', $data['campos_adicionais']);
        $hasOwner = in_array('owner', $data['campos_adicionais']);

        $data['hasDescricaoGlobal'] = $hasDescricaoGlobal;
        $data['hasOwner'] = $hasOwner;
        $owner_user = $this->item_model->get_user_owner_code(sess_user_id());

        $owners = $this->empresa_model->get_owners_by_empresa($empresa);

        $data['owners'] = $owners;

        // if (!is_null($owner_filter) && !in_array(-1, $owner_filter)) {
        if (is_array($owner_filter) && !in_array(-1, $owner_filter)) {
            $this->item_model->set_state('filter.owner', $owner_filter);
        } else {
            $this->item_model->set_state('filter.owner', null);
        }

        $additional_params = '';

        if (is_array($owner_filter)) {
            foreach ($owner_filter as $owner_value) {
                $additional_params .= '&owner[]=' . urlencode($owner_value);
            }
        }

        // Check status!
        $valid_status = FALSE;
        foreach ($data['lista_status'] as $lista_status_r) {
            if (empty($lista_status_r))
                continue;
            if (is_array($status)) {
                if (in_array($lista_status_r['status'], $status)) {
                    $valid_status = TRUE;
                    break;
                }
            } else {
                if ($lista_status_r['status'] == $status) {
                    $valid_status = TRUE;
                    break;
                }
            }
        }

        //$search = addslashes($search);

        $this->item_model->set_state('filter.ignore_like', TRUE);

        (!empty($search)) ? $this->item_model->set_state("filter.search", $search) : NULL;
        (!empty($status) && $valid_status) ? $this->item_model->set_status($status) : NULL;

        $data_ini_str = $this->input->post('data_ini') ? $this->input->post('data_ini') : $this->input->get('data_ini');
        $data_fim_str = $this->input->post('data_fim') ? $this->input->post('data_fim') : $this->input->get('data_fim');

        if (!empty($data_ini_str)) {
            $data_ini = str_replace('/', '-', $data_ini_str);
            $data_ini = date('Y-m-d', strtotime($data_ini));

            $this->item_model->set_state('filter.data_ini', $data_ini);
        }

        if (!empty($data_fim_str)) {
            $data_fim = str_replace('/', '-', $data_fim_str);
            $data_fim = date('Y-m-d', strtotime($data_fim));

            $this->item_model->set_state('filter.data_fim', $data_fim);
        }

        $this->item_model->set_state('filter.evento', $evento);
        $this->item_model->set_state('filter.statusExportacao', $statusExportacao);

        $empresa_prioridade_filter = [];
        $empresa_prioridade_filter = $this->input->post('prioridade') ? $this->input->post('prioridade') : $this->input->get('prioridade');

        if (is_array($empresa_prioridade_filter) && !in_array(-1, $empresa_prioridade_filter)) {
            $this->item_model->set_state('filter.prioridade', $empresa_prioridade_filter);
        } else {
            $this->item_model->set_state('filter.prioridade', null);
        }

        $id_empresa = sess_user_company();

        $itens = $this->item_model->get_entries_export_mestre_itens();

        $empresa = $this->empresa_model->get_entry($id_empresa);

        $logs = $this->item_pais_model->get_entries_for_xls($id_empresa);

        $headerRow = array(
            array(
                'label' => 'PART NUMBER BRASIL',
                'field' => 'part_number',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'ESTABELECIMENTO',
                'field' => 'estabelecimento',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DESCRIÇÃO',
                'field' => 'descricao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'SIGLA DO PAÍS',
                'field' => 'sigla',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'CÓDIGO DO PAÍS',
                'field' => 'codigo',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'NOME DO PAÍS',
                'field' => 'nome',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'NCM PROPOSTO',
                'field' => 'ncm_proposto',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'CÓDIGO CLASSIFICAÇÃO',
                'field' => 'codigo_classificacao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DESCRIÇÃO CURTA',
                'field' => 'descricao_curta',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DESCRIÇÃO COMPLETA',
                'field' => 'descricao_completa',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'L.I.',
                'field' => 'li',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'INFORMAÇÃO ADICIONAL',
                'field' => 'informacao_adicional',
                'col_format' => 'texto',
                'col_width' => 30
            )

        );



        $cabecalho = $this->extract_fields_from_cols($headerRow);

        $config = array(
            'filename'      => "classificacao_paises",
            'titulo'        => "classificacao_paises",
            'nome_planilha' => "classificacao_paises",
            'colunas'       => $headerRow,
            'cabecalho'     => $cabecalho,
            'filter_suffix' => "HOM_"
        );

        require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';
        $relatorio = new XlsxGenerator($config['filename']);
        $writer = $relatorio->init($config['filename']);

        $relatorio->filename = $config['filename'];

        // Estrutura XLSX
        $widths = $relatorio->getWidth($config);
        $fields = array();

        $writer->addImage(FCPATH . 'assets/img/header/logo.png', 1, array('widths' => $widths));

        if (isset($config['colunas'])) {
            foreach ($config['colunas'] as $coluna) {
                if (isset($coluna['label']))
                    $fields[$coluna['label']] = isset($coluna['col_format']) ? $coluna['col_format'] : 'string';
            }
        }

        $headerStyle = array(
            'font' => 'Arial',
            'font-size' => 12,
            'font-style' => 'bold',
            'color' => '#000000',
            'fill' => '#F9FF00',
            'halign' => 'center',
            'valign' => 'center',
            'border' => 'bottom',
            'wrap_text' => 'true',
            'widths' => $widths
        );


        $writer->writeSheetHeader($config['nome_planilha'], $fields, $headerStyle);

        if (isset($config['colunas']) &&  (count($config['colunas']) <= 24) && (count($config['colunas']) >= 10)) {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
        } else if (isset($config['colunas']) && count($config['colunas']) <= 24) {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, 24);
        } else {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
        }

        $defaultStyle = array(
            'font' => 'Arial',
            'font-size' => 11
        );

        $itemRows = array();

        $logsbuff = $logs->result();

        $k = 0;
        $key = 0;

        while ($item = $itens->unbuffered_row()) {


            $descricao_item = $item->descricao;

            if (!empty($descricao_item)) {
                $descricao_item = $item->descricao_mercado_local;
            }

            $tem_paises = 0;
            foreach ($logsbuff as $log) {

                if ($item->part_number == $log->part_number) {

                    $key++;
                    $k = $key + 1;

                    $itemRows[$k] = array(
                        $log->part_number,
                        $log->estabelecimento,
                        $descricao_item,
                        $log->sigla,
                        $log->codigo,
                        $log->nome,
                        $item->ncm_proposto,
                        $log->codigo_classificacao,
                        $log->descricao_curta,
                        $log->descricao_completa,
                        $log->li,
                        $log->informacao_adicional
                    );
                    $tem_paises++;
                }
            }
            if ($tem_paises == 0) {
                $key++;
                $k = $key + 1;
                $itemRows[$k] = array(
                    $item->part_number,
                    $item->estabelecimento,
                    $descricao_item,
                    ' - ',
                    ' - ',
                    ' - ',
                    $item->ncm_proposto,
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - '
                );
            }
        }


        $k = 2;
        foreach ($itemRows as $item) {
            $row = array();
            foreach ($headerRow as $i => $value) {
                $row += array($headerRow[$i]['field'] => $itemRows[$k][$i]);
            }
            $k++;
            $writer->writeSheetRow($config['nome_planilha'], $row, $defaultStyle);
        }

        return $relatorio->download();
    }

    private function extract_fields_from_cols($cols, $line_break = ' ', $label_key_override = 'label_exportar')
    {
        if (empty($cols)) {
            return array();
        }

        $fields = array();

        foreach ($cols as $col) {
            $label_key = 'label';

            if (!empty($label_key_override) && array_key_exists($label_key_override, $col)) {
                $label_key = $label_key_override;
            }

            if (!empty($line_break)) {
                $col[$label_key] = str_replace('<br>', $line_break, $col[$label_key]);
            }

            $fields[$col['field']] = $col[$label_key];
        }

        return $fields;
    }
    public function exportar_modelo()
    {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        $this->load->model(array('item_model', "cad_item_model", "ex_tarifario_model", 'empresa_model', 'item_pais_model', 'empresa_pais_model'));
        if (!empty($this->input->post('reset_filters'))) {
            $this->item_model->clear_states();
        }
        $data = array();

        $status = $this->input->post('status') ? $this->input->post('status') : $this->input->get('status');
        $search = $this->input->post('search') ? $this->input->post('search') : $this->input->get('search');
        $evento = $this->input->post('evento') ? $this->input->post('evento') : $this->input->get('evento');
        $statusExportacao = $this->input->post('statusExportacao') ? $this->input->post('statusExportacao') : $this->input->get('statusExportacao');
        $owner_filter = $this->input->post('owner') ? $this->input->post('owner') : $this->input->get('owner');


        $this->item_model->set_state('filter.id_empresa', sess_user_company());

        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $data['lista_status'] = $this->item_model->get_status();

        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasDescricaoGlobal = in_array('descricao_global', $data['campos_adicionais']);
        $hasOwner = in_array('owner', $data['campos_adicionais']);

        $data['hasDescricaoGlobal'] = $hasDescricaoGlobal;
        $data['hasOwner'] = $hasOwner;
        $owner_user = $this->item_model->get_user_owner_code(sess_user_id());

        $owners = $this->empresa_model->get_owners_by_empresa($empresa);

        $data['owners'] = $owners;

        // if (!is_null($owner_filter) && !in_array(-1, $owner_filter)) {
        if (is_array($owner_filter) && !in_array(-1, $owner_filter)) {
            $this->item_model->set_state('filter.owner', $owner_filter);
        } else {
            $this->item_model->set_state('filter.owner', null);
        }

        $additional_params = '';

        if (is_array($owner_filter)) {
            foreach ($owner_filter as $owner_value) {
                $additional_params .= '&owner[]=' . urlencode($owner_value);
            }
        }

        // Check status!
        $valid_status = FALSE;
        foreach ($data['lista_status'] as $lista_status_r) {
            if (empty($lista_status_r))
                continue;
            if (is_array($status)) {
                if (in_array($lista_status_r['status'], $status)) {
                    $valid_status = TRUE;
                    break;
                }
            } else {
                if ($lista_status_r['status'] == $status) {
                    $valid_status = TRUE;
                    break;
                }
            }
        }

        //$search = addslashes($search);

        $this->item_model->set_state('filter.ignore_like', TRUE);

        (!empty($search)) ? $this->item_model->set_state("filter.search", $search) : NULL;
        (!empty($status) && $valid_status) ? $this->item_model->set_status($status) : NULL;

        $data_ini_str = $this->input->post('data_ini') ? $this->input->post('data_ini') : $this->input->get('data_ini');
        $data_fim_str = $this->input->post('data_fim') ? $this->input->post('data_fim') : $this->input->get('data_fim');

        if (!empty($data_ini_str)) {
            $data_ini = str_replace('/', '-', $data_ini_str);
            $data_ini = date('Y-m-d', strtotime($data_ini));

            $this->item_model->set_state('filter.data_ini', $data_ini);
        }

        if (!empty($data_fim_str)) {
            $data_fim = str_replace('/', '-', $data_fim_str);
            $data_fim = date('Y-m-d', strtotime($data_fim));

            $this->item_model->set_state('filter.data_fim', $data_fim);
        }

        $this->item_model->set_state('filter.evento', $evento);
        $this->item_model->set_state('filter.statusExportacao', $statusExportacao);

        $empresa_prioridade_filter = [];
        $empresa_prioridade_filter = $this->input->post('prioridade') ? $this->input->post('prioridade') : $this->input->get('prioridade');

        if (is_array($empresa_prioridade_filter) && !in_array(-1, $empresa_prioridade_filter)) {
            $this->item_model->set_state('filter.prioridade', $empresa_prioridade_filter);
        } else {
            $this->item_model->set_state('filter.prioridade', null);
        }

        $itens = $this->item_model->get_entries_export_mestre_itens(true);

        $logs = $this->item_pais_model->get_entries_for_xls(sess_user_company());

        $paises = $this->empresa_pais_model->get_entries_by_empresa(sess_user_company());

        $this->load->library('Excel');

        $this->excel->setActiveSheetIndex(0);
        $this->excel->getActiveSheet()->setTitle('modelo_importacao_paises');

        $this->excel->getActiveSheet()->setCellValue('A1', 'Part Number Brasil');
        $this->excel->getActiveSheet()->setCellValue('B1', 'Estabelecimento');
        $this->excel->getActiveSheet()->setCellValue('C1', 'Descrição');
        $this->excel->getActiveSheet()->setCellValue('D1', 'Sigla do País');
        $this->excel->getActiveSheet()->setCellValue('E1', 'Código do País');
        $this->excel->getActiveSheet()->setCellValue('F1', 'Nome do País');
        $this->excel->getActiveSheet()->setCellValue('G1', 'NCM Proposto');
        $this->excel->getActiveSheet()->setCellValue('H1', 'Código Classificação');
        $this->excel->getActiveSheet()->setCellValue('I1', 'Descrição Curta');
        $this->excel->getActiveSheet()->setCellValue('J1', 'Descrição Completa');
        $this->excel->getActiveSheet()->setCellValue('K1', 'L.I.');
        $this->excel->getActiveSheet()->setCellValue('L1', 'Informação Adicional');



        $this->excel->getActiveSheet()->getStyle('A1:L1')->getFont()->setBold(true);

        $this->excel->getActiveSheet()->getStyle('A1:L1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

        $this->excel->getActiveSheet()->getStyle('A1:L1')->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');

        foreach (range('A', 'L') as $columnID) {
            $this->excel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
            $this->excel->getActiveSheet()
                ->getStyle($columnID)
                ->getAlignment()
                ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
        }

        if (!empty($logs)) {
            $horizontal_left = array(
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                ),
            );
            -$i = count($logs) + 1;
            $this->excel->getActiveSheet()->getStyle('A1:A' . $i)->applyFromArray($horizontal_left);

            $logsbuff = $logs->result();
            $key = 0;


            while ($item = $itens->unbuffered_row()) {

                $descricao_item = $item->descricao;

                if ($descricao_item  == "") {
                    $descricao_item = !empty($item->descricao_mercado_local) ? $item->descricao_mercado_local : '';
                }

                foreach ($paises as $p => $pais) {
                    $tem_paises = 0;
                    foreach ($logsbuff as $log) {

                        if ($item->part_number == $log->part_number) {
                            if ($pais->sigla == $log->sigla) {
                                $key++;
                                $i = $key + 1;
                                $this->excel->getActiveSheet()->setCellValueExplicit('A' . $i, $log->part_number, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('B' . $i, $log->estabelecimento, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('C' . $i, $descricao_item, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('D' . $i, $log->sigla, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('E' . $i, $log->codigo, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('F' . $i, $log->nome, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('G' . $i, $item->ncm_proposto, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('H' . $i, $log->codigo_classificacao, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('I' . $i, $log->descricao_curta, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('J' . $i, $log->descricao_completa, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('K' . $i, $log->li, PHPExcel_Cell_DataType::TYPE_STRING);
                                $this->excel->getActiveSheet()->setCellValueExplicit('L' . $i, $log->informacao_adicional, PHPExcel_Cell_DataType::TYPE_STRING);

                                $tem_paises++;
                            }
                        }
                    }
                    if ($tem_paises == 0) {
                        $key++;
                        $i = $key + 1;
                        $estabelecimento = '';
                        if (isset($item->estabelecimento)) {
                            $estabelecimento = $item->estabelecimento;
                        }

                        $this->excel->getActiveSheet()->setCellValueExplicit('A' . $i, $item->part_number, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('B' . $i, $estabelecimento, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('C' . $i, $descricao_item, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('D' . $i, $pais->sigla, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('E' . $i, $pais->codigo, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('F' . $i, $pais->nome, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('G' . $i, $item->ncm_proposto, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('H' . $i, '', PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('I' . $i, '', PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('J' . $i, '', PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('K' . $i, '', PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('L' . $i, '', PHPExcel_Cell_DataType::TYPE_STRING);
                    }
                }
            }
        }

        $filename = 'modelo_importacao_paises.xlsx';

        // header('Content-Type: application/vnd.ms-excel');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
        $objWriter->save('php://output');
    }
}
