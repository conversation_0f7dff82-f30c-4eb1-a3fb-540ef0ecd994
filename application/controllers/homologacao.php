<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;


/**
 * Class Homologacao. Trata da homologação fiscal de itens
 * @property CI_Loader $load
 * @property My_Input $input
 * @property CI_Output $output
 * @property CI_Session $session
 * @property CI_Form_validation $form_validation
 * @property CI_Breadcrumbs $breadcrumbs
 * @property CI_Pagination $pagination
 * @property CI_DB_query_builder $db
 * @property CI_Config $config
 * Carregar models na inicialização
 * @property Cad_Item_Model $cad_item_model
 * @property Usuario_Model $usuario_model
 * @property Foto_Model $foto_model
 * @property Item_Model $item_model
 * @property Item_Log_Model $item_log_model
 * @property Motivo_Model $motivo_model
 * @property Owner_Model $owner_model
 * @property Usuario_Homolog_Bulk_Model $usuario_homolog_bulk_model
 * @property Empresa_Prioridades_Model $empresa_prioridades_model
 * @property Cad_Item_Wf_Atributo_Model $cad_item_wf_atributo_model
 * @property Cad_item_Attr_Model $cad_item_attr_model
 * @property Log_Wf_Atributos_Model $log_wf_atributos_model
 * @property Empresa_model $empresa_model
 * @property Empresa_Pais_Model $empresa_pais_model
 * @property Ctr_Resposta_Model $ctr_resposta_model
 * @property Lessin_Model $lessin_model
 * @property Item_Pais_Model $item_pais_model
 * @property Cad_Item_Nve_Model $cad_item_nve_model
 * @property Ex_Tarifario_Model $ex_tarifario_model
 * @property Comex_Model $comex_model
 * @property Ncm_Model $ncm_model
 * @property Produto_model $produto_model
 * @property Suframa_Model $suframa_model
 * @property Anexo_Model $anexo_model
 * @property Cad_Item_Homologacao_Model $cad_item_homologacao_model
 * @property Cest_Model $cest_model
 * @property Item_Cest_Model $item_cest_model
 * @property Ws_Log_Model $ws_log_model
 * @property Grupo_Tarifario_Model $grupo_tarifario_model
 * Carregar libraries na inicialização
 * @property Atributo $atributo
 * @property Status $status
 * @property Excel $excel
 * @property Email $email
 */
class Homologacao extends MY_Controller
{
    public $_attrs = [];
    public $title = '';
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('homologacao')) {
            show_permission();
        }

        $this->load->model(array(
            'cad_item_model',
            'usuario_model',
            'foto_model',
            'item_model',
            'motivo_model',
            'owner_model',
            'usuario_homolog_bulk_model',
            'empresa_prioridades_model'
        ));

        // enable profiler para debug
        // $this->output->enable_profiler(true);

        $this->load->library('breadcrumbs');
        $this->load->helper('formatador_helper');

        $this->cad_item_model->set_namespace('homologacao');
    }

    public function index()
    {
        $data = array();

        $per_page = $this->input->get('per_page');

        $this->title = "Homologação";

        $id_usuario = sess_user_id();
        $homologarAtributos = false;
        if (customer_has_role('homologar_atributos_workflow', $id_usuario) ) {
            $homologarAtributos = true;
        }

        $this->load->library("Item/Atributo");

        $this->load->model(array(
            'item_model',
            'empresa_model',
            'empresa_pais_model',
            'cad_item_wf_atributo_model'
        ));
        $data['filtered'] = [];

        $id_empresa = sess_user_company();
        $homologacoes = $this->empresa_model->get_homologacao_by_id_empresa($id_empresa);

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode('|', $empresa->campos_adicionais);
        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);

        $hasOwner = in_array('owner', $campos_adicionais);

        if ((empty($per_page) || $per_page == null) && !$this->input->post('homologacao-commit') && !$this->input->get('exportar_planilha_upload') && !$this->input->get('exportar')) {
            $this->usuario_homolog_bulk_model->clean();
        }

        $data['list_opt'] = $this->input->post('list_opt');

        if ($this->input->post('filtered'))
            $this->load->library('pagination');

        $this->usuario_model->set_state('filter.id_empresa', $id_empresa);
        $usuario = $this->usuario_model->get_entry(sess_user_id());

        $sistemas_origens = $this->input->post('sistema_origem');
        if (!is_null($sistemas_origens)) {
            $this->cad_item_model->set_state('filter.sistema_origem', $sistemas_origens);
        } else {
            $this->cad_item_model->unset_state('filter.sistema_origem');
        }

        $integracao_ecomex = $this->input->post('integracao_ecomex');
        if (!is_null($integracao_ecomex) && $integracao_ecomex != -1) {
            $this->cad_item_model->set_state('filter.integracao_ecomex', $integracao_ecomex);
        } else {
            $this->cad_item_model->set_state('filter.integracao_ecomex', null);
        }
        $grupos_tarifarios = '';
        $this->cad_item_model->unset_state('filter.use_index_helper');
        $post = $this->input->post();
        $data = [];
        $data = $this->apply_default_filtersV2($grupos_tarifarios, $post);

        $data['grupos_tarifarios'] = $grupos_tarifarios;
        $funcoes_adicionais = $this->empresa_model->get_funcoes_adicionais(null, $id_empresa);

        if ($this->input->get('exportar') && in_array('exportar', $funcoes_adicionais)) {
            return $this->exportar();
        }

        if ($this->input->get('exportar_paises') && in_array('exportar', $funcoes_adicionais)) {
            return $this->exportar_paises();
        }

        if ($this->input->get('exportar_planilha_upload') && in_array('planilha_upload', $funcoes_adicionais)) {
            return $this->exportar_planilha_upload();
        }

        if ($this->input->get('gerar_exportacao_itens_to_api') && in_array('exportar_diana', $funcoes_adicionais)) {
            return $this->gerar_exportacao_itens_to_api();
        }

        $lista_usuarios = $this->usuario_model->get_entries();

        if (isset($homologacoes['homologacao_engenharia']) && isset($homologacoes['homologacao_fiscal'])) {
        } else {
            for ($i = 0; $i < count($lista_usuarios); $i++) {
                $user = $lista_usuarios[$i];
                if (isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE) {
                    if (strpos($user->perfil, 'Engenharia') !== false) {
                        unset($lista_usuarios[$i]);
                    }
                }

                if (isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE) {
                    if (strpos($user->perfil, 'Fiscal') !== false) {
                        unset($lista_usuarios[$i]);
                    }
                }
            }
        }

        $data['lista_usuarios'] = $lista_usuarios;
        $data['lista_usuarios_ativos'] = array_values(array_filter($data['lista_usuarios'], function($usuario) {
            return $usuario->status == 1;
        }));
        $data['lista_usuarios_inativos'] = array_values(array_filter($data['lista_usuarios'], function($usuario) {
            return $usuario->status == 0;
        }));

        if ($this->cad_item_model->get_state('filter.filtered')) {
            if (has_role('admin') || has_role('consultor')) {
                // Apenas usuários de perfil engenheiro
                $data['usuarios_transferencia'] = $this->usuario_model->get_entries_by_role('engenheiro');
                $data['tipo_responsavel'] = 'id_resp_engenharia';
            } else {
                if (has_role('fiscal')) {
                    $data['tipo_responsavel'] = 'id_resp_fiscal';
                    $data['usuarios_transferencia'] = $this->usuario_model->get_entries_by_role('fiscal');
                } else if (has_role('engenheiro')) {
                    $data['tipo_responsavel'] = 'id_resp_engenharia';
                    $data['usuarios_transferencia'] = $this->usuario_model->get_entries_by_role('engenheiro');
                }
            }

            $this->load->library('pagination');
            $limit = 20;
            $offset = $per_page;

            $config['base_url'] = 'homologacao';
            $config['uri_segment'] = 3;
            $config['total_rows'] = $this->cad_item_model->get_entriesV2($limit, $offset, $homologacoes, FALSE, TRUE);
            $config['per_page'] = $limit;
            $config['page_query_string'] = TRUE;
            $config['num_links'] = 5;
            $this->cad_item_model->set_state('filter.total_rows', $config['total_rows']);

            $this->pagination->initialize($config);

            $data['itens'] = $this->cad_item_model->get_entriesV2($limit, $offset, $homologacoes);
            
            $data['query_homolog'] = $this->cad_item_model->get_entriesV2($limit, $offset, $homologacoes, TRUE);
            $data['total_rows'] = $config['total_rows'];
        } else {
            $data['itens'] = [];
            $data['query_homolog'] = '';
            $data['total_rows'] = 0;
        }

        $data['entry'] = $empresa;

        $data['campos_adicionais'] = explode('|', $empresa->campos_adicionais);

        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;
        $data['simplus']                = $empresa->integracao_simplus;

        $data['homologacoes'] = $homologacoes;

        $data['has_status_exportacao'] = in_array('status_exportacao', $funcoes_adicionais) ? true : false;

        $data['status_todos_atributos'] =  $this->atributo->get_all_status();

        // Motivos
        $this->motivo_model->set_state('filter.id_perfil', $usuario->id_perfil);
        $motivos = $this->motivo_model->get_entries();
        $data['motivos'] = $motivos;

        $data['funcoes_adicionais'] = $funcoes_adicionais;

        $homolog_bulk = $this->usuario_homolog_bulk_model->get_entry();
        $bulk_selection = !empty($homolog_bulk->data) ? unserialize($homolog_bulk->data) : array();

        $data['bulk_selection'] = $bulk_selection;
        $data['bulk_select_all'] = true;

        $data['empresa_pais'] = $this->empresa_pais_model->get_entries_by_empresa($empresa->id_empresa);

        $data['can_homologar_atributos'] = $homologarAtributos;

        // Lista de Estabelecimentos listados no filtro avançado
        $data['estabelecimentos'] = $this->empresa_model->get_estabelecimentos(sess_user_company());

        if (!empty($post['estabelecimento_modal'])) {
            $estabelecimento_modal = $this->input->post('estabelecimento_modal');
            if (is_array($estabelecimento_modal) && $estabelecimento_modal[0] != "" && $estabelecimento_modal[0] != -1) {
                $this->item_model->set_state('filter.estabelecimento_modal', $estabelecimento_modal);
            } else {
                $this->item_model->unset_state('filter.estabelecimento_modal');
            }
        } elseif (!$per_page && $post && empty($post['estabelecimento_modal'])) {
            $this->item_model->unset_state('filter.estabelecimento_modal');
        }

        $this->include_js([
            'jquery.cookie.js',
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-select/bootstrap-select.min.js',
            'b3-datetimepicker.min.js',
            'jquery.mask.min.js'
        ]);

        $this->include_css([
            'bootstrap-select/bootstrap-select.css',
            'bootstrap-select/bootstrap-select.min.css',
            'b3-datetimepicker.min.css'
        ]);

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push("Homologação", '/homologacao/');

        $this->render('homologacao/default', $data);
    }

    public function ajax_homologar()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $this->load->model(array(
            'cad_item_model',
            'empresa_model',
            'cad_item_wf_atributo_model',
            'log_wf_atributos_model'
        ));

        $id_empresa = sess_user_company();
        $homologacoes = $this->empresa_model->get_homologacao_by_id_empresa($id_empresa);

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);

        $response = [
            'status' => 'error',
            'message' => 'Algo deu errado. Tente novamente mais tarde.',
            'redirect_url' => site_url('homologacao')
        ];

        if (has_role('engenheiro') && company_can('homologacao_engenharia')) {
            $tipo_homologacao = 'Engenharia';
        } else if (has_role('fiscal') && company_can('homologacao_fiscal')) {
            $tipo_homologacao = 'Fiscal';
        } else {
            $response['message'] = 'Perfil do usuário não tem permissão de Engenheiro ou Fiscal e/ou a empresa não possui esta permissão.';
            return response_json($response);
        }

        $post = $this->input->post();

        // Se vier da ficha do item , não serão itens, mas sim apenas um item com id_item pelo post
        if (isset($post['isFicha']) && $post['isFicha'] == 1) {
            $id_item = $post['id_item'];
        } else {
            $id_item = '';
        }

        if (empty($id_item)) {
            $bulk = $this->usuario_homolog_bulk_model->get_entry();
            $itens = unserialize($bulk->data);
        } else {
            $itens[0] = $id_item;
        }

        // Contadores para controle de homologação de atributos
        $contadorAtributosHomologados = 0;
        $contadorAtributosNaoHomologados = 0;
        $atributosObrigatoriosNaoPreenchidos = [];

        foreach ($itens as $item) {
            $id_usuario = sess_user_id();
            $is_homologado = (int) $this->input->post('homologado');
            $motivo = '';

            if ($is_homologado == 2) {
                $id_motivo_inativo = $this->input->post('id_motivo_inativo');
                if ($id_motivo_inativo !== '') {
                    $motivo_inativo = $this->motivo_model->get_entry($id_motivo_inativo);
                    $motivo = $motivo_inativo->motivo . ' ' .$this->input->post('motivo') ;
                } else {
                    $motivo = $this->input->post('motivo');
                }
            } else {
                $motivo = $this->input->post('motivo');
            }

            $this->cad_item_model->set_item_homologado($item, $tipo_homologacao, $id_usuario, $is_homologado, $motivo, NULL, $homologacoes);

            if ($this->input->post('ckbx_wf')) {
                $itensValidados = $this->cad_item_wf_atributo_model->get_itens_validados('homologados', $item);

                if (!empty($itensValidados)) {
                    $this->load->model('cad_item_attr_model');
                    $atributos = $this->ajax_get_attrs($itensValidados[0]->id_item, $itensValidados[0]->ncm_proposto);

                    // Validação de atributos obrigatórios para este item específico
                    $atributosObrigatoriosNaoPreenchidosItem = [];

                    if (isset($atributos["data"])) {
                        $atributosParaPreenchimento = isset($atributos["data"]["assocAttrs"]) ?
                            $atributos["data"]["assocAttrs"] :
                            [];

                        if (!empty($atributos["data"]["defaultAttrs"])) {

                            $atributosPreenchidos = [];
                            foreach ($atributos["data"]["defaultAttrs"] as $key => $value) {
                                $atributosPreenchidos[$value->id] = $value->codigo;
                            }

                            foreach ($atributosParaPreenchimento as $key => $value) {
                                if ($value["obrigatorio"] == true || $value["obrigatorio"] == '1') {
                                    // Verifica se o atributo possui dbdata e se o código está vazio
                                    if (isset($value["dbdata"]) && isset($value["dbdata"]["codigo"])) {
                                        // Se o código estiver vazio, é um atributo obrigatório não preenchido
                                        if (trim($value["dbdata"]["codigo"]) === "") {
                                            $atributosObrigatoriosNaoPreenchidosItem[] = $value;
                                            $atributosObrigatoriosNaoPreenchidos[] = $value;
                                        }
                                    } // else {
                                    //     // Caso não haja dbdata, trata como não preenchido
                                    //     $atributosObrigatoriosNaoPreenchidosItem[] = $value;
                                    //     $atributosObrigatoriosNaoPreenchidos[] = $value;
                                    // }
                                }
                            }

                            if (empty($atributosObrigatoriosNaoPreenchidosItem)) {
                                $hasAtributosFilhosObrigatoriosNaoPreenchidos = $this->cad_item_attr_model->check_mandatory_child_attributes($item);

                                if ($hasAtributosFilhosObrigatoriosNaoPreenchidos) {
                                    $atributosObrigatoriosNaoPreenchidosItem[] = $hasAtributosFilhosObrigatoriosNaoPreenchidos;
                                    $atributosObrigatoriosNaoPreenchidos[] = $hasAtributosFilhosObrigatoriosNaoPreenchidos;
                                }
                            }
                        }
                    }

                    // Verifica se pode homologar este item específico
                    if (empty($atributosObrigatoriosNaoPreenchidosItem) || $this->input->post('forcar_homologacao')) {
                        $this->cad_item_wf_atributo_model->set_status('homologados', $item);

                        $justificativa  =  isset($post['justification']) ? $post['justification'] : '';
                        $statusAtributosHomologados = $this->cad_item_wf_atributo_model->get_status_wf_atributos('homologados');
                        $itemWithPartNumber = $this->cad_item_model->get_entry_by_id_item($item);

                        if (!empty($itemWithPartNumber)) {
                            $part_numbers = $itemWithPartNumber['part_number'];
                            $estabelecimento = $itemWithPartNumber['estabelecimento'];
                            $id_empresa = $itemWithPartNumber['id_empresa'];

                            $this->log_wf_atributos_model->registrar_log(
                                $item,
                                $part_numbers,
                                $estabelecimento,
                                $id_empresa,
                                $statusAtributosHomologados->id,
                                'movimentacao_manual',
                                $id_usuario,
                                $justificativa
                            );
                        }

                        $contadorAtributosHomologados++;
                    } else {
                        // Item não foi homologado devido a atributos obrigatórios não preenchidos
                        $contadorAtributosNaoHomologados++;
                    }
                }
            }
        }

        // Verifica se houve checkbox de workflow de atributos marcado
        if ($this->input->post('ckbx_wf')) {
            // Se há atributos obrigatórios não preenchidos e não é forçar homologação
            if (!empty($atributosObrigatoriosNaoPreenchidos) && !$this->input->post('forcar_homologacao')) {
                $homologarAtributosComObrigatoriosVazios = in_array('homologacao_atributos_incompletos', $funcoes_adicionais);

                // Se a empresa tem permissão para homologar com atributos incompletos, mostra warning
                if ($homologarAtributosComObrigatoriosVazios) {
                    $response['status'] = 'warning';
                    $response['atributos_vazios'] = true;
                    $response['pode_homologar'] = true;
                    $response['message'] = 'Existem atributos obrigatórios que ainda não foram preenchidos.';
                } else {
                    // Empresa não tem permissão, mas agora retorna informações sobre o processamento
                    $response['status'] = 'success';
                    $totalItens = count($itens);
                    $response['message'] = "Processamento concluído. {$contadorAtributosHomologados} de {$totalItens} itens tiveram atributos homologados. {$contadorAtributosNaoHomologados} itens ficaram pendentes devido a atributos obrigatórios não preenchidos.";
                }

                $response['atributos_homologados'] = $contadorAtributosHomologados;
                $response['atributos_nao_homologados'] = $contadorAtributosNaoHomologados;
                $response['redirect_url'] = isset($post['isFicha']) &&
                    $post['isFicha'] ?
                    site_url("homologacao/ficha/" . $post['id_item']) :
                    site_url("homologacao");
            } else {
                // Todos os itens foram homologados com sucesso ou foi forçada a homologação
                $response['status'] = 'success';
                $totalItens = count($itens);
                if ($contadorAtributosHomologados > 0) {
                    $response['message'] = "Informações gravadas no banco de dados. {$contadorAtributosHomologados} de {$totalItens} itens tiveram atributos homologados.";
                } else {
                    $response['message'] = 'Informações gravadas no banco de dados.';
                }
                $response['atributos_homologados'] = $contadorAtributosHomologados;
                $response['atributos_nao_homologados'] = $contadorAtributosNaoHomologados;
                $response['redirect_url'] = isset($post['isFicha']) &&
                    $post['isFicha'] ?
                    site_url("homologacao/ficha/" . $post['id_item']) :
                    site_url("homologacao");
                $this->bulk_clean();
            }
        } else {
            // Não foi marcado checkbox de workflow de atributos
            $response['status'] = 'success';
            $response['message'] = 'Informações gravadas no banco de dados.';
            $response['redirect_url'] = isset($post['isFicha']) &&
                $post['isFicha'] ?
                site_url("homologacao/ficha/" . $post['id_item']) :
                site_url("homologacao");
            $this->bulk_clean();
        }

        return response_json($response);
    }

    public function xhr_get_itens()
    {
        $itens = $this->cad_item_model->get_entriesV2($limit, $offset, $homologacoes);

        foreach ($itens as $item) {
            $link = site_url("homologacao/ficha/" . $item->id_item);
            $partnumber = $item->part_number;
            $owner_name = $this->owner_model->get_nome_owner_and_responsaveis($item->cod_owner);
            /*
            <tr class="click-select">
                <td>
                    <input type="checkbox" <?php echo in_array($item->id_item, $bulk_selection) ? 'checked' : '' ?> class="chkitem item_selected" name="chkitem" value="<?php echo $item->id_item ?>" data-part-number="<?php echo $item->part_number ?>" data-estabelecimento="<?php echo $item->estabelecimento ?>" data-tipo="homologacao" />
                </td>
                <td valign="top" align="left">
                    <div class="d-flex">
                        <a style="margin-top: 1px;" href="<?php echo $link ?>"><?php echo $partnumber ?></a>
                        <?php if ($item->item_ja_homologado == 1) : ?>
                            <span data-toggle="tooltip" title="Item já homologado anteriormente" style="margin-left: 5px;">
                                <img src="/assets/img/ok-icon.ico" alt="Ícone check verde para representar OK" width="16">
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="">
                        <?php if ($simplus) : ?>
                            <?php if ($item->status_simplus == null || $item->status_simplus == 0) : ?>
                                <i class="fa fa-circle" style="color: gray" data-toggle="tooltip" title="Item pendente de envio para a Simplus"></i>
                            <?php elseif ($item->status_simplus == 1) : ?>
                                <i class="fa fa-circle" style="color: green" data-toggle="tooltip" title="Item enviado para Simplus"></i>
                            <?php endif ?>
                        <?php endif ?>
                        <?php if ($has_status_exportacao) : ?>
                            <?php if ($item->status_exportacao) : ?>
                                <span style="color: white;background-color:#5cb85c;" class="badge" data-toggle="tooltip" title="Item exportado">
                                    <small>Exportado</small>
                                </span>
                            <?php else : ?>
                                <span style="color: white;background-color:#af0000;" class="badge" data-toggle="tooltip" title="Item pendente">
                                    <small>Pendente</small>
                                </span>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if (in_array('owner', $campos_adicionais)) : ?>
                            <?php if ($item->id_status == 10) : ?>
                                <span style="color: white;background-color:#af0000;" class="badge" data-toggle="tooltip" title="Item Pendente de Integração">
                                    <small>Pendente Integração</small>
                                </span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </td>
                <?php if ($multi_estabelecimentos == 1) { ?>
                    <td class="text-center"><?php echo $item->estabelecimento ? $item->estabelecimento : 'N/A'; ?></td>
                <?php } ?>
                <td>
                    <a href="<?php echo $link ?>"><?php echo $item->descricao_atual ?></a>
                </td>
                <?php if (in_array('owner', $campos_adicionais)) : ?>
                    <td>
                        
                        <?php echo $owner_name ?>
                    </td>
                <?php else : ?>
                    <td>
                        <?php echo $item->descricao_mercado_local ?>
                    </td>
                <?php endif; ?>
                <?php if (in_array('owner', $campos_adicionais)) : ?>
                    <td><?php echo $item->prioridade ?></td>
                <?php else : ?>
                    <td><?php echo $item->evento ?></td>
                <?php endif; ?>
                <td><?php echo $item->ncm_atual ?></td>
                <td>
                    <?php echo $item->ncm_proposto ?>
                </td>
                <td class="text-center">
                    <?php

                    if (preg_replace("/[^0-9]/", "", $item->ncm_atual) != preg_replace("/[^0-9]/", "", $item->ncm_proposto)) {
                    ?>
                        <span class="glyphicon glyphicon-exclamation-sign" style="color: #af0000; font-size: 20px"></span>
                    <?php
                    }
                    ?>
                </td>
                <td class="text-center">
                    <?php

                    $homolog_info = $this->cad_item_model->get_homolog_info($item->id_item);
                    
                    if (isset($homolog_info) && count($homolog_info) > 0) {
                        $count_values = 0;
                        if (count($homolog_info) == 1) {

                            if (
                                ($homolog_info[0]->tipo_homologacao == 'Engenharia' && isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE) ||
                                ($homolog_info[0]->tipo_homologacao == 'Fiscal' && isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE)
                            ) {
                                if ($homolog_info[0]->homologado == 1) {
                    ?>
                                    <h3 data-toggle="tooltip" title="<?php echo $homolog_info[0]->nome ?>" class="glyphicon glyphicon-thumbs-up text-green-next"></h3>
                                <?php
                                } else if ($homolog_info[0]->homologado == 0) {
                                ?>
                                    <h3 data-toggle="tooltip" title="<?php echo $homolog_info[0]->nome ?>" class="glyphicon glyphicon-thumbs-down text-red-next"></h3>
                                <?php
                                } else {
                                ?>
                                    <h3 data-toggle="tooltip" title="<?php echo $homolog_info[0]->nome ?>" class="glyphicon glyphicon-asterisk text-gray-next"></h3>
                                <?php
                                }
                                $count_values++;
                            }
                            if (
                                (isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE ||
                                    isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE) && count($homologacoes) == 2
                            ) {
                                $count_values++;
                                ?>
                                <h3 class="glyphicon glyphicon-thumbs-up text-muted-next"></h3>
                            <?php
                            }
                            if ($count_values == 0) : ?>
                                <?php if ($homologado) : ?>
                                    <h3 data-toggle="tooltip" title="<?php echo $homolog_info[0]->nome ?>" class="glyphicon glyphicon-thumbs-up text-green-next"></h3>
                                <?php else : ?>
                                    <h3 class="glyphicon glyphicon-thumbs-up text-muted-next"></h3>
                                <?php endif; ?>
                                <?php
                            endif;
                        } else {
                            foreach ($homolog_info as $info) {
                                if (
                                    ($info->tipo_homologacao == 'Engenharia' && isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] === TRUE) ||
                                    ($info->tipo_homologacao == 'Fiscal' && isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] === TRUE)
                                ) {
                                    if ($info->homologado == 1) {
                                ?>
                                        <h3 data-toggle="tooltip" title="<?php echo $info->nome ?>" class="glyphicon glyphicon-thumbs-up text-green-next"></h3>
                                    <?php
                                    } else if ($info->homologado == 0) {
                                    ?>
                                        <h3 data-toggle="tooltip" title="<?php echo $info->nome ?>" class="glyphicon glyphicon-thumbs-down text-red-next"></h3>
                                    <?php
                                    } else {
                                    ?>
                                        <h3 data-toggle="tooltip" title="<?php echo $info->nome ?>" class="glyphicon glyphicon-asterisk text-gray-next"></h3>
                        <?php
                                    }
                                }
                            }
                        }
                    } else {
                        ?>
                        <?php if (isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE) : ?>
                            <h3 class="glyphicon glyphicon-thumbs-up text-muted-next"></h3>
                        <?php endif; ?>
                        <?php if (isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE) : ?>
                            <h3 class="glyphicon glyphicon-thumbs-up text-muted-next"></h3>
                        <?php endif; ?>
                    <?php
                    }

                    ?>
                </td>
            </tr>
            */
        }
    }

 
    public function exportar()
    {
        error_reporting(0);
        set_time_limit(0);
        ini_set('memory_limit', -1);
        $id_empresa = sess_user_company();
    
        if ($this->cad_item_model->get_state('filter.list_opt') === 'todos') {
            $this->load->model('empresa_model');
            $empresa = $this->empresa_model->get_entry($id_empresa);
            $campos_adicionais = explode("|", $empresa->campos_adicionais);
            $hasOwner = in_array('owner', $campos_adicionais);
            if (!$hasOwner) {
                $this->cad_item_model->set_state('filter.use_index_helper', TRUE);
            }
        }
    
        $status_cad_item_homolog = array(
            0 => 'Reprovado',
            1 => 'Homologado',
            2 => 'Obsoleto'
        );
    
        $this->load->model('usuario_homolog_bulk_model');
        $result = $this->usuario_homolog_bulk_model->get_entry();
        $idItens = array();
        if ($result) $idItens = unserialize($result->data);
    
        $company_can_lessin = company_can('lessin');
        if ($company_can_lessin) {
            $this->cad_item_model->set_state("filter.lessin", true);
        }
    
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode('|', $empresa->campos_adicionais);
        $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais);
    
        $with_atributos = $this->input->get('atributos') == 1;
        $with_perguntas_respostas = $this->input->get('perguntas_respostas') == 1;
    
        if ($with_atributos) {
            $this->cad_item_model->set_state('filter.with_atributos', true);
        } else {
            $this->cad_item_model->unset_state('filter.with_atributos');
        }
    
        if ($with_perguntas_respostas) {
            $this->cad_item_model->set_state('filter.with_perguntas_respostas', true);
        } else {
            $this->cad_item_model->unset_state('filter.with_perguntas_respostas');
        }
        
        $itens = $this->cad_item_model->get_homolog_data_xls($idItens);
    
        $headerRow = array(
            array('label' => 'CÓDIGO DO PRODUTO', 'field' => 'codigo_poduto', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DESCRIÇÃO PROPOSTA RESUMIDA', 'field' => 'desc_proposta', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'GRUPO', 'field' => 'grupo', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'CARACTERÍSTICA', 'field' => 'caracteristica', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'SUBSÍDIO', 'field' => 'subsidio', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'RESPONSAVEL FISCAL', 'field' => 'responsavel_fiscal', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'RESPONSAVEL ENGENHARIA', 'field' => 'responsavel_engenharia', 'col_format' => 'texto', 'col_width' => 30),
        );
    
        if (in_array('owner', $campos_adicionais)) {
            $headerRow[] = array('label' => 'OWNER', 'field' => 'cod_owner', 'col_format' => 'texto', 'col_width' => 30);
        }
    
        $headerRow = array_merge($headerRow, array(
            array('label' => 'ATUALIZAR (SIM/NAO)', 'field' => 'forcar_atualizacao', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'MEMÓRIA DE CLASSIFICAÇÃO', 'field' => 'memoria_classificacao', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'EVENTO', 'field' => 'evento', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DISPOSITIVOS LEGAIS', 'field' => 'dispositivos_legais', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'SOLUÇÃO DE CONSULTA', 'field' => 'solucao_consulta', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'FUNÇÃO', 'field' => 'funcao', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'PESO', 'field' => 'peso', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'PRIORIDADE', 'field' => 'prioridade', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'APLICAÇÃO', 'field' => 'aplicacao', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'MARCA', 'field' => 'marca', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DESCRIÇÃO PROPOSTA COMPLETA', 'field' => 'descricao_completa', 'col_format' => 'texto', 'col_width' => 30),
        ));
    
        if ($hasDescricaoGlobal) {
            $headerRow[] = array('label' => 'DESCRIÇÃO GLOBAL', 'field' => 'descricao_global', 'col_format' => 'texto', 'col_width' => 30);
        }
        
        $headerRow = array_merge($headerRow, array(
            array('label' => 'OBSERVAÇÕES', 'field' => 'observacoes', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'ESTABELECIMENTO', 'field' => 'estabelecimento', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'MATERIAL CONSTITUTIVO', 'field' => 'material_constitutivo', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'CEST', 'field' => 'cest', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'INFORMAÇÕES ADICIONAIS', 'field' => 'info_adicionais', 'col_format' => 'texto', 'col_width' => 30),
        ));
    
        if ($with_perguntas_respostas) {
            $headerRow[] = array('label' => 'PERGUNTAS & RESPOSTAS', 'field' => 'perguntas_respostas', 'col_format' => 'texto', 'col_width' => 30);
        }
    
        $headerRow = array_merge($headerRow, array(
            array('label' => 'DATA DE CRIAÇÃO', 'field' => 'dat_criacao', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DATA DE MODIFICAÇÃO', 'field' => 'data_modificacao', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'TORNAR PENDENTE DE ENVIO PARA SIMPLUS? (SIM/NAO)', 'field' => 'tornar_pendente', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'ORIGEM (PAIS)', 'field' => 'origem', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'MÁQUINA', 'field' => 'maquina', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'NCM ATUAL', 'field' => 'ncm_atual', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'NCM PROPOSTO', 'field' => 'ncm_proposto', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'NCM FORNECEDOR', 'field' => 'ncm_fornecedor', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DESCRIÇÃO ATUAL', 'field' => 'descricao_atual', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'ITEM JÁ FOI HOMOLOGADO? (SIM/NAO)', 'field' => 'item_ja_homologado', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DATA DA HOMOLOGAÇÃO', 'field' => 'data_homologacao_engenharia', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'USUÁRIO QUE HOMOLOGOU FISCAL', 'field' => 'usuario_homologa_fiscal', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'USUÁRIO QUE HOMOLOGOU ENGENHARIA', 'field' => 'usuario_homologa_engenharia', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'STATUS DO ITEM', 'field' => 'status', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'STATUS DO ITEM FISCAL', 'field' => 'status_fiscal', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'STATUS DO ITEM ENGENHARIA', 'field' => 'status_engenharia', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'MOTIVO DO STATUS DO ITEM FISCAL', 'field' => 'motivo_fiscal', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'MOTIVO DO STATUS DO ITEM ENGENHARIA', 'field' => 'motivo_engenharia', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'NCM PROPOSTA POSSUI EX de II?', 'field' => 'ncm_ii', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'NCM PROPOSTA POSSUI EX de IPI?', 'field' => 'ncm_ipi', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'NCM PROPOSTA POSSUI NVE?', 'field' => 'ncm_nve', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'NOVO MATERIAL', 'field' => 'integracao_novo_material', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'INDICADOR (COMEX)', 'field' => 'indicador_ecomex', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DATA PREVISTA PO/INVOICE (COMEX)', 'field' => 'data_invoice', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DI (COMEX)', 'field' => 'num_di', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DATA ÚLTIMA DI (COMEX)', 'field' => 'data_di', 'col_format' => 'data', 'col_width' => 30),
            array('label' => 'É DRAWBACK? (COMEX)', 'field' => 'ind_drawback', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'NCM (COMEX)', 'field' => 'ncm_ecomex', 'col_format' => 'texto', 'col_width' => 30)
        ));
    
        $customer_can_ncm_fornecedor = customer_can('ncm_fornecedor');
        $customer_can_suframa = customer_can('suframa');
        $customer_can_ex_ipi = customer_can('ipi');
        $customer_can_ex_ii = customer_can('ii');
        $customer_can_nve = customer_can('nve');
    
        if ($customer_can_nve) {
            for ($i = 'a'; $i <= 'h'; $i++) {
                $headerRow[] = array('label' => 'NVE ATRIBUTO A' . strtoupper($i), 'field' => 'nve_a' . $i, 'col_format' => 'texto', 'col_width' => 30);
                $headerRow[] = array('label' => 'NVE VALOR A' . strtoupper($i), 'field' => 'nve_valor_a' . $i, 'col_format' => 'texto', 'col_width' => 30);
            }
            $headerRow[] = array('label' => 'NVE ATRIBUTO U', 'field' => 'nve_u', 'col_format' => 'texto', 'col_width' => 30);
            $headerRow[] = array('label' => 'NVE VALOR U', 'field' => 'nve_valor_u', 'col_format' => 'texto', 'col_width' => 30);
            $headerRow[] = array('label' => 'NVE ATRIBUTO UE', 'field' => 'nve_ue', 'col_format' => 'texto', 'col_width' => 30);
            $headerRow[] = array('label' => 'NVE VALOR UE', 'field' => 'nve_valor_ue', 'col_format' => 'texto', 'col_width' => 30);
        }
        
        $headerRow = array_merge($headerRow, array(
            array('label' => 'LICENCIAMENTO NÃO AUTOMÁTICO', 'field' => 'licenciamento_nao_automatico', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'ORGÃO ANUENTE', 'field' => 'orgao_anuente', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'DESTAQUE LI', 'field' => 'destaque_li', 'col_format' => 'texto', 'col_width' => 30),
            array('label' => 'ANTIDUMPING ATIVO', 'field' => 'antidumping_ativo', 'col_format' => 'texto', 'col_width' => 30)
        ));
        
        if ($customer_can_suframa) {
            $headerRow = array_merge($headerRow, array(
                array('label' => 'SUFRAMA CÓDIGO', 'field' => 'suframa_codigo', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'SUFRAMA DESTAQUE', 'field' => 'suframa_destaque', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'SUFRAMA PPB', 'field' => 'suframa_ppb', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'DESCRIÇÃO SUFRAMA', 'field' => 'descricao_suframa', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'CÓDIGO PRODUTO SUFRAMA', 'field' => 'codigo_suframa', 'col_format' => 'texto', 'col_width' => 30)
            ));
        }
        
        if ($customer_can_ex_ipi) {
            $headerRow = array_merge($headerRow, array(
                array('label' => 'EX-IPI', 'field' => 'ex_ipi', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'TEXTO DO EX-IPI', 'field' => 'texto_ex_ipi', 'col_format' => 'texto', 'col_width' => 30)
            ));
        }
        
        if ($customer_can_ex_ii) {
            $headerRow = array_merge($headerRow, array(
                array('label' => 'EX-II', 'field' => 'exii', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'TEXTO DO EX-II', 'field' => 'texto_ex_ii', 'col_format' => 'texto', 'col_width' => 30)
            ));
        }
    
        if ($company_can_lessin) {
            $headerRow = array_merge($headerRow, array(
                array('label' => 'FAZ PARTE LISTA BECOMEX?', 'field' => 'lista_becomex', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'LISTA BECOMEX REGRA APLICADA', 'field' => 'lista_becomex_regra', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'FAZ PARTE LISTA CLIENTE?', 'field' => 'lista_cliente', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'DESTAQUES INCLUÍDOS', 'field' => 'destaques_incluidos', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'DESTAQUES EXCLUÍDOS', 'field' => 'destaques_excluidos', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'FUNDAMENTO', 'field' => 'fundamento', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'DATA LESSIN', 'field' => 'data_lessin', 'col_format' => 'texto', 'col_width' => 30),
                array('label' => 'DATA VALIDADE', 'field' => 'data_validade', 'col_format' => 'texto', 'col_width' => 30)
            ));
        }
    
        $headerRow[] = array('label' => 'STATUS DE ATRIBUTOS', 'field' => 'status_atributos', 'col_format' => 'texto', 'col_width' => 30);
        $headerRow[] = array('label' => 'STATUS DE PREENCHIMENTO DOS ATRIBUTOS', 'field' => 'status_preenchimento_atributos', 'col_format' => 'texto', 'col_width' => 45);
    
        if ($with_atributos) {
            for ($x = 1; $x <= 25; $x++) {
                $headerRow[] = array('label' => 'ATRIBUTO ' . $x, 'field' => 'attr_atributo_' . $x, 'col_format' => 'texto', 'col_width' => 30);
                $headerRow[] = array('label' => 'VALOR ' . $x, 'field' => 'attr_valor_' . $x, 'col_format' => 'texto', 'col_width' => 30);
            }
        }
    
        $cabecalho = $this->extract_fields_from_cols($headerRow);
        $config = array(
            'filename'      => 'Homologacao' . date('Y-m-d_H-i-s'),
            'titulo'        => "Homologacao",
            'nome_planilha' => "Homologacao",
            'colunas'       => $headerRow,
            'cabecalho'     => $cabecalho,
            'filter_suffix' => "HOM_"
        );
    
        require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';
        $relatorio = new XlsxGenerator($config['filename']);
        $writer = $relatorio->init($config['filename']);
        $relatorio->filename = $config['filename'];
    
        $widths = $relatorio->getWidth($config);
        $fields = array();
        if (isset($config['colunas'])) {
            foreach ($config['colunas'] as $coluna) {
                if (isset($coluna['label']))
                    $fields[$coluna['label']] = isset($coluna['col_format']) ? $coluna['col_format'] : 'string';
            }
        }
    
        $writer->addImage(FCPATH . 'assets/img/header/logo.png', 1, array('widths' => $widths));
        $headerStyle = array('font' => 'Arial', 'font-size' => 12, 'font-style' => 'bold', 'color' => '#000000', 'fill' => '#F9FF00', 'halign' => 'center', 'valign' => 'center', 'border' => 'bottom', 'wrap_text' => 'true', 'widths' => $widths);
        $writer->writeSheetHeader($config['nome_planilha'], $fields, $headerStyle);
        $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
        $defaultStyle = array('font' => 'Arial', 'font-size' => 11);
    
        $this->load->model(array('grupo_tarifario_model', 'ex_tarifario_model', 'nve_atributo_model', 'cad_item_nve_model', 'ctr_resposta_model', 'empresa_model', 'lessin_model', 'catalogo/produto_model', 'cad_item_attr_model'));
        $funcoes_adicionais = $this->empresa_model->get_funcoes_adicionais(null, sess_user_company());
        $show_id_grupo_tarifario = in_array('exibir_id_gpt', $funcoes_adicionais);
        $max_nve = 20;
    
        while ($item = $itens->unbuffered_row()) {
            $rowData = array();
    
            $possui_ex_ii  = $item->total_ex > 0 ? 'SIM' : 'NÃO';
            $possui_ex_ipi = $item->total_exipi > 0 ? 'SIM' : 'NÃO';
            $possui_nve    = $item->has_nve > 0 ? 'SIM' : 'NÃO';
            $val_homolog_fiscal_status = $item->homolog_fiscal_status !== null ? $status_cad_item_homolog[$item->homolog_fiscal_status] : '';
            $val_homolog_engenharia_status = $item->homolog_engenharia_status !== null ? $status_cad_item_homolog[$item->homolog_engenharia_status] : '';
            $cod_cest = 'N/D - Não atende';
            if ($item->cod_cest !== null && $item->cod_cest != '-1') {
                $cod_cest = $item->cod_cest;
            }
    
            $perguntasRespostas = "";
            if ($with_perguntas_respostas) {
                $this->ctr_resposta_model->set_state('filter.partnumber', $item->part_number);
                $this->ctr_resposta_model->set_state('filter.estabelecimento', $item->estabelecimento);
                $historicoItem = $this->ctr_resposta_model->getHistoricoItem();
                if (!empty($historicoItem)) {
                    foreach ($historicoItem as $h => $historico) {
                        $perguntasRespostas .= ($h + 1) . " - {$historico->pergunta}\n";
                        $perguntasRespostas .= "R: " . (!empty($historico->resposta) ? "$historico->resposta \n\n" : " \n\n");
                    }
                }
            }
    
            $data_di = !empty($item->data_di) ? date('d/m/Y', strtotime($item->data_di)) : null;
            $format_date_invoice = !empty($item->data_invoice) ? date('d/m/Y', strtotime($item->data_invoice)) : '';
            $codigo_produto = $item->part_number;
            if (substr($codigo_produto, 0, 1) === ' ') {
                $codigo_produto = "\u{200B}" . $codigo_produto;
            }
            $data_fonte_homologacao = $item->homolog_engenharia_data ?? $item->homolog_fiscal_data ?? '';
            $data_homologacao = !empty($data_fonte_homologacao) ? date("d/m/Y", strtotime($data_fonte_homologacao)) : '';
    
            // --- Montagem da linha na ordem exata do cabeçalho ---
            $rowData[] = $codigo_produto;
            $rowData[] = $item->descricao_mercado_local;
            $rowData[] = $show_id_grupo_tarifario ? $item->id_grupo_tarifario : $item->grupo_tarifario;
            $rowData[] = $item->caracteristicas;
            $rowData[] = $item->subsidio;
            $rowData[] = $item->resp_usuario_fiscal;
            $rowData[] = $item->resp_usuario_engenharia;
    
            if (in_array('owner', $campos_adicionais)) {
                $rowData[] = $item->owner_codigo . ' - ' . $item->owner_descricao . ' - ' . $item->responsaveis_gestores_nomes;
            }
    
            $rowData[] = 'NAO'; // forcar_atualizacao
            $rowData[] = $item->memoria_classificacao;
            $rowData[] = $item->evento;
            $rowData[] = $item->dispositivo_legal;
            $rowData[] = $item->solucao_consulta;
            $rowData[] = $item->funcao;
            $rowData[] = $item->peso;
            $rowData[] = $item->empresa_prioridade;
            $rowData[] = $item->aplicacao;
            $rowData[] = $item->marca;
            $rowData[] = $item->descricao_proposta_completa;
    
            if ($hasDescricaoGlobal) {
                $rowData[] = isset($item->descricao_global) ? $item->descricao_global : 'N/A';
            }
    
            $rowData[] = $item->observacoes;
            $rowData[] = $item->estabelecimento;
            $rowData[] = $item->material_constitutivo;
            $rowData[] = $cod_cest;
            $rowData[] = $item->inf_adicionais;
    
            if ($with_perguntas_respostas) {
                $rowData[] = $perguntasRespostas;
            }
    
            $rowData[] = isset($item->dat_criacao) ? date("d/m/Y", strtotime($item->dat_criacao)) : "";
            $rowData[] = isset($item->data_modificacao) ? date("d/m/Y", strtotime($item->data_modificacao)) : "";
            $rowData[] = 'NÃO'; // tornar_pendente
            $rowData[] = isset($item->origem) ? $item->origem : null;
            $rowData[] = isset($item->maquina) ? $item->maquina : null;
            $rowData[] = $item->ncm_atual;
            $rowData[] = $item->ncm_proposto;
            $rowData[] = $customer_can_ncm_fornecedor ? $item->ncm_fornecedor : '';
            $rowData[] = $item->descricao_atual;
            $rowData[] = $item->item_ja_homologado ? 'SIM' : 'NÃO';
            $rowData[] = $data_homologacao;
            $rowData[] = $item->homolog_fiscal_email;
            $rowData[] = $item->homolog_engenharia_email;
            $rowData[] = $item->item_status;
            $rowData[] = $val_homolog_fiscal_status;
            $rowData[] = $val_homolog_engenharia_status;
            $rowData[] = $item->homolog_fiscal_motivo;
            $rowData[] = $item->homolog_engenharia_motivo;
            $rowData[] = $possui_ex_ii;
            $rowData[] = $possui_ex_ipi;
            $rowData[] = $possui_nve;
            $rowData[] = isset($item->integracao_novo_material) ? (($item->integracao_novo_material == 'S') ? 'SIM' : 'NÃO') : null;
            $rowData[] = $item->indicador_ecomex;
            $rowData[] = $format_date_invoice;
            $rowData[] = $item->num_di;
            $rowData[] = $data_di;
            $rowData[] = isset($item->data_di) ? (($item->ind_drawback == 1 || $item->ind_drawback == 'S') ? 'S' : 'N') : null;
            $rowData[] = $item->ncm_ecomex;
    
            if ($customer_can_nve) {
                if ($possui_nve == 'SIM' && $item->has_nve > 0) {
                    $nve_full_info = !empty($item->nve_full_info) ? explode('||', $item->nve_full_info) : [];
                    $nve_data = [];
                    foreach ($nve_full_info as $info) {
                        list($atributo, $valor) = explode(':', $info, 2);
                        $nve_data[] = $atributo;
                        $nve_data[] = $valor;
                    }
                    $rowData = array_merge($rowData, $nve_data);
                    // Preenche o restante com strings vazias para alinhar
                    for ($i = 0; $i < $max_nve - count($nve_data); $i++) {
                        $rowData[] = '';
                    }
                } else {
                    for ($i = 0; $i < $max_nve; $i++) {
                        $rowData[] = '';
                    }
                }
            }
    
            $rowData[] = !empty($item->li) ? $item->li : '';
            $rowData[] = !empty($item->li_orgao_anuente) ? $item->li_orgao_anuente : '';
            $rowData[] = !empty($item->li_destaque) ? $item->li_destaque : '';
            $rowData[] = !empty($item->antidumping) ? $item->antidumping : '';
    
            if ($customer_can_suframa) {
                $rowData[] = $item->suframa_codigo;
                $rowData[] = $item->suframa_destaque;
                $rowData[] = $item->suframa_ppb;
                $rowData[] = $item->suframa_descricao;
                $rowData[] = $item->suframa_produto;
            }
    
            if ($customer_can_ex_ipi) {
                $rowData[] = !empty($item->num_ex_ipi) ? $item->num_ex_ipi : '';
                $rowData[] = $item->num_ex_ipi == "-1" ? 'ITEM NÃO ATENDE EX' : (!empty($item->num_ex_ipi) ? $item->descricao_ex_linha1_ipi : "");
            }
    
            if ($customer_can_ex_ii) {
                $rowData[] = !empty($item->num_ex_ii) ? $item->num_ex_ii : '';
                $rowData[] = $item->num_ex_ii == "-1" ? 'ITEM NÃO ATENDE EX' : (!empty($item->num_ex_ii) ? $item->descricao_ex_linha1_ii : "");
            }
    
            if ($company_can_lessin) {
                $rowData[] = $item->lista_becomex;
                $rowData[] = $item->regra_aplicada . ' - ' . $this->lessin_model->desc_regra($item->regra_aplicada);
                $rowData[] = $item->lista_cliente;
                $rowData[] = $item->destaques_incluidos ?: "";
                $rowData[] = $item->destaques_excluidos ?: "";
                $rowData[] = $item->fundamento ?: "";
                $rowData[] = $item->criado_em ? date("d/m/Y", strtotime($item->criado_em)) : "";
                $rowData[] = $item->validade ? date("d/m/Y", strtotime($item->validade)) : "";
            }
    
            $rowData[] = $item->status_atributos ?: "";
            $rowData[] = $item->status_preenchimento ? $this->getStatusPreenchimentoDescription($item->status_preenchimento) : "";
    
            if ($with_atributos) {
                $atributos_adicionados = 0;
                if (!empty($item->atributos)) {
                    $atributos = explode('|:|', $item->atributos);
                    $added_attrs = [];
             
                    foreach ($atributos as $attr) {
                        $valores = explode('||', $attr);
                        if (empty(trim($valores[0])) || in_array($valores[0], $added_attrs)) {
                            continue;
                        }
                        
                        $rowData[] = $valores[0] != " null " ? $valores[0] : ''; // Atributo
                        $rowData[] = $valores[1] !=  " - " ? $valores[1] : ''; // Valor
     
                        $added_attrs[] = $valores[0] != " null " ? $valores[0] : '';
                        $atributos_adicionados++;
                    }
                  
              
                }
                // Preenche as colunas de atributos restantes para manter o alinhamento
                for ($i = $atributos_adicionados; $i < 25; $i++) {
                    $rowData[] = ''; // Atributo vazio
                    $rowData[] = ''; // Valor vazio
                }
            }
    
            // Mapeia o array de dados para o formato esperado pelo writer (campo => valor)
            $finalRow = [];
            foreach ($headerRow as $key => $colInfo) {
                if (isset($colInfo['field'])) {
                    // Usa o nome do campo do cabeçalho como chave
                    $finalRow[$colInfo['field']] = isset($rowData[$key]) ? $rowData[$key] : '';
                }
            }
    
            $writer->writeSheetRow($config['nome_planilha'], $finalRow, $defaultStyle);
        }
    
        return $relatorio->download();
    }
    
    private function filterUniqueAttributes($inputArray)
    {
        $attributeMap = [];

        foreach ($inputArray as $item) {
            $attribute = $item->atributo;
            $attributeMap[$attribute] = $item;
        }

        $resultArray = array_values($attributeMap);

        usort($resultArray, function ($a, $b) {
            return strtotime($b->atualizado_em) - strtotime($a->atualizado_em);
        });

        return $resultArray;
    }


    public function exportar_paises()
    {
        ini_set('memory_limit', -1);

        $status_cad_item_homolog = array(
            0 => 'Reprovado',
            1 => 'Homologado',
            2 => 'Obsoleto'
        );

        $this->load->model(
            array(
                'empresa_model',
                'item_pais_model',
                'cad_item_model',
                'usuario_homolog_bulk_model'
            )
        );

        $result = $this->usuario_homolog_bulk_model->get_entry();

        $idItens = array();

        if ($result) $idItens = unserialize($result->data);

        $company_can_lessin = company_can('lessin');

        if ($company_can_lessin) {
            $this->cad_item_model->set_state("filter.lessin", true);
        }

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode('|', $empresa->campos_adicionais);

        $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais) ? true : false;

        $itens = $this->cad_item_model->get_homolog_data_xls($idItens);
        $logs = $this->item_pais_model->get_entries_for_xls(sess_user_company());

        // echo '<pre>';
        //  var_dump($itens->result());
        //  var_dump($logs->result());
        //  die;

        $headerRow = array(
            array(
                'label' => 'PART NUMBER BRASIL',
                'field' => 'part_number',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'ESTABELECIMENTO',
                'field' => 'estabelecimento',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DESCRIÇÃO',
                'field' => 'descricao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'SIGLA DO PAÍS',
                'field' => 'sigla',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'CÓDIGO DO PAÍS',
                'field' => 'codigo',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'NOME DO PAÍS',
                'field' => 'nome',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'CÓDIGO CLASSIFICAÇÃO',
                'field' => 'codigo_classificacao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DESCRIÇÃO CURTA',
                'field' => 'descricao_curta',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DESCRIÇÃO COMPLETA',
                'field' => 'descricao_completa',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'L.I.',
                'field' => 'li',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'INFORMAÇÃO ADICIONAL',
                'field' => 'informacao_adicional',
                'col_format' => 'texto',
                'col_width' => 30
            )

        );



        $cabecalho = $this->extract_fields_from_cols($headerRow);

        $config = array(
            'filename'      => "paises_homologacao_" . date('Y-m-d_H-i-s'),
            'titulo'        => "Paises Homologacao",
            'nome_planilha' => "Paises Homologacao",
            'colunas'       => $headerRow,
            'cabecalho'     => $cabecalho,
            'filter_suffix' => "HOM_"
        );

        require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';
        $relatorio = new XlsxGenerator($config['filename']);
        $writer = $relatorio->init($config['filename']);

        $relatorio->filename = $config['filename'];

        // Estrutura XLSX
        $widths = $relatorio->getWidth($config);
        $fields = array();

        $writer->addImage(FCPATH . 'assets/img/header/logo.png', 1, array('widths' => $widths));

        if (isset($config['colunas'])) {
            foreach ($config['colunas'] as $coluna) {
                if (isset($coluna['label']))
                    $fields[$coluna['label']] = isset($coluna['col_format']) ? $coluna['col_format'] : 'string';
            }
        }

        $headerStyle = array(
            'font' => 'Arial',
            'font-size' => 12,
            'font-style' => 'bold',
            'color' => '#000000',
            'fill' => '#F9FF00',
            'halign' => 'center',
            'valign' => 'center',
            'border' => 'bottom',
            'wrap_text' => 'true',
            'widths' => $widths
        );


        $writer->writeSheetHeader($config['nome_planilha'], $fields, $headerStyle);

        if (isset($config['colunas']) &&  (count($config['colunas']) <= 24) && (count($config['colunas']) >= 10)) {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
        } else if (isset($config['colunas']) && count($config['colunas']) <= 24) {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, 24);
        } else {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
        }

        $defaultStyle = array(
            'font' => 'Arial',
            'font-size' => 11
        );

        $itemRows = array();

        $logsbuff = $logs->result();

        $k = 0;
        $key = 0;

        while ($item = $itens->unbuffered_row()) {

            $tem_paises = 0;
            foreach ($logsbuff as $log) {

                if ($item->part_number == $log->part_number) {

                    $key++;
                    $k = $key + 1;

                    $itemRows[$k] = array(
                        $log->part_number,
                        $log->estabelecimento,
                        $item->descricao_mercado_local,
                        $log->sigla,
                        $log->codigo,
                        $log->nome,
                        $log->codigo_classificacao,
                        $log->descricao_curta,
                        $log->descricao_completa,
                        $log->li,
                        $log->informacao_adicional
                    );
                    $tem_paises++;
                }
            }
            if ($tem_paises == 0) {
                $key++;
                $k = $key + 1;
                $itemRows[$k] = array(
                    $item->part_number,
                    $item->estabelecimento,
                    $item->descricao_mercado_local,
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - '
                );
            }
        }


        $k = 2;
        foreach ($itemRows as $item) {
            $row = array();
            foreach ($headerRow as $i => $value) {
                $row += array($headerRow[$i]['field'] => $itemRows[$k][$i]);
            }
            $k++;
            $writer->writeSheetRow($config['nome_planilha'], $row, $defaultStyle);
        }

        return $relatorio->download();
    }

    private function num2alpha($n)
    {
        for ($r = ""; $n >= 0; $n = intval($n / 26) - 1) {
            $r = chr($n % 26 + 0x41) . $r;
        }

        return $r;
    }

    public function verificar_itens_exportados()
    {
        $itens_exportados = array();
        try {
            $data = $this->apply_default_filters();

            ini_set('memory_limit', -1);

            $this->load->model('usuario_homolog_bulk_model');

            $result = $this->usuario_homolog_bulk_model->get_entry();

            $idItens = array();

            if ($result) $idItens = unserialize($result->data);

            $itens = $this->cad_item_model->get_planilha_upload_data_xls(!empty($idItens) ? $idItens : array());

            foreach ($itens as $item) {
                if ($item->status_exportacao) {
                    $itens_exportados[] = $item;
                }
            }

            return response_json(array(
                'status' => 200,
                'message' => '',
                'data' => array(
                    'exportados' => !empty($itens_exportados) ? TRUE : FALSE,
                    'itens_exportados' => $itens_exportados
                )
            ), 200);
        } catch (Exception $e) {
            return  response_json(array(
                'status' => 500,
                'message' => 'Erro ao verificar se já existem itens homologados.'
            ), 500);
        }
    }

    public function exportar_planilha_upload()
    {
        $this->load->model('empresa_model');
        ini_set('memory_limit', -1);
        $this->load->model('usuario_homolog_bulk_model');

        $result = $this->usuario_homolog_bulk_model->get_entry();

        $idItens = array();

        if ($result) $idItens = unserialize($result->data);

        $itens = $this->cad_item_model->get_planilha_upload_data_xls(!empty($idItens) ? $idItens : array());

        if (count($itens) == 0) show_404();

        $this->load->library('Excel');
        $this->load->helper('text_helper');

        $filename = 'planilha_upload_' . date('Y-m-d_H-i-s') . '.xlsx';

        $this->excel->setActiveSheetIndex(0);

        $sheet = $this->excel->getActiveSheet();
        $sheet->setTitle('Planilha de Upload de Itens');

        // Header
        $headerRow = array(
            'CÓDIGO DO PRODUTO',
            'GRUPO TARIFÁRIO',
            'NCM PROPOSTO',
            'NCM FORNECEDOR',
            'DESCRIÇÃO RESUMIDA',
            'DESCRIÇÃO PROPOSTA COMPLETA',
            'EVENTO',
            'FUNÇÃO',
            'APLICAÇÃO',
            'MARCA',
            'MATERIAL CONSTITUTIVO',
            'ESTABELECIMENTO',
            'MEMÓRIA DE CLASSIFICAÇÃO',
            'INFORMAÇÕES ADICIONAIS',
        );

        $funcoes_adicionais = $this->empresa_model->get_funcoes_adicionais(null, sess_user_company());
        $customer_can_suframa = customer_can('suframa');
        $customer_can_ex_ipi = customer_can('ipi');
        $customer_can_ex_ii = customer_can('ii');
        $customer_can_nve = customer_can('nve');
        $customer_can_ncm_fornecedor = customer_can('ncm_fornecedor');

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode('|', $empresa->campos_adicionais);

        $hasDrawback = in_array('controle_drawback', $campos_adicionais) ? true : false;

        // $customer_can_cest = customer_can('vinculacao_cest');


        if ($customer_can_nve) {
            array_push($headerRow, 'NVE ATRIBUTO AA');
            array_push($headerRow, 'NVE VALOR AA');
            array_push($headerRow, 'NVE ATRIBUTO AB');
            array_push($headerRow, 'NVE VALOR AB');
            array_push($headerRow, 'NVE ATRIBUTO AC');
            array_push($headerRow, 'NVE VALOR AC');
            array_push($headerRow, 'NVE ATRIBUTO AD');
            array_push($headerRow, 'NVE VALOR AD');
            array_push($headerRow, 'NVE ATRIBUTO AE');
            array_push($headerRow, 'NVE VALOR AE');
            array_push($headerRow, 'NVE ATRIBUTO AF');
            array_push($headerRow, 'NVE VALOR AF');
            array_push($headerRow, 'NVE ATRIBUTO AG');
            array_push($headerRow, 'NVE VALOR AG');
            array_push($headerRow, 'NVE ATRIBUTO AH');
            array_push($headerRow, 'NVE VALOR AH');
        }

        array_push($headerRow, 'LICENCIAMENTO NÃO AUTOMÁTICO');
        array_push($headerRow, 'ORGÃO ANUENTE');
        array_push($headerRow, 'DESTAQUE LI');
        array_push($headerRow, 'ANTIDUMPING ATIVO');

        if ($customer_can_suframa) {
            array_push($headerRow, 'CÓDIGO PRODUTO SUFRAMA');
            array_push($headerRow, 'DESTAQUE SUFRAMA');
            array_push($headerRow, 'DESCRIÇÃO SUFRAMA');
            array_push($headerRow, 'PPB SUFRAMA');
        }

        if ($customer_can_ex_ipi) {
            array_push($headerRow, 'EX-IPI');
            array_push($headerRow, 'TEXTO DO EX-IPI');
        }

        if ($customer_can_ex_ii) {
            array_push($headerRow, 'EX-II');
            array_push($headerRow, 'TEXTO DO EX-II');
        }

        if ($hasDrawback) {
            array_push($headerRow, 'CONTROLE DE DRAWBACK');
        }

        $sheet->fromArray($headerRow, NULL, 'A1');

        // Plotagem dos dados
        $itemRows = array();

        $this->load->model(array(
            'grupo_tarifario_model',
            'ex_tarifario_model',
            'nve_atributo_model',
            'cad_item_nve_model'
        ));

        $show_id_grupo_tarifario = false;
        if (in_array('exibir_id_gpt', $funcoes_adicionais)) {
            $show_id_grupo_tarifario = true;
        }

        $atualizar = array();

        foreach ($itens as $k => $item) {

            $atualizar[] = $item->id_item;

            // $grupo_tarifario = $this->grupo_tarifario_model->get_entry($item->id_grupo_tarifario);
            $item_ex_ii = false;
            if (!empty($item->num_ex_ii) && $customer_can_ex_ii) {
                $item_ex_ii = $this->ex_tarifario_model->get_ex_ii_by_ncm($item->num_ex_ii, $item->ncm_proposto);
            }

            $item_ex_ipi = false;
            if (!empty($item->num_ex_ipi) && $customer_can_ex_ipi) {
                $item_ex_ipi = $this->ex_tarifario_model->get_ex_ipi_by_ncm($item->num_ex_ipi, $item->ncm_proposto);
            }

            $nve_entries = false;
            if ($customer_can_nve) $nve_entries = $this->cad_item_nve_model->get_entries($item->id_item);

            $cod_cest = '';

            if ($item->cod_cest !== null) {
                if ($item->cod_cest == '-1') {
                    $cod_cest = 'N/D - Não atende';
                } else {
                    $cod_cest = $item->cod_cest;
                }
            }

            $itemRows[$k] = array(
                $item->part_number,
                $show_id_grupo_tarifario ? $item->id_grupo_tarifario : $item->grupo_tarifario,
                $item->ncm_proposto,
                $customer_can_ncm_fornecedor ? $item->ncm_fornecedor : '',
                $item->descricao_mercado_local,
                $item->descricao_proposta_completa,
                $item->evento,
                $item->funcao,
                $item->aplicacao,
                $item->marca,
                $item->material_constitutivo,
                $item->estabelecimento,
                $item->memoria_classificacao,
                $item->inf_adicionais,
            );

            if ($customer_can_nve) {

                array_push($itemRows[$k], isset($nve_entries[0]->nve_atributo) ? $nve_entries[0]->nve_atributo : '');
                array_push($itemRows[$k], isset($nve_entries[0]->nve_valor) ? $nve_entries[0]->nve_valor : '');

                array_push($itemRows[$k], isset($nve_entries[1]->nve_atributo) ? $nve_entries[1]->nve_atributo : '');
                array_push($itemRows[$k], isset($nve_entries[1]->nve_valor) ? $nve_entries[1]->nve_valor : '');

                array_push($itemRows[$k], isset($nve_entries[2]->nve_atributo) ? $nve_entries[2]->nve_atributo : '');
                array_push($itemRows[$k], isset($nve_entries[2]->nve_valor) ? $nve_entries[2]->nve_valor : '');

                array_push($itemRows[$k], isset($nve_entries[3]->nve_atributo) ? $nve_entries[3]->nve_atributo : '');
                array_push($itemRows[$k], isset($nve_entries[3]->nve_valor) ? $nve_entries[3]->nve_valor : '');

                array_push($itemRows[$k], isset($nve_entries[4]->nve_atributo) ? $nve_entries[4]->nve_atributo : '');
                array_push($itemRows[$k], isset($nve_entries[4]->nve_valor) ? $nve_entries[4]->nve_valor : '');

                array_push($itemRows[$k], isset($nve_entries[5]->nve_atributo) ? $nve_entries[5]->nve_atributo : '');
                array_push($itemRows[$k], isset($nve_entries[5]->nve_valor) ? $nve_entries[5]->nve_valor : '');

                array_push($itemRows[$k], isset($nve_entries[6]->nve_atributo) ? $nve_entries[6]->nve_atributo : '');
                array_push($itemRows[$k], isset($nve_entries[6]->nve_valor) ? $nve_entries[6]->nve_valor : '');

                array_push($itemRows[$k], isset($nve_entries[7]->nve_atributo) ? $nve_entries[7]->nve_atributo : '');
                array_push($itemRows[$k], isset($nve_entries[7]->nve_valor) ? $nve_entries[7]->nve_valor : '');
            }

            array_push($itemRows[$k], !empty($item->li) ? $item->li : '');
            array_push($itemRows[$k], !empty($item->li_orgao_anuente) ? $item->li_orgao_anuente : '');
            array_push($itemRows[$k], !empty($item->li_destaque) ? $item->li_destaque : '');
            array_push($itemRows[$k], !empty($item->antidumping) ? $item->antidumping : '');

            if ($customer_can_suframa) {
                array_push($itemRows[$k], $item->suframa_codigo);
                array_push($itemRows[$k], $item->suframa_destaque);
                array_push($itemRows[$k], $item->suframa_descricao);
                array_push($itemRows[$k], $item->suframa_ppb);
            }

            if ($customer_can_ex_ipi) {
                array_push($itemRows[$k], $item_ex_ipi ? $item_ex_ipi->num_ex : '');
                array_push($itemRows[$k], $item->num_ex_ipi == "-1" || $item->num_ex_ipi == -1 ? 'ITEM NÃO ATENDE EX' : ($item_ex_ipi ? $item_ex_ipi->descricao_linha1 : ""));
            }

            if ($customer_can_ex_ii) {
                array_push($itemRows[$k], $item_ex_ii ? $item_ex_ii->num_ex : '');
                array_push($itemRows[$k], $item->num_ex_ii == "-1" || $item->num_ex_ii == -1 ? 'ITEM NÃO ATENDE EX' : ($item_ex_ii ? $item_ex_ii->descricao_linha1 : ""));
            }
            if ($hasDrawback) {
                array_push($itemRows[$k], isset($item->is_drawback) ? ($item->is_drawback == 1 ? 'Sim' : 'Não') : '');
            }
        }

        // Corrige HTML Chars
        array_walk_recursive($itemRows, function (&$item) {
            $item = html_entity_decode($item, ENT_QUOTES);
        });

        // Inserindo os dados
        list($startColumn, $startRow) = PHPExcel_Cell::coordinateFromString('A2');

        foreach ($itemRows as $rowData) {
            $currentColumn = $startColumn;

            foreach ($rowData as $cellValue) {
                $sheet->setCellValueExplicit($currentColumn . $startRow, $cellValue, PHPExcel_Cell_DataType::TYPE_STRING);
                ++$currentColumn;
            }

            ++$startRow;
        }

        // Cores
        $sheet->getStyle('A1:' . $this->num2alpha(count($headerRow) - 1) . '1')->applyFromArray(
            array(
                'fill' => array(
                    'type' => PHPExcel_Style_Fill::FILL_SOLID,
                    'color' => array('rgb' => '16365C')
                ),
                'font'  => array(
                    'bold'  => true,
                    'color' => array('rgb' => 'FFFFFF'),
                )
            )
        );

        // Tamanho das colunas
        $sheet->getDefaultColumnDimension()->setWidth('40');
        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('J')->setWidth(20);
        $sheet->getColumnDimension('M')->setWidth(20);
        $sheet->getColumnDimension('N')->setWidth(20);
        $sheet->getColumnDimension('O')->setWidth(20);
        $sheet->getColumnDimension('P')->setWidth(20);
        $sheet->getColumnDimension('Q')->setWidth(20);
        $sheet->getColumnDimension('R')->setWidth(20);
        $sheet->getColumnDimension('S')->setWidth(20);
        $sheet->getColumnDimension('T')->setWidth(20);
        $sheet->getColumnDimension('U')->setWidth(20);
        $sheet->getColumnDimension('V')->setWidth(20);
        $sheet->getColumnDimension('W')->setWidth(20);
        $sheet->getColumnDimension('X')->setWidth(20);
        $sheet->getColumnDimension('Y')->setWidth(20);
        $sheet->getColumnDimension('Z')->setWidth(20);
        $sheet->getColumnDimension('AA')->setWidth(20);
        $sheet->getColumnDimension('AB')->setWidth(20);
        $sheet->getColumnDimension('AG')->setWidth(30);
        $sheet->getColumnDimension('AH')->setWidth(20);
        $sheet->getColumnDimension('AJ')->setWidth(20);
        $sheet->getColumnDimension('AK')->setWidth(20);
        $sheet->getColumnDimension('AM')->setWidth(20);

        if (in_array('status_exportacao', $funcoes_adicionais)) {
            $selected_itens = [];

            foreach ($itens as $item) {
                $selected_itens[] = [
                    'id_item'         => $item->id_item,
                    'part_number'     => $item->part_number,
                    'estabelecimento' => $item->estabelecimento,
                ];
            }

            $this->cad_item_model->atualiza_status_exportacao_homologacao(sess_user_company(), 1, $selected_itens);
        }

        if (!$customer_can_ncm_fornecedor) {
            $sheet->removeColumn('D');
        }

        $writer = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
    }

    public function lista_impacto()
    {
        $this->load->model(array('empresa_model', "cad_item_model"));

        ini_set('memory_limit', -1);
        set_time_limit(0);

        $empresas = $this->empresa_model->get_entries();

        $statusExportacao = array("0", "1");
        $statusImplementacao = array("I", "N", "R");

        $homologacoes = array();

        $colunas = array(
            array(
                'label' => 'Empresa',
                'field' => 'razao_social',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Código do Produto',
                'field' => 'part_number',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Descrição Proposta Resumida',
                'field' => 'descricao_mercado_local',
                'col_format' => 'texto',
                'col_width' => 60
            ),
            array(
                'label' => 'NCM Proposto',
                'field' => 'ncm_proposto',
                'col_format' => 'texto',
                'col_width' => 30
            )
        );

        $cabecalho = $this->extract_fields_from_cols($colunas);

        $config = array(
            'filename'      => 'lista_impacto_' . date('Y-m-d_H-i-s'),
            'titulo'        => "Lista de Impacto",
            'nome_planilha' => "Lista de Impacto",
            'colunas'       => $colunas,
            'cabecalho'     => $cabecalho,
            'filter_suffix' => "lista_impacto"
        );

        require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';

        $relatorio = new XlsxGenerator($config['filename']);
        $writer = $relatorio->init($config['filename']);

        $relatorio->filename = $config['filename'];

        // Estrutura XLSX
        $widths = $relatorio->getWidth($config);
        $fields = array();

        $writer->addImage(FCPATH . 'assets/img/header/logo.png', 1, array('widths' => $widths));

        if (isset($config['colunas'])) {
            foreach ($config['colunas'] as $coluna) {
                if (isset($coluna['label']))
                    $fields[$coluna['label']] = isset($coluna['col_format']) ? $coluna['col_format'] : 'string';
            }
        }

        $headerStyle = array(
            'font' => 'Arial',
            'font-size' => 12,
            'font-style' => 'bold',
            'color' => '#000000',
            'fill' => '#F9FF00',
            'halign' => 'center',
            'valign' => 'center',
            'border' => 'bottom',
            'wrap_text' => 'true',
            'widths' => $widths
        );

        $writer->writeSheetHeader($config['nome_planilha'], $fields, $headerStyle);

        if (isset($config['colunas']) &&  (count($config['colunas']) <= 24) && (count($config['colunas']) >= 10)) {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
        } else if (isset($config['colunas']) && count($config['colunas']) <= 24) {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, 24);
        } else {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
        }

        $defaultStyle = array(
            'font' => 'Arial',
            'font-size' => 11
        );
        // Estrutura XLSX

        foreach ($empresas as $empresa) {
            $homologacoes = $this->empresa_model->get_homologacao_by_id_empresa($empresa->id_empresa);

            $this->cad_item_model->unset_state('filter.list_opt');
            $this->cad_item_model->set_state('filter.list_opt', "homologado");

            $this->cad_item_model->set_state('filter.habilitar_pr', false);

            $this->cad_item_model->set_state('filter.atribuido_para', '-1');

            $this->cad_item_model->set_state('filter.status_exportacao', $statusExportacao);

            $this->cad_item_model->set_state('filter.status_implementacao', $statusImplementacao);

            $this->cad_item_model->set_state('filter.id_empresa', $empresa->id_empresa);

            $this->cad_item_model->set_state('filter.use_unbuffered_query', TRUE);

            $query = $this->cad_item_model->get_entries(NULL, NULL, FALSE, TRUE, $homologacoes, TRUE);

            while ($v = $query->unbuffered_row()) {
                $row = array(
                    'razao_social' => $v->razao_social,
                    'part_number' => $v->part_number,
                    'descricao_mercado_local' => $v->descricao_mercado_local,
                    'ncm_proposto' => $v->ncm_proposto
                );

                $writer->writeSheetRow($config['nome_planilha'], $row, $defaultStyle);
            }

            $homologacoes = array();
        }

        return $relatorio->download();
    }

    private function extract_fields_from_cols($cols, $line_break = ' ', $label_key_override = 'label_exportar')
    {
        if (empty($cols)) {
            return array();
        }

        $fields = array();

        foreach ($cols as $col) {
            $label_key = 'label';

            if (!empty($label_key_override) && array_key_exists($label_key_override, $col)) {
                $label_key = $label_key_override;
            }

            if (!empty($line_break)) {
                $col[$label_key] = str_replace('<br>', $line_break, $col[$label_key]);
            }

            $fields[$col['field']] = $col[$label_key];
        }

        return $fields;
    }

    public function apply_default_filters()
    {
        if ($this->input->is_set('reset_filters')) {
            $this->cad_item_model->set_state_store_session(TRUE);
            $this->cad_item_model->clear_states();
        } else {
            $this->cad_item_model->set_state_store_session(TRUE);
            $this->cad_item_model->restore_state_from_session('filter.', 'post');
        }

        if ($this->cad_item_model->get_state('filter.list_opt')) {
            $this->cad_item_model->set_state('filter.list_opt', $this->input->post('list_opt'));
        } else {
            $this->cad_item_model->set_state('filter.list_opt', 'homologar');
        }

        // somente as aprovações do usuário (logado)
        if (!$this->cad_item_model->get_state('filter.atribuido_para')) {
            $this->cad_item_model->set_state('filter.atribuido_para', sess_user_id());
        }

        if ($this->input->is_set('atribuido_para')) {
            $this->cad_item_model->set_state('filter.atribuido_para', $this->input->post('atribuido_para'));
        }

        if ($this->input->is_set('part_numbers')) {
            $part_numbers = $this->input->post('part_numbers');

            if (!is_array($part_numbers) && !empty($part_numbers)) {
                $this->cad_item_model->set_state('filter.part_numbers_view', $part_numbers);

                $generic_part_numbers = array();

                if (strpos($part_numbers, '%') !== false) {
                } else {
                    $separator = get_company_separator(sess_user_company());
                    if (strpos($part_numbers, "\n") !== false) {
                        $separator = !empty($separator) ? $separator : "\n";
                    }
                    $part_numbers = str_replace(array("\t", "\r\n", "\s", "\n"), $separator, $this->input->post('part_numbers'));

                    $matches = array();
                    preg_match_all('/"([^"]*)"/', $part_numbers, $matches);
                    $btw_quotes = $matches[1];

                    $part_numbers = str_replace("*", "%", $part_numbers);
                    $part_numbers = preg_replace('/"([^"]*)"/', "", $part_numbers);

                    //Acha todos os partnumbers entre aspas simples
                    $matches_simple = array();
                    preg_match_all('~\'(.*?)\'~', $part_numbers, $matches_simple);
                    $btw_simple_quotes = $matches_simple[1];

                    //Retira da string todos os partnumbers entre aspas simples
                    $part_numbers = preg_replace('~\'(.*?)\'~', "", $part_numbers);

                    $part_numbers = !empty($separator) ? explode($separator, addslashes($part_numbers)) : [$part_numbers];
                    $part_numbers = array_filter($part_numbers);

                    if (!empty($btw_quotes)) {
                        $addslashes_btw_quotes = implode(',', $btw_quotes);
                        $btw_quotes = explode(",", addslashes($addslashes_btw_quotes));
                    }

                    $part_numbers = array_merge($part_numbers, $btw_quotes);
                    $part_numbers = array_merge($part_numbers, $btw_simple_quotes);


                    foreach ($part_numbers as $key => $part_number) {
                        if (strpos($part_number, "%")) {
                            $generic_part_numbers[] = $part_number;
                            unset($part_numbers[$key]);
                        }
                    }
                }

                if (!empty($part_numbers)) {
                    $this->cad_item_model->set_state('filter.part_numbers', $part_numbers);
                } else {
                    $this->cad_item_model->unset_state('filter.part_numbers');
                }

                if (!empty($generic_part_numbers)) {
                    $this->cad_item_model->set_state('filter.generic_part_numbers', $generic_part_numbers);
                } else {
                    $this->cad_item_model->unset_state('filter.generic_part_numbers', $generic_part_numbers);
                }
            } else {
                $this->cad_item_model->unset_state('filter.part_numbers_view');
                $this->cad_item_model->unset_state('filter.part_numbers');
                $this->cad_item_model->unset_state('filter.generic_part_numbers');
            }
        }

        $this->cad_item_model->set_state('filter.id_empresa', sess_user_company());

        // Filtro: Tipo de Pesquisa
        if ($busca_part_number = $this->input->post('busca_part_number')) {
            $this->cad_item_model->set_state('filter.busca_part_number', $busca_part_number);
        } else {
            $this->cad_item_model->unset_state('filter.busca_part_number');
        }

        if ($this->input->post('evento') && count($this->input->post('evento')) > 0) {
            $evento = $this->input->post('evento');
            if (is_array($evento) && $evento[0] != "") {
                $this->cad_item_model->set_state('filter.evento', $evento);
            } else {
                $this->cad_item_model->unset_state('filter.evento');
            }
        } else {
            $this->cad_item_model->unset_state('filter.evento');
        }

        if ($busca_descricao = $this->input->post('busca_descricao')) {
            $this->cad_item_model->set_state('filter.busca_descricao', $busca_descricao);
        } else {
            $this->cad_item_model->unset_state('filter.busca_descricao');
        }

        if (!$busca_part_number && !$busca_descricao) {
            $this->cad_item_model->set_state('filter.busca_part_number', TRUE);
            $_POST['busca_part_number'] = 1;
        }

        if (!has_role('sysadmin') && !has_role('consultor')) {
            $this->cad_item_model->set_state('filter.has_descricao_mercado_local', TRUE);
        } else {
            $this->cad_item_model->unset_state('filter.has_descricao_mercado_local');
        }

        // Filtro: Status Implementação
        $data['status_implementacao'] = array('R', 'I', 'N');

        if ($this->input->is_set('status_implementacao')) {
            $this->cad_item_model->set_state('filter.status_implementacao', $this->input->post('status_implementacao'));
            $data['status_implementacao'] = $this->input->post('status_implementacao');
        }

        // Filtro: Status Implementação
        $data['status_exportacao'] = array(0, 1);

        if ($this->input->is_set('status_exportacao')) {
            $this->cad_item_model->set_state('filter.status_exportacao', $this->input->post('status_exportacao'));
            $data['status_exportacao'] = $this->input->post('status_exportacao');
        }

        // Filtro: Simplus
        $data['status_simplus'] = array(0, 1);

        if ($this->input->is_set('status_simplus')) {
            $this->cad_item_model->set_state('filter.status_simplus', $this->input->post('status_simplus'));
            $data['status_simplus'] = $this->input->post('status_simplus');
        }
 
        if ($data_inicio_importado_modal = $this->input->post('data_inicio_importado_modal')) {
            $this->cad_item_model->set_state('filter.data_inicio_importado_modal', $data_inicio_importado_modal);
        } else {
            $this->cad_item_model->unset_state('filter.data_inicio_importado_modal');
        }

        if ($data_fim_importado_modal = $this->input->post('data_fim_importado_modal')) {
            $this->cad_item_model->set_state('filter.data_fim_importado_modal', $data_fim_importado_modal);
        } else {
            $this->cad_item_model->unset_state('filter.data_fim_importado_modal');
        }


        // Filtro: Grupo Tarifário


        if ($id_grupo_tarifario = $this->cad_item_model->get_state('filter.id_grupo_tarifario')) {
            $data['id_grupo_tarifario'] = $id_grupo_tarifario;
        } else {
            $data['id_grupo_tarifario'] = NULL;
        }

        if ($post_grupo_tarifario = $this->input->post('id_grupo_tarifario')) {
            if ($post_grupo_tarifario == -1) {
                $this->cad_item_model->unset_state('filter.id_grupo_tarifario');
                $data['id_grupo_tarifario'] = -1;
            } else {
                // Valida se o grupo tarifário pesquisado
                // ainda é válido para a próxima pesquisa
                $find_grupo_tarifario = false;

                $grupos_tarifarios = $this->cad_item_model->get_grupos_tarifarios();

                foreach ($grupos_tarifarios as $struct) {
                    if ($post_grupo_tarifario == $struct->id_grupo_tarifario) {
                        $find_grupo_tarifario = $struct;
                        break;
                    }
                }

                if ($find_grupo_tarifario) {
                    $this->cad_item_model->set_state('filter.id_grupo_tarifario', $post_grupo_tarifario);
                    $data['id_grupo_tarifario'] = $post_grupo_tarifario;
                } else {
                    $data['id_grupo_tarifario'] = -1;
                }
            }
        } else {
            $this->cad_item_model->unset_state('filter.id_grupo_tarifario');
        }

        $data['prioridades_filter'] = is_array($this->input->post('prioridade')) ? $this->input->post('prioridade') : array();

        if (!in_array("none", $data['prioridades_filter'])) {
            $this->cad_item_model->set_state('filter.prioridade', $data['prioridades_filter']);
        } else {
            $this->cad_item_model->unset_state('filter.prioridade');
        }

        return $data;
    }

    /* Bulk Selection */
    public function ajax_bulk_select_all()
    {
        $data = array();

        $this->load->model('empresa_model');

        $id_empresa = sess_user_company();
        $homologacoes = $this->empresa_model->get_homologacao_by_id_empresa($id_empresa);

        /* Cad_item */
        $this->apply_default_filters();
        $itens = $this->cad_item_model->get_entries(NULL, NULL, FALSE, FALSE, $homologacoes, TRUE);

        /* Usuario_Bulk */
        $result = $this->usuario_homolog_bulk_model->get_entry();

        if ($result) {
            $data = unserialize($result->data);
        }

        $data_map = array_flip($data); // Create a map for quick lookup

        foreach ($itens as $item) {
            if ($result) {
                if (!isset($data_map[$item->id_item])) {
                    $data[] = $item->id_item;
                    $data_map[$item->id_item] = true;
                }
            } else {
                $data[] = $item->id_item;
            }
        }

        $dbdata = array(
            'id_usuario' => sess_user_id(),
            'id_empresa' => sess_user_company(),
            'data' => $data
        );

        $this->usuario_homolog_bulk_model->save($dbdata);

        $return = array(
            'total_items' => count($data),
            'select_all'  => false
        );

        echo json_encode($return);
    }

    public function ajax_bulk_get()
    {
        $data = array();

        $result = $this->db->usuario_homolog_bulk_model->get_entry();

        $data = array();

        if ($result) {
            $data = unserialize($result->data);
        }

        echo json_encode($data);
    }

    public function ajax_bulk_save()
    {
        if ($items = $this->input->post('items')) {
            $action = $this->input->post('action');
            $data = array();

            $result = $this->usuario_homolog_bulk_model->get_entry();

            if ($result) {
                $data = unserialize($result->data);

                foreach ($items as $item) {
                    if ($action == 'rm') {
                        if (($k = array_search($item, $data)) !== false) {
                            unset($data[$k]);
                        }
                    } else {
                        if (($k = array_search($item, $data)) === false) {
                            $data[] = $item;
                        }
                    }
                }
            } else {
                $data = $items;
            }

            /*$this->apply_default_filters();

            $lista_cad_item = array();

            $this->apply_default_filters();
            $cad_item_list = $this->cad_item_model->get_all_entries();

            foreach ($cad_item_list as $cad_item) {
                $lista_cad_item[] = $cad_item->id_item;
            }

            $bulk_select_all = (!(count(array_intersect($data, $lista_cad_item)) == count($lista_cad_item)));*/

            $dbdata = array(
                'id_usuario' => sess_user_id(),
                'id_empresa' => sess_user_company(),
                'data' => $data
            );

            $this->usuario_homolog_bulk_model->save($dbdata);

            $return = array(
                'total_items' => count($data),
                'select_all'  => true //$bulk_select_all
            );

            echo json_encode($return);
            exit;
        }

        echo json_encode(array());
    }

    public function bulk_clean()
    {
        return $this->usuario_homolog_bulk_model->clean();
    }
    /**/

    public function get_ex_ii_ipi_by_id_item($id_item)
    {
        $this->load->model('ex_tarifario_model');

        try {
            $item = $this->cad_item_model->get_entry($id_item);
        } catch (Exception $e) {
            show_404();
        }

        if (!empty($item->num_ex_ii) && $item->num_ex_ii != '-1') {
            $data['item_ex_ii'] = $this->ex_tarifario_model->get_ex_ii_by_ncm($item->num_ex_ii, $item->ncm_proposto);
        }

        if (!empty($item->num_ex_ipi)  && $item->num_ex_ipi != '-1') {
            $data['item_ex_ipi'] = $this->ex_tarifario_model->get_ex_ipi_by_ncm($item->num_ex_ipi, $item->ncm_proposto);
        }

        return $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($data));
    }

    public function ficha($id_item)
    {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        if (!has_role('homologacao_ficha')) {
            show_permission();
        }

        $data = array();

        $this->load->helper('formatador_helper');
        $this->load->model(array(
            'ncm_model',
            'cad_item_wf_atributo_model',
            'ncm_capitulo_model',
            'ncm_capitulo_grupos_model',
            'cad_item_homologacao_model',
            'nve_atributo_model',
            'grupo_tarifario_model',
            'cad_item_nve_model',
            'cest_model',
            'anexo_model',
            'ex_tarifario_model',
            'empresa_model',
            'suframa_model',
            'cad_item_attr_model',
            'catalogo/produto_model',
            'owner_model',
            'empresa_prioridades_model',
            'comex_model'
        ));
        $this->cad_item_attr_model->set_state('filter.vinculacao', false);
        // $id_empresa = $this->usuario_model->get_user_company(sess_user_id());
        $id_empresa = sess_user_company();

        $this->cad_item_model->set_state('filter.id_empresa', $id_empresa);

        $this->title = "Ficha de Homologação";

        $this->load->library("Item/LessinUseCases");

        try {

            $item = $this->cad_item_model->get_entry($id_item);
            $item->peso = format_peso($item->peso);
            $item->importado = $this->comex_model->check_imported($item->part_number, $id_empresa, $item->estabelecimento);
        } catch (Exception $e) {
            show_404();
        }

        // #4433 - FUNÇÃO DE TRANSFERENCIA DE USUÁRIO
        // Precisamos que quando o Item ainda estiver em REVISÃO, não poderá aparecer na tela de HOMOLOGAÇÃO.
        // -Andrei
        if (!has_role('admin') && !has_role('consultor') && $item->v_item_status == 'pendente_revisao') {
            show_error('Item ainda não disponível para homologação.', 500, 'Erro');
        }

        if ($this->input->post('submit')) {
            if (has_role('engenheiro') && company_can('homologacao_engenharia')) {
                $tipo_homologacao = 'Engenharia';
            } else if (has_role('fiscal') && company_can('homologacao_fiscal')) {
                $tipo_homologacao = 'Fiscal';
            } else {
                $this->message_next_render('<h4>Ooops!</h4> Perfil do usuário não tem permissão de Engenheiro ou Fiscal e/ou a empresa não possui esta permissão.', 'error');
                redirect('homologacao/ficha/' . $id_item);
            }

            $id_usuario = sess_user_id();
            $is_homologado = (int) $this->input->post('homologado');
            $motivo = '';

            if ($is_homologado == 2) {
                $id_motivo_inativo = $this->input->post('id_motivo_inativo');

                if ($id_motivo_inativo !== '') {
                    $motivo_inativo = $this->motivo_model->get_entry($id_motivo_inativo);
                    $motivo = $motivo_inativo->motivo;
                } else {
                    $motivo = $this->input->post('motivo');
                }
            } else {
                $motivo = $this->input->post('motivo');
            }

            $this->load->model('empresa_model');
            $homologacoes = $this->empresa_model->get_homologacao_by_id_empresa($id_empresa);

            $this->cad_item_model->set_item_homologado($id_item, $tipo_homologacao, $id_usuario, $is_homologado, $motivo, NULL, $homologacoes);


            if ($this->input->post('ckbx_wf')) {
                $return = $this->cad_item_wf_atributo_model->get_itens_validados('homologados', $item->id_item);

                if (count($return) > 0) {
                    $this->cad_item_wf_atributo_model->set_status('homologados', $item->id_item);
                }
            }

            $this->message_next_render('<h4>Sucesso</h4> Informações gravadas no banco de dados.');

            redirect('homologacao/ficha/' . $id_item);
        }

        $data['item'] = $item;
        $data['allow_edit'] = TRUE;
        $data['perfil_allow_edit'] = FALSE;

        if (has_role('engenheiro') || has_role('fiscal') || has_role('consultor')) {
            $data['perfil_allow_edit'] = TRUE;
        }

        $homolog_info = $this->cad_item_model->get_homolog_info($item->id_item);
        $homolog_arr = array_filter($homolog_info, function ($item) {
            return ($item->homologado == 1);
        });

        // RETIRADA VALIDAÇÃO DE HOMOLOGAÇÃO A PEDIDO DO CLIENTE PARA SEMPRE PERMITIR EDIÇÃO DOS ITENS
        // SE OS PERFIS DE USUÁRIO FOREM OS ACIMA
        // if (count($homolog_arr) == 2 && !has_role('sysadmin')) {
        //     $data['allow_edit'] = FALSE;
        // }

        if (isset($item->ncm_atual) && !empty($item->ncm_atual)) {
            if ($impostos_ncm_atual = $this->ncm_model->get_impostos($item->ncm_atual)) {
                $data['impostos_ncm_atual'] = $impostos_ncm_atual;
            } else if ($impostos_ncm_historico = $this->ncm_model->get_impostos($item->ncm_atual, TRUE)) {
                $data['impostos_ncm_historico'] = $impostos_ncm_historico;
            }
        }

        if (!customer_can('ncm_fornecedor')) {
            unset($item->ncm_fornecedor);
        }
        if (isset($item->ncm_fornecedor) && !empty($item->ncm_fornecedor)) {
            if ($impostos_ncm_fornecedor = $this->ncm_model->get_impostos($item->ncm_fornecedor)) {
                $data['impostos_ncm_fornecedor'] = $impostos_ncm_fornecedor;
            } else if ($impostos_ncm_fornecedor_historico = $this->ncm_model->get_impostos($item->ncm_fornecedor, TRUE)) {
                $data['impostos_ncm_fornecedor_historico'] = $impostos_ncm_fornecedor_historico;
            }
        }

        if (isset($item->ncm_proposto) && !empty($item->ncm_proposto)) {
            if ($impostos_ncm_proposto = $this->ncm_model->get_impostos($item->ncm_proposto)) {
                $data['impostos_ncm_proposto'] = $impostos_ncm_proposto;
            } else if ($impostos_ncm_proposto_hist = $this->ncm_model->get_impostos($item->ncm_proposto, TRUE)) {
                $data['impostos_ncm_proposto_hist'] = $impostos_ncm_proposto_hist;
            }

            if ($antidumpings = $this->ncm_model->get_antidumping_oracle($item->ncm_proposto)) {
                $data['antidumpings'] = $antidumpings;
            }


            if ($lis = $this->ncm_model->get_li_oracle($item->ncm_proposto)) {
                $data['lis'] = $lis;
            }
        }

        $data['part_number_similar'] = $this->cad_item_model->get_similar_pn($item->part_number);

        $fotos_ctr_pendencias = $this->foto_model->get_fotos_controle_pendencias($item->part_number, $item->id_empresa);
        $has_ctr_fotos = (count($fotos_ctr_pendencias) > 0) ? TRUE : FALSE;

        if ($data['part_number_similar'] && !$has_ctr_fotos) {
            $data['part_number'] = $data['part_number_similar'];
        } else {
            $data['part_number'] = $item->part_number;
        }

        $data['has_ctr_fotos'] = $has_ctr_fotos;

        $role = NULL;
        if (!has_role('sysadmin')) {
            if (has_role('engenheiro')) {
                $role = 'Engenharia';
            } else if (has_role('fiscal')) {
                $role = 'Fiscal';
            }
        }

        $data['historico'] = $this->cad_item_homologacao_model->get_entries_by_pn($id_item, $role);

        $data['ex_tarifarios_ii'] = NULL;
        $data['ex_tarifarios_ipi'] = NULL;

        if (!empty($item->ncm_proposto)) {
            $data['ex_tarifarios_ii'] = $this->ex_tarifario_model->get_all_ex_ii_by_ncm($item->ncm_proposto, true);
            $data['ex_tarifarios_ipi'] = $this->ex_tarifario_model->get_all_ex_ipi_by_ncm($item->ncm_proposto, true);
            $data['cests'] = $this->cest_model->get_entries_by_ncm($item->ncm_proposto);
            $data['classificacoes_energeticas'] = $this->ncm_model->get_classificacao_energetica($item->ncm_proposto);
        }

        if (!empty($item->num_ex_ii)) {
            $data['item_ex_ii'] = $this->ex_tarifario_model->get_ex_ii_by_ncm($item->num_ex_ii, $item->ncm_proposto);
        }

        if (!empty($item->num_ex_ipi)) {
            $data['item_ex_ipi'] = $this->ex_tarifario_model->get_ex_ipi_by_ncm($item->num_ex_ipi, $item->ncm_proposto);
        }


        $data['has_nve'] = $this->cad_item_nve_model->has_nve($item->id_item);

        $empresa = $this->empresa_model->get_entry($id_empresa);

        $data['itemLessin'] = null;
        if (strtolower($item->lista_becomex) == 'sim' || strtolower($item->lista_cliente) == 'sim') {
            if (!empty($item->id_lessin)) {
                $this->lessin_model->set_state('filter.id', $item->id_lessin);
            } else {
                $this->lessin_model->set_state('filter.ncm', $item->ncm_proposto);
            }

            $data['itemLessin'] = $this->lessin_model->get_item();
        }

        $data['campos_adicionais'] = explode('|', $empresa->campos_adicionais);
        $data['funcoes_adicionais'] = explode('|', $empresa->funcoes_adicionais);

        $hasDescricaoGlobal = in_array('descricao_global', $data['campos_adicionais']);
        $data['hasDescricaoGlobal'] = $hasDescricaoGlobal;

        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;
        $data['simplus']                = $empresa->integracao_simplus;

        $usuario = $this->usuario_model->get_entry(sess_user_id());
        $this->usuario_model->set_state('filter.id_empresa', sess_user_company());

        // Mestre
        $mestre_item = $this->item_model->get_entry($item->part_number, $item->id_empresa, $item->estabelecimento);
        $mestre_item->peso = format_peso($mestre_item->peso);

        // Motivos
        $this->motivo_model->set_state('filter.id_perfil', $usuario->id_perfil);
        $motivos = $this->motivo_model->get_entries();

        $data['motivos'] = $motivos;
        $data['descricao_max_caracteres'] = $empresa->descricao_max_caracteres;
        $data['item_status'] = $mestre_item->status;
        $data['can_ncm_fornecedor'] = customer_can('ncm_fornecedor');
        $data['can_atr'] = customer_can('atr');
        $data['has_attrs'] = null;
        $data['owner_item'] = $mestre_item->cod_owner;

        $owner = $this->owner_model->get_nome_owner_and_responsaveis($mestre_item->cod_owner);
        $data['owner'] = $owner;

        $owners_empresa = $this->empresa_model->get_owners_by_empresa($empresa);
        $data['owners_empresa'] = $owners_empresa;

        $prioridades = $this->empresa_prioridades_model->get_entry($id_empresa);

        $prioridade = [];
        foreach ($prioridades as $p) {
            $prioridade[$p->id_prioridade] = $p->nome;
        }
        $data['prioridades'] = $prioridade;

        if (customer_can('atr')) {
            $attr_metricas = $this->produto_model->get_metricas_by_item($item->id_item);

            $data['has_attrs'] = false;
            $data['has_attrs_pendencias'] = false;

            if (!empty($attr_metricas->total_atributos)) {
                $data['has_attrs'] = ($attr_metricas->total_atributos);
                $data['has_attrs_pendencias'] = $attr_metricas->has_assign_pending();
            }
        }

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-select/i18n/defaults-pt_BR.min.js',
            'bootstrap-notify.min.js',
            'jquery.cookie.js',
            'sweetalert.min.js',
            'jquery.autocomplete.min.js',
            'jquery.mask.min.js',
            'select2.full.min.js'
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'sweetalert.css',
            'autocomplete.css',
            'select2.min.css'
        ));

        // Quais são os homologadores da empresa...
        $data['homologacoes'] = $this->empresa_model->get_homologacao_by_id_empresa($empresa->id_empresa);
        // Buscandos os itens de suframa
        $data['suframa_produtos_empresa'] = $this->suframa_model->get_suframa_produtos_by_empresa();
        $data['existe_suframa_ncm_produto'] = $this->suframa_model->has_suframa_produtos_ncm_empresa($item);

        // Ficha Técnica
        $this->anexo_model->set_state('filter.tipo_anexo', 'ficha_tecnica');
        $data['ficha_tecnica'] = $this->anexo_model->get_entries($item->part_number, $item->estabelecimento, $item->id_empresa);

        $part_number = $item->part_number;
        $estabelecimento = $item->estabelecimento;
        $id_empresa = $item->id_empresa;

        $usuarios_seguidores = $this->item_model->get_usuarios_seguidores($part_number, $id_empresa, $estabelecimento);

        $data['usuarios_seguidores'] = $usuarios_seguidores;

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push("Homologação", '/homologacao/');
        $this->breadcrumbs->push('Ficha do item', '/homologacao/ficha/' . $id_item);

        $this->render('homologacao/ficha', $data);
    }

    public function xhr_get_responsaveis()
    {
        $this->load->model(array('usuario_model'));

        $tipo_responsavel = $this->input->post("tipo_responsavel") == "id_resp_engenharia" ? "engenheiro" : "fiscal";

        $this->usuario_model->set_state('filter.id_empresa', sess_user_company());
        $this->usuario_model->set_state('order_by', array('u.id_perfil' => 'desc', 'nome' => 'asc'));

        $lista_usuarios = $this->usuario_model->get_responsaveis($tipo_responsavel, null, true);

        $id_perfil = null;
        $select_body = "";

        $lista_usuarios = array_filter($lista_usuarios, function ($usuario) {
            return !customer_has_role('sysadmin', $usuario->id_usuario) && !customer_has_role('becomex_pmo', $usuario->id_usuario);
        });

        foreach ($lista_usuarios as $k => $usuario) {
            if (
                customer_has_role('cliente_pmo', $usuario->id_usuario)
                && (has_role('fiscal') || has_role('engenheiro'))
            ) {
                $select_body .= "<optgroup label='Gerente de Projetos'>";
                $select_body .= "<option value='{$usuario->id_usuario}' data-subtext='{$usuario->email}'>{$usuario->nome}</option>";
                $select_body .= "</optgroup>";
            } else {
                if ($id_perfil == null || ($id_perfil !== $usuario->id_perfil)) {
                    $select_body .= $id_perfil !== null ? "</optgroup>" : '';
                    $select_body .= "<optgroup label='{$usuario->perfil_descricao}'>";

                    $id_perfil = $usuario->id_perfil;
                }

                $select_body .= "<option data-id-perfil='{$usuario->id_perfil}' value='{$usuario->id_usuario}' data-subtext='{$usuario->email}'>{$usuario->nome}</option>";

                $select_body .= (count($lista_usuarios) + 1) == $k ? "</optgroup>" : "";
            }
        }

        $data['select_body'] = $select_body;

        return response_json($data);
    }

    public function modal_simplus()
    {
        $lista_item = $this->input->post('item');

        if ($lista_item == false) {
            $bulk        = $this->usuario_homolog_bulk_model->get_entry();
            $lista_item = unserialize($bulk->data);
        }

        $necessita_homologacao = FALSE;

        if (count($lista_item) == 1) {
            $id_item = current($lista_item);

            $cad_item = $this->cad_item_model->get_entry($id_item);
            $item = $this->item_model->get_entry($cad_item->part_number, $cad_item->id_empresa, $cad_item->estabelecimento);

            $necessita_homologacao = ($item->status == 'pendente_hom_ambos' ? TRUE : FALSE);
        }

        $data['necessita_homologacao'] = $necessita_homologacao;
        $data['lista_item']            = $lista_item;

        $this->load->view('homologacao/modal-simplus', $data);
    }

    public function envia_simplus()
    {
        $post = $this->input->post();

        if ($post == false) {
            show_404();
        }

        $id_empresa             = sess_user_company();

        $itens                  = $post['item'];
        $motivo                 = $post['motivo'];
        $necessita_homologacao  = (isset($post['necessita_homologacao']) ? 1 : 2);

        $this->load->model('cest_model');
        $this->load->model('ex_tarifario_model');

        $data = array();

        foreach ($itens as $item) {
            $this->cad_item_model->set_state('filter.id_empresa', $id_empresa);
            $cad_item = $this->cad_item_model->get_entry($item);

            $ex_ipi = $this->ex_tarifario_model->get_ex_ipi_by_ncm($cad_item->num_ex_ipi, $cad_item->ncm_proposto);
            $ex_ii  = $this->ex_tarifario_model->get_ex_ii_by_ncm($cad_item->num_ex_ii,  $cad_item->ncm_proposto);
            $cest   = $this->cest_model->get_entry($cad_item->cod_cest_proposto);

            $desc_ex_ipi = !empty($ex_ipi) ? $ex_ipi->descricao_linha1 : null;
            $desc_ex_ii  = !empty($ex_ii) ? $ex_ii->descricao_linha1 : null;
            $desc_cest   = !empty($cest) ? $cest->descricao : null;

            $pct_ex_ii  = !empty($ex_ii) ? $ex_ii->pct_ii : null;
            $pct_ex_ipi = !empty($ex_ipi) ? $ex_ipi->pct_ipi : null;

            $ncm_impostos  = $this->ncm_model->get_impostos($cad_item->ncm_proposto, false);

            $pct_ii  = null;
            $pct_ipi = null;

            if (!empty($ncm_impostos)) {
                $pct_ii  = $ncm_impostos->pct_ii;
                $pct_ipi = $ncm_impostos->pct_ipi;
            }

            $data[] = array(
                'id_item' => $cad_item->id_item,
                'ws_data' => array(
                    'gtin'                 => $cad_item->part_number,
                    'cnpj'                 => $cad_item->estabelecimento,
                    'proposedNCM'          => $cad_item->ncm_proposto,
                    'classificationMemory' => $cad_item->memoria_classificacao,
                    'subsidy'              => $cad_item->subsidio,
                    'characteristic'       => $cad_item->caracteristicas,
                    'fareGroup'            => $cad_item->grupo_tarifario_desc,
                    'numExIPI'             => $cad_item->num_ex_ipi,
                    'DesExIPI'             => $desc_ex_ipi,
                    'numExII'              => $cad_item->num_ex_ii,
                    'DesExII'              => $desc_ex_ii,
                    'codCEST'              => $cad_item->cod_cest_proposto,
                    'DesCEST'              => $desc_cest,
                    // 'AliqIINom'            => $pct_ii,
                    // 'AliqIPINom'           => $pct_ipi,
                    // 'AliqIIEx'             => $pct_ex_ii,
                    // 'AliqIPIEx'            => $pct_ex_ipi,
                    'requiresHomologation' => $necessita_homologacao,
                    'message'              => $motivo
                )
            );
        }

        $this->load->library('guzzle');
        $this->load->model('ws_log_model');

        $client = new GuzzleHttp\Client();
        $url = config_item('simplus_url') . 'fiscalClassification/itemClassified';

        foreach ($data as $item) {
            $responseBodyAsString = '';
            $responseHttpCode = 200;

            try {

                if (ENVIRONMENT == 'production') {
                    $response = $client->request(
                        'POST',
                        $url,
                        [
                            'json' => $item['ws_data'],
                            'headers' => [
                                'Authorization' => 'basic efa37daf24a60624522fb38eb7205686'
                            ]
                        ]
                    );
                    $responseHttpCode = $response->getStatusCode();
                }
            } catch (GuzzleHttp\Exception\BadResponseException $e) {
                $response = $e->getResponse();
                $responseBodyAsString = $response->getBody()->getContents();
                $responseHttpCode = $response->getStatusCode();
            }

            $json_ws_data = json_encode($item['ws_data']);

            // WS Log
            $log_data = array(
                'origem'        => "SIMPLUS",
                'tipo'          => "OUTPUT",
                'dados'         => $json_ws_data,
                'mensagem'      => $responseBodyAsString,
                'codigo_http'   => $responseHttpCode
            );

            $this->ws_log_model->save_entry($log_data);

            // Update Status!
            $this->db->where_in('id_item', $item['id_item']);
            $this->db->update('cad_item', array('status_simplus' => 1));

            //UPDATE ITEM CAMPO EVENTO
            $item_update = $this->item_model->get_entry($item['ws_data']['gtin'], $id_empresa, $item['ws_data']['cnpj']);
            if (!empty($item_update) && (empty($item_update->evento) || $item_update->evento == NULL || $item_update->evento == '')) {
                $dat_criacao = date('d/m/Y', strtotime($item_update->dat_criacao));
                $dat_envio = date('d/m/Y');
                $evento = "LOTE {$dat_criacao} - ENVIO {$dat_envio}";

                $where = array(
                    'id_empresa' => $item_update->id_empresa,
                    'estabelecimento' => $item_update->estabelecimento,
                    'part_number' => $item_update->part_number
                );

                $this->db->update('item', array('evento' => $evento), $where);
            }

            // Save log!
            $motivo  = 'Classificação enviada para SimplusTEC.<br>';
            $motivo .= '<strong>Motivo:</strong>&nbsp;' . ($item['ws_data']['message'] ? $item['ws_data']['message'] : '<em>Não informado</em>') . '<br>';
            $motivo .= '<strong>Necessita Homologação?</strong>&nbsp;' . ($item['ws_data']['requiresHomologation'] == 1 ? 'SIM' : 'NÃO');

            $data = array(
                'id_empresa'        => sess_user_company(),
                'id_usuario'        => sess_user_id(),
                'part_number'       => $item['ws_data']['gtin'],
                'estabelecimento'   => $item['ws_data']['cnpj'],
                'titulo'            => 'simplus',
                'motivo'            => $motivo,
                'criado_em'         => date("Y-m-d H:i:s")
            );

            $this->db->insert('item_log', $data);
        }

        // Clear bulk
        $this->bulk_clean();

        $this->message_next_render('<strong>Sucesso.</strong> A homologação do(s) item(ns) foi enviada para a Simplus.');
        redirect("homologacao");
    }

    public function atualiza_simplus()
    {
        $id_item = $this->input->post('id_item');

        try {
            $item = $this->cad_item_model->get_entry($id_item);

            $this->db->where('id_item', $id_item);
            $this->db->set('status_simplus', 0);
            $this->db->update('cad_item');

            $data['message'] = 'Item atualizado com sucesso!';
        } catch (Exception $e) {
            $data['message'] = $e->getMessage();
        }

        return $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($data));
    }

    public function ajax_atualizar_item()
    {
        $can_formatar_texto = company_can("formatar_texto");

        $item_pk = $this->input->post('pk');
        $nova_descricao = formatar_texto($can_formatar_texto, $this->input->post('value'));

        if (empty($item_pk) || empty($nova_descricao) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry($item->id_empresa);

        $max_chars = $empresa->descricao_max_caracteres;

        if (!empty($max_chars) && strlen($nova_descricao) > $max_chars) {
            return 'A descrição excede o limite de ' . $empresa->descricao_max_caracteres . ' caracteres.';
        }

        $motivo = "<strong>Descrição proposta resumida:</strong> <em>{$item->descricao_mercado_local}</em> &rarr; {$nova_descricao}";

        $update_arr = array(
            'descricao_mercado_local' => $nova_descricao,
            'houve_descricao_manual' => 1
        );

        //****Workflow de atributos ****/
        if ($this->input->post('value')) {
            $this->load->model([
                'cad_item_wf_atributo_model',
                'log_wf_atributos_model'
            ]);

            $return = $this->cad_item_wf_atributo_model->get_itens_validados('em_revisao', $item_pk);
            if (count($return) > 0) {
                $this->cad_item_wf_atributo_model->set_status('em_revisao', $item_pk);

                $justificativa  =  isset($post['justification']) ? $post['justification'] : '';
                $statusAtributosHomologados = $this->cad_item_wf_atributo_model->get_status_wf_atributos('em_revisao');
                $id_usuario = sess_user_id();

                $this->log_wf_atributos_model->registrar_log(
                    $item_pk,
                    $item->part_number,
                    $item->estabelecimento,
                    $item->id_empresa,
                    $statusAtributosHomologados->id,
                    'movimentacao_manual',
                    $id_usuario,
                    $justificativa
                );
            }
        }

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_funcao()
    {
        $can_formatar_texto = company_can("formatar_texto");

        $item_pk = $this->input->post('pk');
        $value = formatar_texto($can_formatar_texto, $this->input->post('value'));

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Função: </strong> <em>{$item->funcao}</em> &rarr; {$value}";

        $update_arr = array(
            'funcao' => $value,
            'houve_funcao_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_inf_adicionais()
    {
        $can_formatar_texto = company_can("formatar_texto");

        $item_pk = $this->input->post('pk');
        $value = formatar_texto($can_formatar_texto, $this->input->post('value'));

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Informações Adicionais:</strong> <em>{$item->inf_adicionais}</em> &rarr; {$value}";

        $update_arr = array(
            'inf_adicionais' => $value,
            'houve_inf_adicionais_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_descricao_completa()
    {
        $this->load->model("cad_item_wf_atributo_model");
        $this->load->model("log_wf_atributos_model");

        $id_usuario = sess_user_id();
        $can_formatar_texto = company_can("formatar_texto");

        $item_pk = $this->input->post('pk');
        $value = formatar_texto($can_formatar_texto, $this->input->post('value'));

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        if ($item->descricao_proposta_completa <> $value && $item->wf_status_atributos == '7')
        {
            $this->cad_item_wf_atributo_model->set_status('em_revisao', $item_pk);
            $this->log_wf_atributos_model->registrar_log(
                $item_pk,
                $item->part_number,
                $item->estabelecimento,
                $item->id_empresa,
                5,
                'movimentacao_manual',
                $id_usuario,
                null
            );
        }

        $motivo = "<strong>Descrição proposta completa:</strong><em>{$item->descricao_proposta_completa}</em> &rarr; {$value}";

        $update_arr = array(
            'descricao_proposta_completa' => $value,
            'houve_descricao_completa_manual' => 1
        );

        return $this->item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_owner()
    {
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value)) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Owner na ficha do item: </strong><em>Part Number: {$item->part_number}</em> &rarr; {$value}";

        $data = array(
            'cod_owner' => $value
        );

        return $this->item_model->update_item($item->part_number, $item->id_empresa, $data, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_peso()
    {
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value)) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = '<strong>Peso:</strong> ' . "<em>{$item->peso}</em> &rarr;" . $value . '<br>';

        $data = array(
            'peso' => $value
        );

        return $this->item_model->update_item($item->part_number, $item->id_empresa, $data, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_prioridade()
    {
        $this->load->model('empresa_prioridades_model');
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value)) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $desc_old_prioridade = 'Não Informado';
        $desc_new_prioridade = 'Não Informado';
        if (!empty($item->id_prioridade)) {
            $old_prioridade = $this->empresa_prioridades_model->get_entry_by_id($item->id_prioridade);
            $desc_old_prioridade = $old_prioridade[0]->nome;
        }

        if (!empty($value)) {
            $new_prioridade = $this->empresa_prioridades_model->get_entry_by_id($value);
            $desc_new_prioridade = $new_prioridade[0]->nome;
        }
        if (trim($desc_old_prioridade) != trim($desc_new_prioridade)) {
            $motivo = '<strong>Prioridade:</strong> ' . "<em>{$desc_old_prioridade}</em> &rarr;" . $desc_new_prioridade . '<br>';

            $data = array(
                'id_prioridade' => $value
            );

            return $this->item_model->update_item($item->part_number, $item->id_empresa, $data, $motivo, $item->estabelecimento);
        } else {
            return 'Erro ao processar atualização.';
        }
    }

    public function ajax_atualizar_aplicacao()
    {
        $can_formatar_texto = company_can("formatar_texto");

        $item_pk = $this->input->post('pk');
        $value = formatar_texto($can_formatar_texto, $this->input->post('value'));

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Aplicação:</strong> <em>{$item->aplicacao}</em> &rarr; {$value}";

        $update_arr = array(
            'aplicacao' => $value,
            'houve_aplicacao_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_marca()
    {
        $can_formatar_texto = company_can("formatar_texto");

        $item_pk = $this->input->post('pk');
        $value = formatar_texto($can_formatar_texto, $this->input->post('value'));

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Marca:</strong> <em>{$item->marca}</em> &rarr; {$value}";

        $update_arr = array(
            'marca' => $value,
            'houve_marca_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_solucao_consulta()
    {
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Solução de Consulta:</strong> <em>{$item->solucao_consulta}</em> &rarr; {$value}";

        $update_arr = array(
            'solucao_consulta' => $value,
            'houve_solucao_consulta_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_dispositivo_legal()
    {
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Dispositivo Legal:</strong>  <em>{$item->dispositivo_legal}</em> &rarr; {$value}";

        $update_arr = array(
            'dispositivo_legal' => $value,
            'houve_dispositivo_legal_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_caracteristica()
    {
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Características:</strong>  <em>{$item->caracteristicas}</em> &rarr; {$value}";

        $update_arr = array(
            'caracteristicas' => $value,
            'houve_caracteristica_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_subsidio()
    {
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Subsídio:</strong> <em>{$item->subsidio}</em> &rarr; {$value}";

        $update_arr = array(
            'subsidio' => $value,
            'houve_subsidio_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_memoria_classificacao()
    {
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Memória de Classificação:</strong> <em>{$item->memoria_classificacao}</em> &rarr; {$value}";

        $update_arr = array(
            'memoria_classificacao' => $value,
            'houve_memoria_classificacao_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_observacoes()
    {
        $item_pk = $this->input->post('pk');
        $value = $this->input->post('value');

        if (empty($item_pk) || empty($value)) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Observações:</strong>  <em>{$item->observacoes}</em> &rarr; {$value}";

        $update_arr = array(
            'observacoes' => $value
        );

        return $this->item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_material_constitutivo()
    {
        $can_formatar_texto = company_can("formatar_texto");

        $item_pk = $this->input->post('pk');
        $value = formatar_texto($can_formatar_texto, $this->input->post('value'));

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong> Material contitutivo:</strong> <em>{$item->material_constitutivo}</em> &rarr; {$value}";

        $update_arr = array(
            'material_constitutivo' => $value,
            'houve_material_constitutivo_manual' => 1
        );

        return $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_origem()
    {
        $can_formatar_texto = company_can("formatar_texto");
        $item_pk = $this->input->post('pk');
        $value = formatar_texto($can_formatar_texto, $this->input->post('value'));
        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Origem:</strong> <em>{$item->origem}</em> &rarr; {$value}";

        $update_arr = array(
            'origem' => $value
        );

        return $this->item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_maquina()
    {
        $can_formatar_texto = company_can("formatar_texto");

        $item_pk = $this->input->post('pk');
        $value = formatar_texto($can_formatar_texto, $this->input->post('value'));

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "<strong>Maquina:</strong> <em>{$item->maquina}</em> &rarr; {$value}";

        $update_arr = array(
            'maquina' => $value
        );

        return $this->item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
    }

    public function ajax_atualizar_li()
    {
        $item_pk = $this->input->post('pk');
        $li =  $this->input->post('li');
        $li_orgao_anuente =  $this->input->post('li_orgao_anuente');
        $li_destaque =  $this->input->post('li_destaque');
        $previous_li_orgao_anuente = $this->input->post('previous_li_orgao_anuente');
        $previous_li_destaque = $this->input->post('previous_li_destaque');

        if (empty($item_pk) || empty($li) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);

        $motivo = "Alteração do LI: ";
        $motivo .= "<em>{$previous_li_orgao_anuente} - {$previous_li_destaque}</em> &rarr;";
        $motivo .= "<strong>{$li_orgao_anuente} - {$li_destaque}</strong>";

        $update_arr = array(
            'li' => $li,
            'li_orgao_anuente' => $li_orgao_anuente,
            'li_destaque' => $li_destaque
        );

        $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);

        return response_json(array(
            'status' => 200
        ));
    }

    public function ajax_atualizar_antidumping()
    {
        $item_pk = $this->input->post('pk');
        $value =  $this->input->post('value');
        $descricao =  $this->input->post('descricao');

        if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
            return 'Erro ao processar atualização.';
        }

        $item = $this->cad_item_model->get_entry($item_pk);
        $anti_dumping = $item->antidumping;

        $valor_de = 'SIM';
        $motivo = "Alteração do Antidumping: <em>{$valor_de}</em> &rarr; <strong> SIM ({$descricao})</strong>";

        if (empty($anti_dumping)) {
            $valor_de = 'NÃO';
            $motivo = "Alteração do Antidumping: <em>{$valor_de}</em> &rarr; <strong> SIM ({$descricao})</strong>";
        }

        $update_arr = array(
            'antidumping' => $value
        );

        $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
        return response_json(array(
            'status' => 200
        ));
    }

    public function ajax_atualizar_grupo_tarifario()
    {
        try {
            $item_pk = $this->input->post('pk');
            $value = $this->input->post('value');

            if (empty($item_pk) || empty($value) || (!has_role('engenheiro') && !has_role('fiscal') && !has_role('consultor'))) {
                return 'Erro ao processar atualização.';
            }

            $item = $this->cad_item_model->get_entry($item_pk);

            $this->load->model([
                'grupo_tarifario_model',
                'cad_item_homologacao_model',
                'ncm_model'
            ]);
            $this->load->library("Item/Status");

            $grupo = $this->grupo_tarifario_model->get_entry($value);

            $motivo = "<strong>Grupo Tarifário: </strong> <em>{$item->grupo_tarifario_desc}</em> &rarr; {$grupo->descricao}<br> 
                       <strong>NCM Proposto: </strong> <em>{$item->ncm_proposto}</em> &rarr; {$grupo->ncm_recomendada}";


            $ncm_grupo_tarifario = $item->ncm_proposto;

            $this->db->where('id_grupo_tarifario', $grupo->id_grupo_tarifario);
            $query = $this->db->get('grupo_tarifario');
            $result_grupo_tarifario = $query->row();
            $ncm_novo_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;


            if (!empty($ncm_grupo_tarifario) && !empty($ncm_novo_grupo_tarifario) && $ncm_grupo_tarifario != $ncm_novo_grupo_tarifario) {
                $this->db->where('id_item', $item->id_item);
                $this->db->where("id_grupo_tarifario <> '{$grupo->id_grupo_tarifario}'", NULL, FALSE);
                $this->db->delete('cad_item_attr');
            } else {
                $this->db->set('id_grupo_tarifario', $grupo->id_grupo_tarifario);
                $this->db->from('cad_item_attr');
                $this->db->where('id_item', $item->id_item);
                $this->db->where("id_grupo_tarifario <> '{$grupo->id_grupo_tarifario}'", NULL, FALSE);
                $this->db->update('cad_item_attr attr');
            }

            $update_arr = array(
                'id_grupo_tarifario' => $grupo->id_grupo_tarifario,
                'ncm_proposto' => $grupo->ncm_recomendada,
                'houve_grupo_tarifario_manual' => 1
            );

            $li = $this->ncm_model->get_li_oracle($grupo->ncm_recomendada);
            if (empty($li)) {
                $update_arr['li'] = 'NÃO';
                $update_arr['li_orgao_anuente'] = '';
                $update_arr['li_destaque'] = '';
            } else {
                $update_arr['li'] = '';
                $update_arr['li_orgao_anuente'] = '';
                $update_arr['li_destaque'] = '';
            }

            // $this->db->query("SET @user_update_cad_item_homologacao = " . sess_user_id());
            $id = $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $update_arr, $motivo, $item->estabelecimento);
            // $this->db->query("SET @user_update_cad_item_homologacao = NULL");
            $this->cad_item_homologacao_model->drop_item($item->id_item);
            $this->status->set_status("homologar");
            $this->status->update_item($item->part_number, $item->estabelecimento);


            return response_json(array(
                'status' => 200,
                'message' => 'Informações foram alteradas.',
                'data' => array(
                    'id' => $id,
                    'ncm' => $grupo->ncm_recomendada,
                    'grupo_tarifario' => $grupo->id_grupo_tarifario
                )
            ));
        } catch (Exception $e) {
            return response_json(array(
                'status' => 500,
                'message' => 'Encontramos um erro ao atualizar o Grupo Tarifário, verifique as informações fornecidas.',
                'errors' => $e->getMessage()
            ));
        }
    }

    public function transferir_responsavel()
    {
        if ($post = $this->input->post()) {
            $dbdata = array();
            $error = '';
            $usuario = NULL;

            if ($this->input->is_set('id_usuario') && !empty($post['id_usuario'])) {
                $dbdata[$post['tipo_responsavel']] = $post['id_usuario'];

                $this->load->model('usuario_model');
                $usuario = $this->usuario_model->get_entry($post['id_usuario']);
            } else {
                $error .= $this->message_config('<strong>Oops!</strong> Informe o novo usuário responsável para concluir a transferência.', 'error');
            }

            if (!$this->input->is_set('motivo_transf')) {
                $error .= $this->message_config('<strong>Oops!</strong> Informe o motivo para concluir a transferência de usuário.', 'error');
            }

            if (!empty($error)) {
                $this->message_next_render($error, NULL, TRUE);
            } else {
                $usuario_logado = $this->usuario_model->get_entry(sess_user_id());

                $bulk = $this->usuario_homolog_bulk_model->get_entry();
                $itens = unserialize($bulk->data);

                if (empty($itens)) {
                    $itens = array($post['id_item']);
                }

                $error = FALSE;
                $part_numbers = '';
                foreach ($itens as $key => $item) {
                    $item = $this->cad_item_model->get_entry($item);

                    if ($key > 0) {
                        $part_numbers .= ', ';
                    }

                    $part_numbers .= '<a href="' . config_item('online_url') . 'homologacao/ficha/' . $item->id_item . '">' . $item->part_number . '</a>';

                    if ($post['tipo_responsavel'] == 'id_resp_fiscal') {
                        try {
                            $old_usuario = $this->usuario_model->get_entry($item->id_resp_fiscal);
                        } catch (Exception $e) {
                            $old_usuario = NULL;
                        }

                        $responsavel = 'fiscal';
                    } else {
                        try {
                            $old_usuario = $this->usuario_model->get_entry($item->id_resp_engenharia);
                        } catch (Exception $e) {
                            $old_usuario = NULL;
                        }
                        $responsavel = 'engenharia';
                    }

                    $motivo  = 'Transferência de responsável ' . $responsavel;

                    if (!empty($old_usuario)) {
                        $motivo .= ' ' . $old_usuario->nome . ' (ID.: ' . $old_usuario->id_usuario . '),';
                    }

                    $motivo .= ' para usuário ' . $usuario->nome . ' (ID.: ' . $usuario->id_usuario . ')<br>';

                    $motivo .= '<strong>Motivo: </strong>' . $post['motivo'];

                    if (!$this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbdata, $motivo, $item->estabelecimento)) {
                        $error = TRUE;
                    }
                }

                if ($error === FALSE) {
                    $this->message_next_render('<strong>OK!</strong> Transferência de responsável concluída com sucesso.', 'success');

                    $this->load->library('email');

                    $this->email->to($usuario->email);
                    $this->email->from($this->config->item('mail_from_addr'), $this->config->item('mail_from_name'));
                    $this->email->subject('[Gestão Tarifária] - Transferência de responsável');

                    $data['base_url'] = config_item('online_url');
                    $data['html_message'] =
                        '<h4>Notificação de transferência de responsável</h4>
                        <p>' . $usuario->nome . ', você foi designado como responsável (' . $responsavel . ') pelos seguintes itens: ' . $part_numbers . '.<br>
                        <strong>Motivo da transferência:</strong> ' . $post['motivo'] . '</p>
                        <p>Em caso de dúvidas, contate o gerente de projetos.</p>';

                    $html = $this->load->view('templates/basic_template', $data, TRUE);

                    $this->email->message($html);
                    if (!$this->email->send()) {
                        // print_r($this->email->print_debugger); die();
                    }

                    if (!customer_has_role('cliente_pmo', $usuario->id_usuario)) {
                        if ($gp = $this->usuario_model->get_gp_by_empresa(sess_user_company())) {
                            $this->email->to($gp->email);
                            $this->email->from($this->config->item('mail_from_addr'), $this->config->item('mail_from_name'));
                            $this->email->subject('[Gestão Tarifária] - Transferência de responsável');

                            $data['base_url'] = config_item('online_url');
                            $data['html_message'] =
                                '<h4>Notificação de transferência de responsável</h4>
                                <p>' . $gp->nome . ', o usuário ' . $usuario->nome . ' foi designado como responsável (' . $responsavel . ') pelos seguintes itens: ' . $part_numbers . '.<br>
                                <strong>Motivo da transferência:</strong> ' . $post['motivo'] . '<br>
                                <strong>Usuário que efetuou a transferência: </strong>' . $usuario_logado->nome . '</p>
                                <p>Em caso de dúvidas, contate o usuário responsável pela transferência.</p>';

                            $html = $this->load->view('templates/basic_template', $data, TRUE);

                            $this->email->message($html);
                            if (!$this->email->send()) {
                                //print_r($this->email->print_debugger()); die();
                            }
                        }
                    }
                }
            }

            if ($post['url_redirect'] == 'homologacao') {
                $this->bulk_clean();
            }

            redirect($post['url_redirect']);
        }
    }

    public function ajax_save_lessin()
    {
        if (
            empty($this->input->post("partnumber"))
        ) {
            return response_json(array("error" => true));
        }

        $this->load->model("item_model");

        $filters = array(
            'part_number' => $this->input->post("partnumber"),
            'estabelecimento' => $this->input->post("estabelecimento")
        );

        if ($arr = $this->item_model->update_lessin($filters)) {
            return response_json(array(
                "error" => false,
                "html" => $this->load->view(
                    'homologacao/ficha-lessin',
                    array('lessin' => $arr['itemLessin'], 'item' => $arr['item']),
                    TRUE
                )
            ));
        }

        return response_json(array("error" => true));
    }

    public function ajax_save_cest()
    {
        if ($post = $this->input->post()) {
            $this->load->model('cest_model');
            $cad_item = $this->cad_item_model->get_entry($post['id_item']);

            $motivo = array();
            $motivo['titulo'] = 'vinculacao_cest';

            if ($post['type_cest'] == 'cest') {
                $dbdata['cod_cest'] = $post['cest'];

                // Descrição EX de II.
                if ($post['cest'] == -1) {
                    $descricao_ii = 'Item não atende CEST.';
                } else {
                    $cest = $this->cest_model->get_entry($post['cest']);
                    $descricao = "<strong>CEST:</strong> {$cest->cod_cest}
                    <br /><strong>Descrição:</strong> {$cest->descricao}";
                }

                $motivo['descricao'] = '<strong>CEST vinculado</strong><br />' . $descricao;
            }

            if ($this->cad_item_model->update_item($cad_item->part_number, $cad_item->id_empresa, $dbdata, $motivo, $cad_item->estabelecimento)) {
                $motivo = $motivo['descricao'];

                $this->load->model('item_cest_model');

                if (!$this->item_cest_model->insert_item_log($cad_item->part_number, sess_user_company(), $cad_item->estabelecimento, array(), $motivo)) {
                    // $this->message_on_render('<strong>Oops!</strong> Ocorreu um erro ao gerar um log de CEST para o item selecionado.', 'error');
                    echo 0;
                    return FALSE;
                }
                echo 1;
                return TRUE;
            }
        }

        return FALSE;
    }

    public function ajax_save_classificacao_energetica()
    {
        $response_data = array(
            'message' => 'Sucesso'
        );
        $status = 200;

        try {

            if ($post = $this->input->post()) {
                $cad_item = $this->cad_item_model->get_entry($post['id_item']);
                if (!empty($cad_item) && $post['classificacao_energetica'] == '-1') {
                    $motivo = array();
                    $motivo['titulo'] = 'classificacao_energetica';
                    $motivo['descricao'] = '<strong>Item não atende Classificação Energética</strong><br />';

                    $dbdata['id_classificacao_energetica'] = '-1';
                    $this->cad_item_model->update_item($cad_item->part_number, $cad_item->id_empresa, $dbdata, $motivo, $cad_item->estabelecimento);
                } else if (!empty($cad_item) && isset($post['classificacao_energetica'])) {
                    $this->load->model('ncm_model');

                    $clas_energetica = $this->ncm_model->get_classificacao_energetica_id($post['classificacao_energetica']);

                    $motivo['titulo'] = 'classificacao_energetica';
                    $motivo['descricao'] = "Classificação Energética Atribuida: <strong>{$clas_energetica->ncm}</strong>";
                    $dbdata['id_classificacao_energetica'] = $post['classificacao_energetica'];
                    $this->cad_item_model->update_item($cad_item->part_number, $cad_item->id_empresa, $dbdata, $motivo, $cad_item->estabelecimento);
                }
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $status = 406;
        }

        return response_json($response_data, $status);
    }

    public function ajax_save_ex_tarifario()
    {
        if ($post = $this->input->post()) {
            $this->load->model('ex_tarifario_model');
            $cad_item = $this->cad_item_model->get_entry($post['id_item']);

            $motivo = array();
            $motivo['titulo'] = 'vinculacao_ex';

            if ($post['type_ex'] == 'ex_ii') {
                $dbdata['num_ex_ii'] = $post['num_ex'];

                // Descrição EX de II.
                if ($post['num_ex'] == -1) {
                    $descricao_ii = 'Item não atende EX.';
                } else {
                    $ii = $this->ex_tarifario_model->get_ex_ii_by_ncm($post['num_ex'], $cad_item->ncm_proposto);
                    $descricao_ii = "<strong>EX:</strong> {$ii->num_ex}
                    <br /><strong>Descrição:</strong> {$ii->descricao_linha1}";
                }

                $motivo['descricao'] = '<strong>EX de II vinculado</strong><br />' . $descricao_ii;
            } else if ($post['type_ex'] == 'ex_ipi') {
                $dbdata['num_ex_ipi'] = $post['num_ex'];

                // Descrição EX de IPI.
                if ($post['num_ex'] == -1) {
                    $descricao_ipi = 'Item não atende EX.';
                } else {
                    $ipi = $this->ex_tarifario_model->get_ex_ipi_by_ncm($post['num_ex'], $cad_item->ncm_proposto);
                    $descricao_ipi = "<strong>EX:</strong> {$ipi->num_ex}
                    <br /><strong>Descrição:</strong> {$ipi->descricao_linha1}";
                }

                $motivo['descricao'] = '<strong>EX de IPI vinculado</strong><br />' . $descricao_ipi;
            }

            if ($this->cad_item_model->update_item($cad_item->part_number, $cad_item->id_empresa, $dbdata, $motivo, $cad_item->estabelecimento)) {
                echo 1;
                return TRUE;
            }
        }

        return FALSE;
    }

    public function ajax_save_suframa()
    {
        $response_data = array(
            'message' => 'Sucesso'
        );
        $status = 200;

        try {

            if ($post = $this->input->post()) {

                $cad_item = $this->cad_item_model->get_entry($post['id_item']);

                $motivo = 'Atualizando SUFRAMA ficha.';

                if (!empty($cad_item) && $post['suframa_val'] == '-1') {

                    $motivo = array();
                    $motivo['titulo'] = 'suframa';
                    $motivo['descricao'] = '<strong>Item não atende SUFRAMA</strong><br />';

                    $dbdata['suframa_destaque'] = NULL;
                    $dbdata['suframa_ppb'] = NULL;
                    $dbdata['suframa_descricao'] = NULL;
                    $dbdata['suframa_produto'] = NULL;
                    $dbdata['suframa_codigo'] = NULL;

                    $this->cad_item_model->update_item($cad_item->part_number, $cad_item->id_empresa, $dbdata, $motivo, $cad_item->estabelecimento);
                } else if (!empty($cad_item) && $post['suframa_val'] == 1) {
                    $dbdata['suframa_destaque'] = $post['destaque'];
                    $dbdata['suframa_ppb'] = $post['ppb'];
                    $dbdata['suframa_codigo'] = $post['codigo'];
                    $dbdata['suframa_descricao'] = $post['descricao'];
                    $dbdata['suframa_produto'] = $post['produto'];

                    $this->cad_item_model->update_item($cad_item->part_number, $cad_item->id_empresa, $dbdata, $motivo, $cad_item->estabelecimento);
                }
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $status = 406;
        }

        return response_json($response_data, $status);
    }

    public function get_entry_id_item()
    {
        $post = \json_decode(file_get_contents("php://input"), TRUE);

        $this->load->model('cad_item_wf_atributo_model');
        $this->load->model('cad_item_model');
        $this->load->model('item_model');
        $item = $post['item'];

        $cad_item = $this->cad_item_model->get_entry($item);
        $item = $this->item_model->get_entry($cad_item->part_number, $cad_item->id_empresa, $cad_item->estabelecimento);

        $perguntasRespostas = "";

        $historicoItem = $this->cad_item_wf_atributo_model->getHistoricoItem($item->part_number, $item->estabelecimento);

        if (!empty($historicoItem)) {
            foreach ($historicoItem as $h => $historico) {
                $perguntasRespostas .= ($h + 1) . " - {$historico->pergunta}\n";
                $perguntasRespostas .= "R: " . (!empty($historico->resposta) ? "$historico->resposta \n\n" : " \n\n");
            }
        }


        $descricao = '';
        if (!empty($item->descricao_proposta_completa)) {
            $descricao = $item->descricao_proposta_completa . ' ' . $perguntasRespostas;
        } else if (!empty($item->descricao)) {
            $descricao = $item->descricao . ' ' . $perguntasRespostas;
        }

        if (!empty($item->part_number)) {
            $descricao = $descricao . ' Part Number: ' . $item->part_number;
        }

        if (!empty($item->marca)) {
            $descricao = $descricao . ' Marca: ' . $item->marca;
        }

        if (!empty($item->material_constitutivo)) {
            $descricao = $descricao . ' Material Constitutivo: ' . $item->material_constitutivo;
        }

        if (!empty($item->descricao)) {
            $descricao = $descricao . ' Descrição curta: ' . $item->descricao;
        }

        if (!empty($item->descricao_resumida_cad_item)) {
            $descricao = $descricao . ' Descrição resumida: ' . $item->descricao_resumida_cad_item;
        }


        $response_data = array(
            'item' => $descricao
        );

        return response_json($response_data, 200);
    }
    public function save_nve()
    {
        if ($post = $this->input->post()) {
            $this->load->model('nve_atributo_model');

            $this->load->model('cad_item_nve_model');
            $this->load->model('item_log_model');

            $nve_atributos = $post['nve'];
            $id_item = $post['id_item'];

            $item = $this->cad_item_model->get_entry($id_item);

            // Log data
            $log_data['part_number'] = $item->part_number;
            $log_data['estabelecimento'] = $item->estabelecimento;
            $log_data['id_empresa'] = $item->id_empresa;
            $log_data['id_usuario'] = sess_user_id();
            $log_data['criado_em'] = date('Y-m-d H:i:s');

            // NVE data
            $dbdata_nve['id_item'] = $item->id_item;

            foreach ($nve_atributos as $attr => $valor) {
                $erro = FALSE;

                // Próximo atributo caso o atributo/valor atual seja nulo
                // ou já existe o mesmo valor para o mesmo atributo pro item
                if ((empty($attr) || empty($valor))
                    || $this->cad_item_nve_model->check_item_atributo($item->id_item, $attr, $valor)
                ) {
                    continue;
                }

                $dbdata_nve['nve_atributo'] = $attr;
                $dbdata_nve['nve_valor'] = $valor;

                if ($this->cad_item_nve_model->save($dbdata_nve)) {
                    $nve = $this->nve_atributo_model->get_valor($item->ncm_proposto, $attr, $valor);

                    $log_data['titulo'] = 'vinculacao_nve';
                    $log_data['motivo'] = '
                        <strong>Atributo:</strong> ' . $attr . '<br />
                        <strong>Código:</strong> ' . $valor . '<br />
                        <strong>Descrição: </strong> ' . $nve->nm_especif_ncm;

                    $this->item_log_model->save($log_data);
                } else {
                    $erro = TRUE;
                }
            }

            if ($erro === FALSE) {
                $this->message_next_render('<strong>OK!</strong> Atributos de NVE vinculados com sucesso.', 'success');
            } else {
                $this->message_next_render('<strong>Oops!</strong> Não foi possível vincular os atributos de NVE ao item.', 'error');
            }

            redirect('homologacao/ficha/' . $post['id_item']);
        }

        return FALSE;
    }

    public function xhr_get_lista_grupos_tarif()
    {
        $query = $this->input->get('query');

        $by_empresa = false;
        if ($query != NULL) {
            $data = array();

            $this->load->model('grupo_tarifario_model');
            $itens = $this->grupo_tarifario_model->get_entries_no_inner($by_empresa, $query);
            if (!empty($itens)) {
                foreach ($itens as $key => $value) {
                    $data[] = array(
                        'value' => $value->descricao . ' - NCM: ' . $value->ncm_recomendada,
                        'data' => $value->id_grupo_tarifario,
                        'ncm' => $value->ncm_recomendada,
                        'descricao' => $value->descricao
                    );
                }
            }

            return response_json(array('suggestions' => $data));
        }

        return response_json(array('query' => $query, 'response' => 'success', 'data' => $data, 'status' => 200));
    }

    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            return;
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;

        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }


    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            return;
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE);

        $arr_dbdata = $ncm_item["defaultAttrs"];
        $arr_attr   = $ncm_item["listaAtributos"];

        $this->assoc_recursively($arr_dbdata, $arr_attr);

        return $arr_attr;
    }

    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            return;
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach ($arr_attr as &$attr) {
            $attr_template = !empty($attr["atributo"]) ? $attr["atributo"] : $attr;

            $attr_template["dbdata"] = ["codigo" => ""];

            foreach ($arr_dbdata as $dbdata) {
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"] = $dbdata;
                } else if ($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"] = $dbdata;
                }
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                foreach ($attr_template["condicionados"] as &$cond) {
                    if ($this->has_attr_cond($attr_template, $cond)) {
                        $this->_attrs['sim'][] = $attr_template["dbdata"]['atributo'];
                    }
                }

                if (!empty($this->_attrs['sim']) && in_array($attr_template['codigo'], $this->_attrs['sim'])) {
                    $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);
                }
            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }
            if (!empty($attr_template["dbdata"]["id_item"])) {
                $this->_attrs[] = $attr_template["dbdata"]['atributo'];
            }

            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;
            } else {
                $attr = $attr_template;
            }
        }
    }

    public function ajax_revisar_item()
    {
        // error_log("ajax_revisar_item");

        header('Content-Type: application/json; charset=utf-8');

        $id_item = $this->input->post('id_item');
        $part_number = $this->input->post('part_number');
        $estabelecimento = $this->input->post('estabelecimento');
        $descricao = $this->input->post('descricao');
        $id_empresa = $this->input->post('id_empresa');

        if (empty($id_item) || empty($part_number) || empty($estabelecimento) || empty($descricao) || empty($id_empresa)) {
            echo json_encode(array('success' => false, 'message' => 'Erro ao revisar item!'));
            return;
        }

        $this->load->library('Item/Status');
        $this->status->set_status("homologado");
        $this->status->update_item($part_number, $estabelecimento, $id_empresa);

        echo json_encode(array('success' => true, 'message' => 'Item revisado com sucesso!'));
    }

    public function ajax_reverter_item()
    {
        header('Content-Type: application/json; charset=utf-8');

        $id_item = $this->input->post('id_item');
        $part_number = $this->input->post('part_number');
        $estabelecimento = $this->input->post('estabelecimento');
        $id_empresa = $this->input->post('id_empresa');
        $descricao = $this->input->post('descricao');

        if (empty($id_item) || empty($part_number) || empty($estabelecimento) || empty($descricao) || empty($id_empresa)) {
            echo json_encode(array('success' => false, 'message' => 'Erro ao revisar item!'));
            return;
        }

        $this->load->model('item_model');

        $log_descricao_item = $this->item_model->buscar_log_descricao_item($part_number, $id_empresa, $estabelecimento, $descricao);

        if ($log_descricao_item) {
            $descricao_anterior = $log_descricao_item->descricao_anterior;
            $resultado = $this->item_model->atualizar_descricao_item($part_number, $id_empresa, $estabelecimento, $descricao_anterior);

            if ($resultado) {
                $this->load->library('Item/Status');
                $this->status->set_status("homologado");
                $this->status->update_item($part_number, $estabelecimento, $id_empresa);

                echo json_encode(array('success' => true, 'message' => 'Item atualizado com sucesso!'));
            } else {
                echo json_encode(array('success' => false, 'message' => 'Erro ao atualizar item!'));
            }
        } else {
            echo json_encode(array('success' => false, 'message' => 'Registro não encontrado!'));
        }
    }

    public function apply_default_filtersV2(&$grupos_tarifarios, $post)
    {
        $per_page = $this->input->get('per_page');

        if ($this->input->is_set('reset_filters')) {
            $this->cad_item_model->set_state_store_session(TRUE);
            $this->cad_item_model->clear_states();
        } else {
            $this->cad_item_model->set_state_store_session(TRUE);
            $this->cad_item_model->restore_state_from_session('filter.', 'post');
        }

        if ($data_inicio_importado_modal = $this->input->post('data_inicio_importado_modal')) {
            $this->cad_item_model->set_state('filter.data_inicio_importado_modal', $data_inicio_importado_modal);
        } else {
            $this->cad_item_model->unset_state('filter.data_inicio_importado_modal');
        }

        if ($data_fim_importado_modal = $this->input->post('data_fim_importado_modal')) {
            $this->cad_item_model->set_state('filter.data_fim_importado_modal', $data_fim_importado_modal);
        } else {
            $this->cad_item_model->unset_state('filter.data_fim_importado_modal');
        }


        if ($this->cad_item_model->get_state('filter.list_opt')) {
            $this->cad_item_model->set_state('filter.list_opt', $this->input->post('list_opt'));
        } else {
            $this->cad_item_model->set_state('filter.list_opt', 'homologar');
        }

        // somente as aprovações do usuário (logado)
        if (!$this->cad_item_model->get_state('filter.atribuido_para')) {
            $this->cad_item_model->set_state('filter.atribuido_para', sess_user_id());
        }

        if ($this->input->is_set('atribuido_para')) {
            $this->cad_item_model->set_state('filter.atribuido_para', $this->input->post('atribuido_para'));
        }

        if ($this->input->is_set('filtered')) {
            $this->cad_item_model->set_state('filter.filtered', $this->input->post('filtered'));
        } else {
            $this->cad_item_model->set_state('filter.filtered', 0);
        }

        if ($this->input->is_set('part_numbers')) {
            $part_numbers = $this->input->post('part_numbers');

            if (!is_array($part_numbers) && !empty($part_numbers)) {
                $this->cad_item_model->set_state('filter.part_numbers_view', $part_numbers);

                // $separator = get_company_separator(sess_user_company());
                // if (strpos($part_numbers, "\n") !== false) {
                //     $separator = !empty($separator) ? $separator : "\n";
                // } 
                // $part_numbers = str_replace(array("\t", "\r\n", "\s", "\n"), $separator, $this->input->post('part_numbers'));

                // $matches = array();
                // preg_match_all('/"([^"]*)"/', $part_numbers, $matches);
                // $btw_quotes = $matches[1];

                // $part_numbers = str_replace("*", "%", $part_numbers);
                // $part_numbers = preg_replace('/"([^"]*)"/', "", $part_numbers);

                // //Acha todos os partnumbers entre aspas simples
                // $matches_simple = array();
                // preg_match_all('~\'(.*?)\'~', $part_numbers, $matches_simple);
                // $btw_simple_quotes = $matches_simple[1];

                // //Retira da string todos os partnumbers entre aspas simples
                // $part_numbers = preg_replace('~\'(.*?)\'~', "", $part_numbers);

                // $part_numbers = !empty($separator) ? explode($separator, addslashes($part_numbers)) : [$part_numbers];
                // $part_numbers = array_filter($part_numbers);

                // if (!empty($btw_quotes)) {
                //     $addslashes_btw_quotes = implode(',', $btw_quotes);
                //     $btw_quotes = explode(",", addslashes($addslashes_btw_quotes));
                // }

                // $part_numbers = array_merge($part_numbers, $btw_quotes);
                // $part_numbers = array_merge($part_numbers, $btw_simple_quotes);

                $generic_part_numbers = array();

                // foreach ($part_numbers as $key => $part_number) {
                //     if (strpos($part_number, "%")) {
                //         $generic_part_numbers[] = $part_number;
                //         unset($part_numbers[$key]);
                //     }
                // }

                if (!empty($part_numbers)) {
                    $this->cad_item_model->set_state('filter.part_numbers', $part_numbers);
                } else {
                    $this->cad_item_model->unset_state('filter.part_numbers');
                }

                if (!empty($generic_part_numbers)) {
                    $this->cad_item_model->set_state('filter.generic_part_numbers', $generic_part_numbers);
                } else {
                    $this->cad_item_model->unset_state('filter.generic_part_numbers', $generic_part_numbers);
                }
            } else {
                $this->cad_item_model->unset_state('filter.part_numbers_view');
                $this->cad_item_model->unset_state('filter.part_numbers');
                $this->cad_item_model->unset_state('filter.generic_part_numbers');
            }
        }

        $this->cad_item_model->set_state('filter.id_empresa', sess_user_company());

        // Filtro: Tipo de Pesquisa
        if ($busca_part_number = $this->input->post('busca_part_number')) {
            $this->cad_item_model->set_state('filter.busca_part_number', $busca_part_number);
        } else {
            $this->cad_item_model->unset_state('filter.busca_part_number');
        }

        if ($busca_pendentes = $this->input->post('pendentes')) {
            $this->cad_item_model->set_state('filter.pendentes', $busca_pendentes);
        } else {
            $this->cad_item_model->unset_state('filter.pendentes');
        }
        if (isset($post['evento']) && count($post['evento']) > 0) {
            $evento = $post['evento'];
            if (is_array($evento) && $evento[0] != "") {
                $this->cad_item_model->set_state('filter.evento', $evento);
            } else if (!$per_page && $post) {
                $this->cad_item_model->unset_state('filter.evento');
            }
        } elseif (!$per_page && $post) {
            $this->cad_item_model->unset_state('filter.evento');
        }

        if ($busca_descricao = $this->input->post('busca_descricao')) {
            $this->cad_item_model->set_state('filter.busca_descricao', $busca_descricao);
        } else {
            $this->cad_item_model->unset_state('filter.busca_descricao');
        }

        if (!$busca_part_number && !$busca_descricao) {
            $this->cad_item_model->set_state('filter.busca_part_number', TRUE);
            $_POST['busca_part_number'] = 1;
        }

        if (!has_role('sysadmin') && !has_role('consultor')) {
            // $this->cad_item_model->set_state('filter.has_descricao_mercado_local', TRUE);
            $this->cad_item_model->unset_state('filter.has_descricao_mercado_local');
        } else {
            $this->cad_item_model->unset_state('filter.has_descricao_mercado_local');
        }

        // Filtro: Status Implementação
        $data['status_implementacao'] = [];

        if ($this->input->is_set('status_implementacao')) {
            $this->cad_item_model->set_state('filter.status_implementacao', $this->input->post('status_implementacao'));
            $data['status_implementacao'] = $this->input->post('status_implementacao');
        } elseif (!$per_page && $post) {
            $this->cad_item_model->unset_state('filter.status_implementacao');
        }

        // Filtro: Status Implementação
        $data['status_exportacao'] = [];

        if ($this->input->is_set('status_exportacao')) {
            $this->cad_item_model->set_state('filter.status_exportacao', $post['status_exportacao']);
            $data['status_exportacao'] = $post['status_exportacao'];
        } elseif (!$per_page && $post) {
            $this->cad_item_model->unset_state('filter.status_exportacao');
        }

        // Filtro: Simplus
        $data['status_simplus'] = [];

        if ($this->input->is_set('status_simplus')) {
            $this->cad_item_model->set_state('filter.status_simplus', $this->input->post('status_simplus'));
            $data['status_simplus'] = $this->input->post('status_simplus');
        } else {
            $this->cad_item_model->unset_state('filter.status_simplus');
        }

        // Filtro: Grupo Tarifário


        if ($id_grupo_tarifario = $this->cad_item_model->get_state('filter.id_grupo_tarifario')) {
            $data['id_grupo_tarifario'] = $id_grupo_tarifario;
        } else {
            $data['id_grupo_tarifario'] = NULL;
        }

        $grupos_tarifarios = $this->cad_item_model->get_grupos_tarifarios();

        if ($post_grupo_tarifario = $this->input->post('id_grupo_tarifario')) {
            if ($post_grupo_tarifario == -1) {
                $this->cad_item_model->unset_state('filter.id_grupo_tarifario');
                $data['id_grupo_tarifario'] = -1;
            } else {
                // Valida se o grupo tarifário pesquisado
                // ainda é válido para a próxima pesquisa
                $find_grupo_tarifario = false;

                foreach ($grupos_tarifarios as $struct) {
                    if ($post_grupo_tarifario == $struct->id_grupo_tarifario) {
                        $find_grupo_tarifario = $struct;
                        break;
                    }
                }

                if ($find_grupo_tarifario) {
                    $this->cad_item_model->set_state('filter.id_grupo_tarifario', $post_grupo_tarifario);
                    $data['id_grupo_tarifario'] = $post_grupo_tarifario;
                } else {
                    $data['id_grupo_tarifario'] = -1;
                }
            }
        } else {
            $this->cad_item_model->unset_state('filter.id_grupo_tarifario');
        }

        $data['prioridades_filter'] = isset($post['prioridade']) && is_array($post['prioridade']) ? $post['prioridade'] : array();

        if (!empty($data['prioridades_filter'])) {
            $this->cad_item_model->set_state('filter.prioridade', $data['prioridades_filter']);
        } elseif ((!$per_page) && $post) {
            $this->cad_item_model->unset_state('filter.prioridade');
        }

        $owner_filter = isset($post['owner']) && is_array($post['owner']) ? $post['owner'] : array();
        if (!empty($owner_filter)) {
            $this->cad_item_model->set_state('filter.owner', $owner_filter);
        } elseif (!$per_page && $post) {
            $this->cad_item_model->unset_state('filter.owner');
        }

        $integracao = $post['integracao'] ?? -1;
        if ($integracao != -1) {
            $this->cad_item_model->set_state('filter.integracao', $integracao);
        } elseif (!$per_page && $post) {
            $this->cad_item_model->unset_state('filter.integracao');
        }

        //Status Atributos
        if (!empty($post['status_atributos'])) {
            $status_atributos = $this->input->post('status_atributos');
            if (is_array($status_atributos) && $status_atributos[0] != "") {
                $this->cad_item_model->set_state('filter.status_atributos', $status_atributos);
            } else {
                $this->cad_item_model->unset_state('filter.status_atributos');
            }
        } elseif (!$per_page && $post && empty($post['status_atributos'])) {
            $this->cad_item_model->unset_state('filter.status_atributos');
        }

        //Status de Preenchimento
        if (!empty($post['status_preenchimento'])) {
            $status_preenchimento = $this->input->post('status_preenchimento');
            if (is_array($status_preenchimento) && $status_preenchimento[0] != "") {
                $this->cad_item_model->set_state('filter.status_preenchimento', $status_preenchimento);
            } else {
                $this->cad_item_model->unset_state('filter.status_preenchimento');
            }
        } elseif (!$per_page && $post && empty($post['status_preenchimento'])) {
            $this->cad_item_model->unset_state('filter.status_preenchimento');
        }

        //Status de Estabelecimento
        if (!empty($post['estabelecimento_modal'])) {
            $estabelecimento_modal = $this->input->post('estabelecimento_modal');
            if (is_array($estabelecimento_modal) && $estabelecimento_modal[0] != "" && $estabelecimento_modal[0] != -1) {
                $this->cad_item_model->set_state('filter.estabelecimento_modal', $estabelecimento_modal);
            } else {
                $this->cad_item_model->unset_state('filter.estabelecimento_modal');
            }
        } elseif (!$per_page && $post && empty($post['estabelecimento_modal'])) {
            $this->cad_item_model->unset_state('filter.estabelecimento_modal');
        }

        //Status de NCM Proposto
        if (!empty($post['ncm_proposta_modal'])) {
            $ncm_proposta_modal = $this->input->post('ncm_proposta_modal');
            if (is_array($ncm_proposta_modal) && $ncm_proposta_modal[0] != "" && $ncm_proposta_modal[0] != -1) {
                $this->cad_item_model->set_state('filter.ncm_proposta_modal', $ncm_proposta_modal);
            } else {
                $this->cad_item_model->unset_state('filter.ncm_proposta_modal');
            }
        } elseif (!$per_page && $post && empty($post['ncm_proposta_modal'])) {
            $this->cad_item_model->unset_state('filter.ncm_proposta_modal');
        }

        // Data início homologação
        if (!empty($post['data_inicio_homologacao_modal'])) {
            $data_inicio_homologacao_modal = $this->input->post('data_inicio_homologacao_modal');
            $data_inicio_homologacao_modal = str_replace('/', '-', $data_inicio_homologacao_modal);
            $data_inicio_homologacao_modal = date('Y-m-d', strtotime($data_inicio_homologacao_modal));
            $this->cad_item_model->set_state('filter.data_inicio_homologacao_modal', $data_inicio_homologacao_modal);
        } elseif (!$per_page && $post && empty($post['data_inicio_homologacao_modal'])) {
            $this->cad_item_model->unset_state('filter.data_inicio_homologacao_modal');
        }

        // Data fim homologação
        if (!empty($post['data_fim_homologacao_modal'])) {
            $data_fim_homologacao_modal = $this->input->post('data_fim_homologacao_modal');
            $data_fim_homologacao_modal = str_replace('/', '-', $data_fim_homologacao_modal);
            $data_fim_homologacao_modal = date('Y-m-d', strtotime($data_fim_homologacao_modal));
            $this->cad_item_model->set_state('filter.data_fim_homologacao_modal', $data_fim_homologacao_modal);
        } elseif (!$per_page && $post && empty($post['data_fim_homologacao_modal'])) {
            $this->cad_item_model->unset_state('filter.data_fim_homologacao_modal');
        }

        return $data;
    }

    public function has_attr_cond($attr, $cond)
    {
        $cond_op  = $cond["condicao"]["operador"]; // O operador para comparação.
        $cond_val = $cond["condicao"]["valor"];    // O valor para ser comparado.
        $cond_res = FALSE;                         // O resultado final da comparação.

        if (!empty($attr['multivalorado'])) {
            $attr_vals = $attr["dbdata"]["codigo"]; // Multivalorado.

            if (!\is_array($attr_vals)) {
                $attr_vals = explode(",", $attr_vals);
            }

            foreach ($attr_vals as $attr_val) {

                $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

                if ($cond_res) {
                    break;
                }
            }
        } else {
            $attr_val = $attr["dbdata"]["codigo"]; // Valor simples.

            if (strtoupper($attr["formaPreenchimento"]) == 'BOOLEANO' && $attr_val > 0) {
                $attr_val = "true";
            }

            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
        }

        return $cond_res;
    }

    public function gerar_exportacao_itens_to_api()
    {
        $this->load->library('PlanilhaGenerator');
        $generator = new PlanilhaGenerator();
        $resultado = $generator->gerarExportacaoItens();

        if ($resultado['success'] && isset($resultado['file_path'])) {
            $response = ['success' => true, 'file_path' => $resultado['file_path']];
        } else {
            $response = ['success' => false, 'error' => $resultado['error'] ?? 'Erro desconhecido'];
        }

        $this->output->set_content_type('application/json')->set_output(json_encode($response));
    }

    public function ajax_get_attrs($id_item = null, $ncm = null, $id_grupo_tarifario = null)
    {
        try {
            $this->load->model([
                "catalogo/produto_model",
                "grupo_tarifario_attr_model",
                "cad_item_attr_model",
            ]);

            // var_dump($id_item, $ncm, $id_grupo_tarifario); exit;

            $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($ncm));

            if (empty($ncm_item['listaAtributos'])) {
                throw new \Exception("Nenhum atributo encontrado.");
            }

            if (!empty($id_grupo_tarifario)) {
                $ncm_item["defaultAttrs"] = $this->grupo_tarifario_attr_model->check_attr_default_ncm($id_grupo_tarifario);
            } else if (!empty($id_item)) {
                $ncm_item["defaultAttrs"] = $this->cad_item_attr_model->get_attr($id_item);
            } else {
                throw new \Exception("Parametros invalidos ou nao informados.");
            }

            $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

            return [
                "success" => TRUE,
                "data"    => $ncm_item,
            ];
        } catch (\Exception $e) {
            return [
                "msg"     => $e->getMessage(),
                "success" => false,
            ];
        }
    }

    public function get_list_eventos()
    {
        try {
            $response = $this->cad_item_model->get_all_evento_in_item_by_empresa();

            if (!$response) {
                $response = [];
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (\Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar eventos: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_prioridades_by_empresa()
    {
        try {
            $this->load->model([
                'empresa_prioridades_model',
            ]);

            $response = $this->empresa_prioridades_model->get_entry(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar prioridades: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_owners_by_empresa()
    {
        try {
            $this->load->model([
                'empresa_model',
            ]);

            $empresa = $this->empresa_model->get_entry(sess_user_company());

            $response = $this->empresa_model->get_owners_by_empresa($empresa);

            if (!$response) {
                $response = [];
            }

            $response = array_map(function ($owner) {
                return [
                    'codigo' => $owner->codigo,
                    'descricao' => $owner->descricao,
                    'nomes' => $owner->nomes
                ];
            }, $response);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar owners: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_sistemas_origens()
    {
        try {

            $response = $this->item_model->get_list_sistemas_origens(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar sistemas de origem: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Returns a string description of the status preenchimento given a status code.
     *
     * @param int $statusCode
     * @return string
     */
    private function getStatusPreenchimentoDescription($statusCode)
    {
        switch ((int)$statusCode) {
            case 1: return 'Sem preenchimento';
            case 2: return 'Obrigatórios não preenchidos';
            case 3: return 'Opcionais não preenchidos';
            case 4: return 'Totalmente preenchidos';
            case 5: return 'Ncm sem atributos';
            default: return 'Sem preenchimento';
        }
    }

    public function ajax_pendente_homologacao()
    {
        try {
            if (!$this->input->is_ajax_request()) {
                throw new \Exception("Acesso negado.");
            }

            $id_item = $this->input->post('id_item');
            $part_number = $this->input->post('part_number');
            $estabelecimento = $this->input->post('estabelecimento');
            $id_empresa = $this->input->post('id_empresa');

            $item = $this->cad_item_model->get_entry($id_item);

            if (empty($item)) {
                throw new \Exception("Item não encontrado.");
            }

            if ($item->status_slug == 'homologar') {
                throw new \Exception("O item não pode ser marcado como Pendente de Homologação. Verifique se o status do item é 'Pendente de Homologação'.");
            }

            $this->load->library('Item/Status');
            $this->status->set_status("homologar");
            $this->status->update_item($part_number, $estabelecimento, $id_empresa);

            return response_json(array(
                'success' => true,
                'message' => 'Item atualizado com sucesso!',
                'data' => array(
                    'id' => $id_item,
                )
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'success' => false,
                'message' => $e->getMessage(),
            ), 400);
        }
    }
}
