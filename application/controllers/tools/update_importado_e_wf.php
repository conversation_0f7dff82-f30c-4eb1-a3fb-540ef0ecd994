<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

/**
 * Controller para processar planilha e atualizar COMEX/Item
 *
 * Lê o arquivo XLSX em assets/tmp/Alteraritensparaimportado.xlsx,
 * obtém os part numbers (removendo asteriscos e ignorando o cabeçalho),
 * insere/atualiza registros na tabela comex e ajusta wf_status_atributos
 * na tabela item quando necessário.
 *
 * Regras:
 * - id_base_comex = 1
 * - id_empresa = 1
 * - part_number_original = PN
 * - part_number_modificado = PN
 * - cnpj_raiz = 16701716
 * - unidade_negocio = estabelecimento do item
 * - ind_ecomex = 'EI'
 * - data_criacao = agora()
 * - Atualizar item.wf_status_atributos = 2 se estiver NULL ou = 1
 *
 * Execução: somente via CLI.
 */
/**
 * @property CI_Input $input
 * @property CI_Loader $load
 * @property CI_DB_query_builder $db
 * @property Item_model $item_model
 */
class Update_Importado_E_Wf extends CI_Controller // NOSONAR: manter nome para compatibilidade CI
{
    // Constantes e configuração
    private const EMPRESA_ID = 1;
    private const ID_BASE_COMEX = 1;
    private const CNPJ_RAIZ = '16701716';
    private const IND_ECOMEX = 'EI';
    private const FILE_RELATIVE_PATH = 'assets/tmp/Alteraritensparaimportado.xlsx';

    // Contadores de processamento
    private $countTotal = 0;
    private $countFound = 0;
    private $countNotFound = 0;
    private $countInserted = 0;
    private $countUpdated = 0;
    private $countItemStatusUpdated = 0;
    private $countSkippedInvalid = 0;
    private $startTime;

    // Arrays para armazenar detalhes dos erros e processamento
    private $partNumbersNotFound = [];
    private $partNumbersInvalid = [];
    private $partNumbersWithoutEstabelecimento = [];
    private $partNumbersProcessed = [];
    private $partNumbersErrors = [];

    // Configuração de logs
    private $logDirectory;
    private $logTimestamp;

    private $triggerName = 'tr_item_update_catalogo_envio';
    private $triggerSql = "CREATE DEFINER=`gestaotarifaria_stellantis`@`%` TRIGGER tr_item_update_catalogo_envio
        AFTER UPDATE ON item
        FOR EACH ROW
        BEGIN
            DECLARE v_id_item BIGINT(20) DEFAULT NULL;

            IF ((NEW.descricao_proposta_completa <> ''
                    OR (OLD.descricao_proposta_completa != NEW.descricao_proposta_completa
                        OR OLD.descricao_proposta_completa IS NULL AND NEW.descricao_proposta_completa IS NOT NULL))
                OR (NEW.id_status = 2)
                OR (NEW.wf_status_atributos = 7)) THEN
                SELECT
                    cad_item.id_item INTO v_id_item
                FROM cad_item
                WHERE 1=1
                    AND cad_item.part_number = NEW.part_number
                    AND cad_item.id_empresa = NEW.id_empresa
                    AND cad_item.estabelecimento = NEW.estabelecimento
                    LIMIT 1;
                IF v_id_item IS NOT NULL THEN
                    CALL sp_cria_catalogo_envio(v_id_item);
                END IF;
            END IF;
        END";

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Acesso permitido apenas via linha de comando');
        }

        // Autoload do Composer para usar PhpSpreadsheet
        $autoload1 = FCPATH . 'vendor/autoload.php';
        $autoload2 = APPPATH . '../vendor/autoload.php';
        if (!class_exists(Xlsx::class)) {
            if (file_exists($autoload1)) {
                require_once $autoload1; // NOSONAR: necessário para carregar dependências do Composer
            } elseif (file_exists($autoload2)) {
                require_once $autoload2; // NOSONAR
            }
        }

        $this->load->model('item_model');
        $this->load->database();

        // Inicializa sistema de logs
        $this->initializeLogging();
    }

    /**
     * Inicializa o sistema de logging detalhado
     */
    private function initializeLogging(): void
    {
        $this->logTimestamp = date('Y-m-d_H-i-s');
        $this->logDirectory = rtrim(FCPATH, '/\\') . '/assets/logs/update_importado_e_wf';

        // Cria diretório de logs se não existir
        if (!is_dir($this->logDirectory)) {
            mkdir($this->logDirectory, 0755, true);
        }

        echo "Logs serão salvos em: {$this->logDirectory}\n";
    }

    /**
     * Adiciona um part number à lista de não encontrados
     */
    private function addPartNumberNotFound(string $pn, int $linha): void
    {
        $this->partNumbersNotFound[] = [
            'part_number' => $pn,
            'linha' => $linha,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Adiciona um part number à lista de inválidos
     */
    private function addPartNumberInvalid(string $pn, int $linha, string $motivo = ''): void
    {
        $this->partNumbersInvalid[] = [
            'part_number' => $pn,
            'linha' => $linha,
            'motivo' => $motivo,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Adiciona um part number à lista de itens sem estabelecimento
     */
    private function addPartNumberWithoutEstabelecimento(string $pn, int $linha): void
    {
        $this->partNumbersWithoutEstabelecimento[] = [
            'part_number' => $pn,
            'linha' => $linha,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Adiciona um part number à lista de processados com sucesso
     */
    private function addPartNumberProcessed(string $pn, int $linha, string $estabelecimento, string $action): void
    {
        $this->partNumbersProcessed[] = [
            'part_number' => $pn,
            'linha' => $linha,
            'estabelecimento' => $estabelecimento,
            'action' => $action,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Adiciona um part number à lista de erros gerais
     */
    private function addPartNumberError(string $pn, int $linha, string $erro): void
    {
        $this->partNumbersErrors[] = [
            'part_number' => $pn,
            'linha' => $linha,
            'erro' => $erro,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Ponto de entrada: processa o XLSX e aplica as alterações.
     */
    public function index()
    {
        ini_set('memory_limit', '-1');
        set_time_limit(0);
        $this->db->query("SET SESSION sql_mode = ''");

        $this->startTime = microtime(true);
        $filePath = $this->resolveFilePath();

        echo "Iniciando processamento da planilha: {$filePath}\n";
        log_message('info', __METHOD__ . ": Início processamento planilha {$filePath}");

        // Remove a trigger se existir
        // $this->removerTrigger();

        if (!file_exists($filePath)) {
            $msg = "Arquivo não encontrado: {$filePath}";
            echo "ERRO: {$msg}\n";
            log_message('error', __METHOD__ . ': ' . $msg);
            return;
        }

        try {
            $this->processSpreadsheet($filePath);
            // Recria a trigger
            // $this->criarTrigger();
        } catch (Throwable $e) {
            echo "ERRO FATAL: " . $e->getMessage() . "\n";
            log_message('error', __METHOD__ . ': ERRO FATAL: ' . $e->getMessage());
        } finally {
            $this->generateDetailedReports();
            $this->printSummary();
        }
    }

    /**
     * Resolve o caminho absoluto do arquivo da planilha.
     */
    private function resolveFilePath(): string
    {
        // FCPATH normalmente aponta para a raiz pública (onde está index.php)
        $candidate1 = rtrim(FCPATH, '/\\') . '/' . self::FILE_RELATIVE_PATH;
        if (file_exists($candidate1)) {
            return $candidate1;
        }

        // Fallback: relativo ao APPPATH/.. (raiz do projeto)
        $candidate2 = realpath(APPPATH . '../') . '/' . self::FILE_RELATIVE_PATH;
        return $candidate2 ?: $candidate1;
    }

    /**
     * Processa a planilha XLSX linha por linha.
     */
    private function processSpreadsheet(string $filePath): void
    {
        $reader = new Xlsx();
        $reader->setReadDataOnly(true);

        $spreadsheet = $reader->load($filePath);
        $sheet = $spreadsheet->getActiveSheet();
        $highestRow = $sheet->getHighestRow();

        // Assumimos que o part number está na primeira coluna (A)
        $pnColumn = 'A';

        echo "Linhas detectadas (inclui cabeçalho): {$highestRow}\n";

        for ($row = 2; $row <= $highestRow; $row++) { // pula cabeçalho (linha 1)
            $raw = $sheet->getCell($pnColumn . $row)->getValue();
            $pn = $this->sanitizePartNumber($raw);
            $this->countTotal++;

            if (empty($pn)) {
                $this->countSkippedInvalid++;
                $this->addPartNumberInvalid($raw ?: 'VAZIO', $row, 'Part number vazio ou inválido');
                if ($this->countSkippedInvalid <= 5) {
                    echo "Linha {$row}: PN inválido/ vazio. Ignorado.\n";
                }
                continue;
            }

            try {
                $this->processPartNumber($pn, $row);
            } catch (Throwable $e) {
                $this->countSkippedInvalid++;
                $this->addPartNumberError($pn, $row, $e->getMessage());
                $msg = "Falha ao processar PN {$pn} na linha {$row}: " . $e->getMessage();
                echo "ERRO: {$msg}\n";
                log_message('error', __METHOD__ . ': ' . $msg);
            }

            if ($this->countTotal % 1000 === 0) {
                echo $this->progressLine();
            }
        }
    }

    /**
     * Processa um único part number: localiza item, upsert em comex e atualiza status do item.
     */
    private function processPartNumber(string $pn, int $linha): void
    {
        $item = $this->findItemByPartNumber($pn, self::EMPRESA_ID);
        if (!$item) {
            $this->countNotFound++;
            $this->addPartNumberNotFound($pn, $linha);
            return;
        }

        $this->countFound++;

        $estabelecimento = $item->estabelecimento ?? null;
        if (empty($estabelecimento)) {
            // Sem estabelecimento não conseguimos montar a chave do COMEX
            $this->countSkippedInvalid++;
            $this->addPartNumberWithoutEstabelecimento($pn, $linha);
            log_message('warning', __METHOD__ . ": Item sem estabelecimento para PN {$pn}. Ignorado.");
            return;
        }

        $action = $this->upsertComex($pn, $estabelecimento);
        if ($action === 'insert') {
            $this->countInserted++;
        } elseif ($action === 'update') {
            $this->countUpdated++;
        }

        if ($this->updateItemStatusIfNeeded($pn, $estabelecimento, self::EMPRESA_ID)) {
            $this->countItemStatusUpdated++;
        }

        // Registra o processamento bem-sucedido
        $this->addPartNumberProcessed($pn, $linha, $estabelecimento, $action);
    }

    /**
     * Remove asteriscos e espaços do part number.
     */
    private function sanitizePartNumber($value): string
    {
        if ($value === null) {
            return '';
        }
        $pn = trim((string) $value);
        // Remove asteriscos no início e fim, e espaços excedentes
        $pn = trim($pn, " *\t\n\r\0\x0B");
        return $pn;
    }

    /**
     * Busca o item utilizando item_model->get_entry_by_pn (sem LIKE).
     */
    private function findItemByPartNumber(string $pn, int $idEmpresa)
    {
        try {
            $item = $this->item_model->get_entry_by_pn($pn, $idEmpresa, null);
        } catch (Throwable $e) {
            // Fallback: consulta direta se ocorrer algum problema no model
            log_message('warning', __METHOD__ . ": Falha em get_entry_by_pn para PN {$pn}: " . $e->getMessage());
            $this->db->where('part_number', $pn);
            $this->db->where('id_empresa', $idEmpresa);
            $query = $this->db->get('item');
            $item = $query->row();
        }

        // Alguns models retornam array; padroniza para objeto (primeiro elemento)
        if (is_array($item)) {
            if (empty($item)) {
                return null;
            }
            return (object) $item[0];
        }
        return $item ?: null;
    }

    /**
     * Insere ou atualiza registro na tabela comex para o PN/estabelecimento.
     * Retorna 'insert', 'update' ou 'none'.
     */
    private function upsertComex(string $pn, string $estabelecimento): string
    {
        $now = date('Y-m-d H:i:s');

        $this->db->where('part_number_original', $pn);
        $this->db->where('id_empresa', self::EMPRESA_ID);
        $this->db->where('unidade_negocio', $estabelecimento);
        $existing = $this->db->get('comex')->row();

        $data = [
            'id_base_comex'       => self::ID_BASE_COMEX,
            'id_empresa'          => self::EMPRESA_ID,
            'part_number_original' => $pn,
            'part_number_modificado' => $pn,
            'cnpj_raiz'           => self::CNPJ_RAIZ,
            'unidade_negocio'     => $estabelecimento,
            'ind_ecomex'          => self::IND_ECOMEX,
            'data_criacao'        => $now,
        ];

        if ($existing) {
            // Atualiza somente campos relevantes, mantendo chaves
            $this->db->where('part_number_original', $pn)
                ->where('id_empresa', self::EMPRESA_ID)
                ->where('unidade_negocio', $estabelecimento)
                ->update('comex', $data);
            return 'update';
        } else {
            $this->db->insert('comex', $data);
            return 'insert';
        }
    }

    /**
     * Atualiza item.wf_status_atributos = 2 se estiver NULL ou = 1.
     * Retorna true se atualizou, false caso contrário.
     */
    private function updateItemStatusIfNeeded(string $pn, string $estabelecimento, int $idEmpresa): bool
    {
        // Busca valor atual para decidir
        $this->db->select('wf_status_atributos')
            ->where('part_number', $pn)
            ->where('id_empresa', $idEmpresa)
            ->where('estabelecimento', $estabelecimento);
        $row = $this->db->get('item')->row();

        $current = $row->wf_status_atributos ?? null;
        if ($current === null || (string)$current === '1' || (int)$current === 1) {
            $this->db->set('wf_status_atributos', 2)
                ->where('part_number', $pn)
                ->where('id_empresa', $idEmpresa)
                ->where('estabelecimento', $estabelecimento)
                ->update('item');
            return $this->db->affected_rows() > 0;
        }
        return false;
    }

    /**
     * Gera relatórios detalhados em arquivos CSV e TXT
     */
    private function generateDetailedReports(): void
    {
        echo "\nGerando relatórios detalhados...\n";

        // Relatório de part numbers não encontrados
        if (!empty($this->partNumbersNotFound)) {
            $this->generateCSVReport(
                $this->partNumbersNotFound,
                'part_numbers_nao_encontrados',
                ['Part Number', 'Linha', 'Timestamp']
            );
        }

        // Relatório de part numbers inválidos
        if (!empty($this->partNumbersInvalid)) {
            $this->generateCSVReport(
                $this->partNumbersInvalid,
                'part_numbers_invalidos',
                ['Part Number', 'Linha', 'Motivo', 'Timestamp']
            );
        }

        // Relatório de part numbers sem estabelecimento
        if (!empty($this->partNumbersWithoutEstabelecimento)) {
            $this->generateCSVReport(
                $this->partNumbersWithoutEstabelecimento,
                'part_numbers_sem_estabelecimento',
                ['Part Number', 'Linha', 'Timestamp']
            );
        }

        // Relatório de erros gerais
        if (!empty($this->partNumbersErrors)) {
            $this->generateCSVReport(
                $this->partNumbersErrors,
                'part_numbers_erros_gerais',
                ['Part Number', 'Linha', 'Erro', 'Timestamp']
            );
        }

        // Relatório de processados com sucesso (opcional, apenas se houver muitos)
        if (!empty($this->partNumbersProcessed) && count($this->partNumbersProcessed) <= 10000) {
            $this->generateCSVReport(
                $this->partNumbersProcessed,
                'part_numbers_processados_sucesso',
                ['Part Number', 'Linha', 'Estabelecimento', 'Ação', 'Timestamp']
            );
        }

        // Gera resumo consolidado
        $this->generateSummaryReport();
    }

    /**
     * Gera um arquivo CSV com os dados fornecidos
     */
    private function generateCSVReport(array $data, string $filename, array $headers): void
    {
        $filepath = $this->logDirectory . "/{$filename}_{$this->logTimestamp}.csv";

        $file = fopen($filepath, 'w');
        if (!$file) {
            echo "ERRO: Não foi possível criar arquivo {$filepath}\n";
            return;
        }

        // Escreve cabeçalho
        fputcsv($file, $headers, ';');

        // Escreve dados
        foreach ($data as $row) {
            $csvRow = [];
            foreach ($row as $value) {
                $csvRow[] = $value;
            }
            fputcsv($file, $csvRow, ';');
        }

        fclose($file);
        echo "Relatório gerado: {$filepath} (" . count($data) . " registros)\n";
    }

    /**
     * Gera um relatório resumo em formato texto
     */
    private function generateSummaryReport(): void
    {
        $filepath = $this->logDirectory . "/resumo_processamento_{$this->logTimestamp}.txt";

        $content = "RELATÓRIO DE PROCESSAMENTO - UPDATE IMPORTADO E WF\n";
        $content .= "Data/Hora: " . date('d/m/Y H:i:s') . "\n";
        $content .= "Timestamp: {$this->logTimestamp}\n";
        $content .= str_repeat("=", 60) . "\n\n";

        $content .= "RESUMO GERAL:\n";
        $content .= "- Total de linhas processadas: {$this->countTotal}\n";
        $content .= "- Itens encontrados: {$this->countFound}\n";
        $content .= "- Part numbers NÃO encontrados: {$this->countNotFound}\n";
        $content .= "- Part numbers inválidos/erros: {$this->countSkippedInvalid}\n";
        $content .= "- COMEX inseridos: {$this->countInserted}\n";
        $content .= "- COMEX atualizados: {$this->countUpdated}\n";
        $content .= "- Itens com wf_status_atributos ajustado: {$this->countItemStatusUpdated}\n\n";

        $content .= "DETALHAMENTO DOS ERROS:\n";
        $content .= "- Part numbers não encontrados na base: " . count($this->partNumbersNotFound) . "\n";
        $content .= "- Part numbers inválidos/vazios: " . count($this->partNumbersInvalid) . "\n";
        $content .= "- Part numbers sem estabelecimento: " . count($this->partNumbersWithoutEstabelecimento) . "\n";
        $content .= "- Erros gerais de processamento: " . count($this->partNumbersErrors) . "\n\n";

        if (!empty($this->partNumbersNotFound)) {
            $content .= "PRIMEIROS 20 PART NUMBERS NÃO ENCONTRADOS:\n";
            $sample = array_slice($this->partNumbersNotFound, 0, 20);
            foreach ($sample as $item) {
                $content .= "- {$item['part_number']} (linha {$item['linha']})\n";
            }
            if (count($this->partNumbersNotFound) > 20) {
                $remaining = count($this->partNumbersNotFound) - 20;
                $content .= "... e mais {$remaining} part numbers (veja arquivo CSV completo)\n";
            }
            $content .= "\n";
        }

        $elapsed = microtime(true) - $this->startTime;
        $content .= "Tempo total de processamento: " . number_format($elapsed, 1) . " segundos\n";

        file_put_contents($filepath, $content);
        echo "Resumo detalhado gerado: {$filepath}\n";
    }

    /**
     * Linha de progresso compacta a cada 1000 itens.
     */
    private function progressLine(): string
    {
        $elapsed = microtime(true) - $this->startTime;
        return sprintf(
            "[Progresso] total=%d, encontrados=%d, comex_ins=%d, comex_upd=%d, item_upd=%d, não_encontrados=%d, inválidos=%d, t=%.1fs\n",
            $this->countTotal,
            $this->countFound,
            $this->countInserted,
            $this->countUpdated,
            $this->countItemStatusUpdated,
            $this->countNotFound,
            $this->countSkippedInvalid,
            $elapsed
        );
    }

    /**
     * Imprime um resumo final do processamento.
     */
    private function printSummary(): void
    {
        $elapsed = microtime(true) - $this->startTime;
        $summary = sprintf(
            "\n" . str_repeat("=", 60) . "\n" .
                "RESUMO FINAL DO PROCESSAMENTO\n" .
                str_repeat("=", 60) . "\n" .
                "- Lidas (sem cabeçalho): %d\n" .
                "- Itens encontrados: %d\n" .
                "- COMEX inseridos: %d\n" .
                "- COMEX atualizados: %d\n" .
                "- Itens com wf_status_atributos ajustado: %d\n" .
                "- Part numbers NÃO encontrados: %d\n" .
                "- Inválidos/erros: %d\n" .
                "- Tempo total: %.1fs\n" .
                str_repeat("-", 60) . "\n" .
                "DETALHAMENTO DOS ERROS:\n" .
                "- Não encontrados na base: %d\n" .
                "- Inválidos/vazios: %d\n" .
                "- Sem estabelecimento: %d\n" .
                "- Erros gerais: %d\n" .
                str_repeat("-", 60) . "\n" .
                "📁 Logs detalhados salvos em: %s\n" .
                "📊 Verifique os arquivos CSV para lista completa dos part numbers com erro\n" .
                str_repeat("=", 60) . "\n",
            $this->countTotal,
            $this->countFound,
            $this->countInserted,
            $this->countUpdated,
            $this->countItemStatusUpdated,
            $this->countNotFound,
            $this->countSkippedInvalid,
            $elapsed,
            count($this->partNumbersNotFound),
            count($this->partNumbersInvalid),
            count($this->partNumbersWithoutEstabelecimento),
            count($this->partNumbersErrors),
            $this->logDirectory
        );

        echo $summary;
        log_message('info', __METHOD__ . ': ' . str_replace("\n", ' ', $summary));

        // Exibe informações específicas sobre part numbers não encontrados
        if ($this->countNotFound > 0) {
            echo "\n🚨 ATENÇÃO: {$this->countNotFound} part numbers não foram encontrados na base!\n";
            echo "📋 Lista completa disponível em: {$this->logDirectory}/part_numbers_nao_encontrados_{$this->logTimestamp}.csv\n";

            if (count($this->partNumbersNotFound) > 0) {
                echo "\n📝 Primeiros 10 part numbers não encontrados:\n";
                $sample = array_slice($this->partNumbersNotFound, 0, 10);
                foreach ($sample as $item) {
                    echo "   - {$item['part_number']} (linha {$item['linha']})\n";
                }
                if (count($this->partNumbersNotFound) > 10) {
                    $remaining = count($this->partNumbersNotFound) - 10;
                    echo "   ... e mais {$remaining} part numbers\n";
                }
            }
        }
    }

    /**
     * Remove a trigger de atualização de status de atributos de itens se existir
     *
     * Caso a trigger exista, remove-a e imprime uma mensagem de sucesso.
     * Caso haja um erro, imprime uma mensagem de aviso com o erro e o loga como erro.
     */
    private function removerTrigger()
    {
        try {
            $this->db->query("DROP TRIGGER IF EXISTS {$this->triggerName}");
            echo "Trigger {$this->triggerName} removida com sucesso.\n";
        } catch (Exception $e) {
            echo "Aviso: Erro ao remover trigger: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao remover trigger {$this->triggerName}: " . $e->getMessage());
        }
    }

    /**
     * Cria a trigger de atualização de status de atributos de itens
     *
     * Caso a trigger exista, remove-a e a recria com o sql armazenado na
     * propriedade $trigger_sql.
     *
     * Caso haja um erro, imprime uma mensagem de aviso com o erro e o loga como erro.
     */
    private function criarTrigger()
    {
        try {
            $this->db->query($this->triggerSql);
            echo "Trigger {$this->triggerName} criada com sucesso.\n";
        } catch (Exception $e) {
            echo "ERRO: Falha ao criar trigger: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao criar trigger {$this->triggerName}: " . $e->getMessage());
            throw $e;
        }
    }
}
