<?php if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}
/**
 * Classe controller para Dad<PERSON> Técnicos
 * @property MY_Loader $load
 * @property MY_Controller $MY_Controller
 * @property Item_model $item_model
 * @property Status $status
 * @property Usuario_model $usuario_model
 * @property Suframa_model $suframa_model
 * @property Empresa_model $empresa_model
 * @property Cad_item_model $cad_item_model
 * @property Empresa_pais_model $empresa_pais_model
 * @property Unidade_negocio_model $unidade_negocio_model
 * @property Empresa_prioridades_model $empresa_prioridades_model
 * @property Anexo_model $anexo_model
 * @property Produto_model $produto_model
 * @property Ncm_model $ncm_model
 * @property Ctr_resposta_model $ctr_resposta_model
 * @property Ctr_pendencias_pergunta_model $ctr_pendencias_pergunta_model
 * @property Ctr_anexo_resposta_model $ctr_anexo_resposta_model
 * @property Comex_model $comex_model
 * @property Item_pais_model $item_pais_model
 * @property Grupo_tarifario_model $grupo_tarifario_model
 * @property Oculto_atribuir_grupo_model $oculto_atribuir_grupo_model
 * @property Cad_item_nve_model $cad_item_nve_model
 * @property Item_log_model $item_log_model
 * @property Ex_tarifario_model $ex_tarifario_model
 * @property Ex_ii_model $ex_ii_model
 * @property Ex_ipi_model $ex_ipi_model
 * @property Ex_icms_model $ex_icms_model
 * @property Nve_atributo_model $nve_atributo_model
 * @property Nve_model $nve_model
 * @property Lessin_model $lessin_model
 * @property Cad_item_attr_model $cad_item_attr_model
 * @property Cad_item_homologacao_model $cad_item_homologacao_model
 * @property Nve_atributo_model $nve_atributo_model
 * @property Log_notificacao_usuario_model $log_notificacao_usuario_model
 * @property Email $email
 * @property Diana $diana
 * @property Filter_manager $filter_manager
 * @property Breadcrumbs $breadcrumbs
 * @property Excel $excel
 * @property LessinUseCases $lessinusecases
 * @property MY_Input $input
 * @property MY_Output $output
 * @property MY_Session $session
 * @property MY_Security $security
 * @property MY_Config $config
 * @property MY_Database $db
 * @property MY_Log $log
 * @property Pagination $pagination
 */
class Atribuir_grupo extends MY_Controller
{
    public $title;
    public $breadcrumbs;
    public $formatador_helper;
    public $filter_helper;
    public $excel;
    public $lessinusecases;

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('dados_tecnicos')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->library('diana');
        $this->load->library('filter_manager');
        $this->load->helper('formatador_helper');
        $this->load->helper('filter_helper');

        $this->load->model(array(
            'item_model',
            'ncm_model',
            'ncm_capitulo_model',
            'ncm_capitulo_grupos_model',
            'ctr_resposta_model',
            'empresa_prioridades_model',
            'empresa_model'
        ));

        $this->item_model->set_state_store_session(TRUE);
    }

    /**
     * Prepara os dados de filtros para a view
     * @return array
     * @access private
     */
    private function prepare_filter_data($campos_adicionais, $funcoes_adicionais)
    {
        if (empty($campos_adicionais)) {
            // Carregar dados da empresa para verificar configurações
            $id_empresa = sess_user_company();
            $empresa = $this->empresa_model->get_entry($id_empresa);
            $campos_adicionais = explode('|', $empresa->campos_adicionais);
        }

        if (empty($funcoes_adicionais)) {
            // Carregar dados da empresa para verificar configurações
            $id_empresa = sess_user_company();
            $empresa = $this->empresa_model->get_entry($id_empresa);
            $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
        }

        // Verificar se o Farol SLA está habilitado para a empresa
        $has_farol_sla = in_array('habilitar_farol_dados_tecnicos', $campos_adicionais);
        $has_status_triagem_diana = in_array('status_triagem_diana', $funcoes_adicionais);

        // Carregar filtros salvos do backend para injetar no JavaScript
        $filtros_salvos = [
            'eventosSelecionados' => $this->item_model->get_state('filter.pacotes_eventos') ?: [],
            'sistemasOrigemSelecionados' => $this->item_model->get_state('filter.sistema_origem') ?: [],
            'ownersSelecionados' => $this->item_model->get_state('filter.owner') ?: [],
            'preAgrupamentoSelecionados' => $this->item_model->get_state('filter.pre_agrupamento') ?: [],
            'prioridadesSelecionadas' => $this->item_model->get_state('filter.prioridade') ?: [],
            'usuariosSelecionados' => $this->item_model->get_state('filter.atribuido_para') ?: '',
            'statusSelecionados' => $this->item_model->get_state('filter.status_classificacao_fiscal') ?: [],
            'triagemDianaFalhaSelecionado' => $this->item_model->get_state('filter.triagem_diana_falha'),
            'novoMaterialSelecionado' => $this->item_model->get_state('filter.novo_material') ?: [],
            'estabelecimentoSelecionado' => $this->item_model->get_state('filter.estabelecimento') ?: [],
            'importadoSelecionado' => $this->item_model->get_state('filter.importado') ?: [],
            'farolSlaSelecionado' => $this->item_model->get_state('filter.farol_sla') ?: [],
            'dataCriacaoFrom' => $this->item_model->get_state('filter.data_criacao_from') ?: '',
            'dataCriacaoTo' => $this->item_model->get_state('filter.data_criacao_to') ?: '',
            'dataModificacaoFrom' => $this->item_model->get_state('filter.data_modificacao_from') ?: '',
            'dataModificacaoTo' => $this->item_model->get_state('filter.data_modificacao_to') ?: '',
            'dataImportadoFrom' => $this->item_model->get_state('filter.data_importado_from') ?: '',
            'dataImportadoTo' => $this->item_model->get_state('filter.data_importado_to') ?: ''
        ];

        // Mapear filtros salvos para os nomes usados na configuração dos filtros
        $filtros = [
            'atribuido_para' => $this->item_model->get_state('filter.atribuido_para') ?: [],
            'owner' => $this->item_model->get_state('filter.owner') ?: [],
            'pre_agrupamento' => $this->item_model->get_state('filter.pre_agrupamento') ?: [],
            'pacotes_eventos' => $this->item_model->get_state('filter.pacotes_eventos') ?: [],
            'prioridade' => $this->item_model->get_state('filter.prioridade') ?: [],
            'status_classificacao_fiscal' => $this->item_model->get_state('filter.status_classificacao_fiscal') ?: [],
            'sistema_origem' => $this->item_model->get_state('filter.sistema_origem') ?: [],
            'triagem_diana_falha' => $this->item_model->get_state('filter.triagem_diana_falha') ?: false,
            'novo_material' => $this->item_model->get_state('filter.novo_material') ?: [],
            'estabelecimento' => $this->item_model->get_state('filter.estabelecimento') ?: [],
            'importado' => $this->item_model->get_state('filter.importado') ?: [],
            // Verificar se o filtro farol_sla está habilitado para a empresa antes de aplicar
            'farol_sla' => $this->item_model->get_state('filter.farol_sla') ?: [],
            'data_criacao' => [
                'from' => $this->item_model->get_state('filter.data_criacao_from') ?: '',
                'to' => $this->item_model->get_state('filter.data_criacao_to') ?: ''
            ],
            'data_modificacao' => [
                'from' => $this->item_model->get_state('filter.data_modificacao_from') ?: '',
                'to' => $this->item_model->get_state('filter.data_modificacao_to') ?: ''
            ],
            'data_importado' => [
                'from' => $this->item_model->get_state('filter.data_importado_from') ?: '',
                'to' => $this->item_model->get_state('filter.data_importado_to') ?: ''
            ]
        ];

        /**
         * Configuração de Filtros para Dados Técnicos
         *
         * Este arquivo demonstra como configurar filtros com diferentes comportamentos:
         *
         * 1. FILTROS SIMPLES (hardcoded):
         *    - 'options' com array fixo
         *    - 'ajax_url' vazio
         *    - 'select_config' para customizar comportamento
         *
         * 2. FILTROS DINÂMICOS (AJAX):
         *    - 'options' vazio
         *    - 'ajax_url' com endpoint
         *    - Carregamento lazy automático
         *
         * 3. CONFIGURAÇÕES DO SELECTPICKER:
         *    - 'multiple': true/false (padrão: true)
         *    - 'data-live-search': true/false (padrão: true)
         *    - 'data-actions-box': true/false (padrão: true)
         *    - Outros atributos do Bootstrap Select
         *
         * Exemplo de uso em outras telas:
         * $filters = [
         *     [
         *         'type' => 'select',
         *         'name' => 'meu_filtro',
         *         'label' => 'Meu Filtro',
         *         'options' => [['value' => '1', 'label' => 'Opção 1']],
         *         'select_config' => ['multiple' => false, 'data-live-search' => 'false']
         *     ]
         * ];
         */

        $basic_filters = [
            [
                'type' => 'select',
                'name' => 'atribuido_para',
                'title' => 'Selecione...',
                'label' => 'Atribuídos para',
                'options' => [],
                'value' => $filtros['atribuido_para'] ?? '',
                'ajax_url' => base_url('atribuir_grupo/ajax_get_usuarios_filtro'),
                'select_config' => [
                    'multiple' => false, // Select simples, não múltiplo
                    'data-live-search' => 'true', // Busca habilitada para usuários
                    'data-actions-box' => 'false' // Desabilitar ações (Selecionar Todos/Nenhum)
                ]
            ],
            [
                'type' => 'select',
                'name' => 'owner',
                'title' => 'Selecione...',
                'label' => 'Owner',
                'options' => [],
                'value' => $filtros['owner'] ?? [],
                'select_config' => [
                    'multiple' => true, // Select múltiplo
                    'data-live-search' => 'true', // Busca habilitada para usuários
                    'data-actions-box' => 'true' // Ações (Selecionar Todos/Nenhum)
                ],
                'ajax_url' => base_url('atribuir_grupo/get_list_owners_by_empresa'),
            ],
            [
                'type' => 'select',
                'name' => 'pre_agrupamento',
                'title' => 'Selecione...',
                'label' => 'Pré-agrupamento',
                'options' => [],
                'value' => $filtros['pre_agrupamento'] ?? [],
                'ajax_url' => base_url('atribuir_grupo/ajax_get_tags_modal'),
                'filter_options' => 'true',
            ],
            [
                'type' => 'select',
                'name' => 'pacotes_eventos',
                'title' => 'Selecione...',
                'label' => 'Pacotes / Eventos',
                'options' => [],
                'value' => $filtros['pacotes_eventos'] ?? [],
                'select_config' => [
                    'multiple' => true, // Select múltiplo
                    'data-live-search' => 'true', // Busca habilitada para usuários
                    'data-actions-box' => 'true' // Ações (Selecionar Todos/Nenhum)
                ],
                'ajax_url' => base_url('atribuir_grupo/get_list_eventos'),
            ],
            [
                'type' => 'select',
                'name' => 'prioridade',
                'title' => 'Selecione...',
                'label' => 'Prioridade',
                'options' => [],
                'value' => $filtros['prioridade'] ?? [],
                'select_config' => [
                    'multiple' => true, // Select múltiplo
                    'data-live-search' => 'true', // Busca habilitada para usuários
                    'data-actions-box' => 'true' // Ações (Selecionar Todos/Nenhum)
                ],
                'ajax_url' => base_url('atribuir_grupo/get_list_prioridades_by_empresa'),
            ],
            [
                'type' => 'select',
                'name' => 'status_classificacao_fiscal',
                'title' => 'Selecione...',
                'label' => 'Status de classificação fiscal',
                'options' => [],
                'value' => $filtros['status_classificacao_fiscal'] ?? [],
                'ajax_url' => base_url('atribuir_grupo/ajax_get_status_classificacao_fiscal'),
                'select_config' => [
                    'multiple' => true, // Select múltiplo
                    'data-live-search' => 'true', // Busca habilitada para usuários
                    'data-actions-box' => 'true' // Ações (Selecionar Todos/Nenhum)
                ]
            ],
            [
                'type' => 'select',
                'name' => 'sistema_origem',
                'title' => 'Selecione...',
                'label' => 'Sistema de origem',
                'options' => [],
                'value' => $filtros['sistema_origem'] ?? [],
                'ajax_url' => base_url('atribuir_grupo/get_list_sistemas_origens'),
            ]
        ];

        // Adicionar filtro Triagem Diana somente se habilitado para a empresa
        if ($has_status_triagem_diana) {
            $basic_filters[] = [
                'type' => 'checkbox',
                'name' => 'triagem_diana_falha',
                'title' => 'Selecione...',
                'label' => 'Falha na triagem',
                'value' => $filtros['triagem_diana_falha'] ?? false,
            ];
        }


        $advanced_filters = [
            [
                'type' => 'select',
                'name' => 'novo_material',
                'title' => 'Selecione...',
                'label' => 'Novo material',
                'options' => [
                    ['value' => 'S', 'label' => 'SIM'],
                    ['value' => 'N', 'label' => 'NÃO']
                ],
                'value' => $filtros['novo_material'] ?? [],
                'ajax_url' => '', // Vazio para não fazer carregamento AJAX
                'select_config' => [
                    'multiple' => false,
                    'data-actions-box' => 'true',
                    'data-deselect-all-text' => 'Nenhum',
                    'data-select-all-text' => 'Todos',
                    'data-live-search' => 'false'
                ]
            ],
            [
                'type' => 'select',
                'name' => 'estabelecimento',
                'title' => 'Selecione...',
                'label' => 'Estabelecimento',
                'options' => [],
                'value' => $filtros['estabelecimento'] ?? [],
                'ajax_url' => base_url('atribuir_grupo/get_simple_filter_options?type=estabelecimento'),
                'select_config' => [
                    'data-live-search' => 'true',
                ]
            ],
            [
                'type' => 'select',
                'name' => 'importado',
                'title' => 'Selecione...',
                'label' => 'Importado',
                'options' => [
                    ['value' => 'S', 'label' => 'SIM'],
                    ['value' => 'N', 'label' => 'NÃO']
                ],
                'value' => $filtros['importado'] ?? [],
                'ajax_url' => '', // Vazio para não fazer carregamento AJAX
                'select_config' => [
                    'multiple' => false,
                    'data-live-search' => 'false',
                    'data-actions-box' => 'false'
                ]
            ],
        ];

        // Adicionar filtro Farol SLA somente se habilitado para a empresa
        if ($has_farol_sla) {
            $advanced_filters[] = [
                'type' => 'select',
                'name' => 'farol_sla',
                'title' => 'Selecione...',
                'label' => 'Farol SLA',
                'options' => [
                    ['value' => 'verde', 'label' => 'Verde'],
                    ['value' => 'amarelo', 'label' => 'Amarelo'],
                    ['value' => 'vermelho', 'label' => 'Vermelho']
                ],
                'value' => $filtros['farol_sla'] ?? [],
                'ajax_url' => '', // Vazio para não fazer carregamento AJAX
                'select_config' => [
                    'multiple' => true,
                    'data-live-search' => 'false',
                    'data-actions-box' => 'true'
                ]
            ];
        }

        // Adicionar filtros de data (sempre exibidos)
        $advanced_filters[] = [
            'type' => 'date_range',
            'name' => 'data_criacao',
            'title' => 'Selecione...',
            'label' => 'Data de criação',
            'value' => $filtros['data_criacao'] ?? ['from' => '', 'to' => ''],
        ];
        $advanced_filters[] = [
            'type' => 'date_range',
            'name' => 'data_modificacao',
            'title' => 'Selecione...',
            'label' => 'Data de modificação',
            'value' => $filtros['data_modificacao'] ?? ['from' => '', 'to' => ''],
        ];
        $advanced_filters[] = [
            'type' => 'date_range',
            'name' => 'data_importado',
            'title' => 'Selecione...',
            'label' => 'Data que tornou-se importado',
            'value' => $filtros['data_importado'] ?? ['from' => '', 'to' => ''],
        ];

        return [
            'filtros_salvos' => $filtros_salvos,
            'filtros' => $filtros,
            'basic_filters' => $basic_filters,
            'advanced_filters' => $advanced_filters
        ];
    }

    /**
     * Método para carregar itens com filtros aplicados
     * Baseado na lógica do ajax_get_itens, mas retorna os dados diretamente
     * @return array
     * @access private
     */
    private function load_filtered_items()
    {
        $id_empresa = sess_user_company();
        $empresa = $this->empresa_model->get_entry($id_empresa);

        $campos_adicionais = explode('|', $empresa->campos_adicionais);
        $hasOwner = in_array('owner', $campos_adicionais);

        $has_farol_sla = in_array('habilitar_farol_dados_tecnicos', $campos_adicionais);

        // Valores padrão para order, item_input e tag
        $order_data = $this->input->post('order') ?? ['order' => 'i.part_number', 'by' => 'asc'];
        $item_input = $this->input->post('item_input') ?? '';
        $tag = $this->input->post('tag') ?? '';

        $order_by = $order_data['order'] . ' ' . $order_data['by'];

        // Garantir que order_by não seja vazio
        if (empty(trim($order_by))) {
            $order_by = 'part_number asc';
        }

        // Configuração de paginação
        $per_page = 50; // 50 itens por página conforme solicitado
        $page = $this->input->get('per_page') ?: $this->input->post('per_page') ?: 1;
        $page = max(1, (int)$page); // Garantir que seja pelo menos 1
        $offset = ($page - 1) * $per_page;

        // Verificar permissões e carregar dados de owner se necessário
        $owner_user = null;
        $part_numbers_created_for_user = array();

        if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
            $owner_user = $this->item_model->get_user_owner_codes(sess_user_id());
            $part_numbers_created_for_user = $this->item_model->get_items_created_user(sess_user_id());

            if (!empty($part_numbers_created_for_user)) {
                $part_numbers_created_for_user = array_column($part_numbers_created_for_user, 'part_number');
            }
        }

        // Contar total de registros (para paginação)
        if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
            $total_rows = $this->item_model->get_entries_by_pn_or_desc(
                $item_input,
                $tag,
                $order_by,
                true,
                null,
                null,
                $owner_user,
                $part_numbers_created_for_user,
                null, // sem limit para contar
                null,  // sem offset para contar
                true // count_only
            );
        } else {
            $total_rows = $this->item_model->get_entries_by_pn_or_desc(
                $item_input,
                $tag,
                $order_by,
                true,
                null,
                null,
                null,
                null,
                null, // sem limit para contar
                null,  // sem offset para contar
                true // count_only
            );
        }

        // Carregar itens com os filtros aplicados e paginação
        if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
            $itens = $this->item_model->get_entries_by_pn_or_desc(
                $item_input,
                $tag,
                $order_by,
                true,
                null,
                null,
                $owner_user,
                $part_numbers_created_for_user,
                $per_page,
                $offset
            );
        } else {
            $itens = $this->item_model->get_entries_by_pn_or_desc(
                $item_input,
                $tag,
                $order_by,
                true,
                null,
                null,
                null,
                null,
                $per_page,
                $offset
            );
        }

        // Carregar dados de SLA diretamente nos itens se habilitado
        if ($has_farol_sla) {
            $this->load_sla_data($itens);
            // Aplicar filtro de Farol SLA se necessário
            $itens = $this->apply_farol_sla_filter($itens);

            // Ajustar total_rows se houve filtro de farol SLA
            $farol_sla = $this->item_model->get_state('filter.farol_sla');
            if ($this->has_valid_farol_sla_filter($farol_sla)) {
                $total_rows = count($itens);
            }
        }


        return [
            'itens' => $itens ?: [],
            'total_rows' => $total_rows,
            'current_page' => $page,
            'per_page' => $per_page,
            'total_pages' => ceil($total_rows / $per_page)
        ];
    }

    /**
     * Método para limpar os filtros da sessão
     * @return void
     * @access public
     *
     */
    public function reset_filters()
    {
        try {
            // Limpar dados problemáticos do POST antes do reset
            $this->clean_post_data_before_reset();

            // Configurar item_model para armazenar estado na sessão
            $this->item_model->set_state_store_session(TRUE);

            // Limpar todos os estados/filtros da sessão
            $this->item_model->clear_states();

            // Definir filtros essenciais após reset
            $this->item_model->set_state('filter.id_empresa', sess_user_company());

            return response_json(array(
                'status' => 200,
                'msg' => 'Filtros limpos com sucesso!'
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 500,
                'msg' => 'Erro ao limpar filtros: ' . $e->getMessage()
            ), 500);
        }
    }

    /**
     * Limpa dados problemáticos do POST antes do reset
     */
    private function clean_post_data_before_reset()
    {
        // Lista de campos que podem causar problemas se ficarem como array vazio
        $problematic_fields = [
            'pacotes_eventos',
            'sistema_origem',
            'owner',
            'prioridade',
            'atribuido_para',
            'status_classificacao_fiscal',
            'pre_agrupamento',
            'novo_material',
            'estabelecimento',
            'importado',
            'farol_sla'
        ];

        // Limpar campos problemáticos
        foreach ($problematic_fields as $field) {
            if (isset($_POST[$field])) {
                unset($_POST[$field]);
            }
        }

        // Definir apenas o que é necessário para o reset
        $_POST['reset_filters'] = true;
    }

    public function index()
    {
        $data = array();

        $id_empresa = sess_user_company();
        $this->title = "Dados Técnicos";
        $this->load->model(array(
            'anexo_model',
            'usuario_model',
            'suframa_model',
            'empresa_model',
            'cad_item_model',
            'item_model',
            'empresa_pais_model',
            'empresa_prioridades_model'
        ));

        $empresa = $this->empresa_model->get_entry($id_empresa);
        $campos_adicionais = explode('|', $empresa->campos_adicionais);

        $data['empresa_pais'] = $this->empresa_pais_model->get_entries_by_empresa($empresa->id_empresa);

        $has_owner = in_array('owner', $campos_adicionais);
        $data['has_owner'] = $has_owner;

        $has_farol_sla = in_array('habilitar_farol_dados_tecnicos', $campos_adicionais);
        $data['has_farol_sla'] = $has_farol_sla;

        $data['part_number_direto'] = $this->input->get('part_number');

        // Verificar se é uma requisição de reset via GET
        if ($this->input->get('reset_filters')) {
            $this->reset_filters();
            // Redirecionar para limpar a URL
            redirect('atribuir_grupo');
        }

        $filtered = $this->item_model->get_state('filter.filtered');
        $data['filtered'] = $filtered;

        // Processar filtros ANTES de carregar a view para garantir estado correto
        // Isso é necessário para que os filtros sejam limpos da sessão antes da view ser renderizada
        if ($this->input->post('filtered')) {
            // Aplicar filtros apenas quando há submissão de formulário
            $this->apply_default_filters($this->input->post());
        } else {
            // Mesmo sem POST, precisamos garantir que o item_model está configurado para sessão
            $this->item_model->set_state_store_session(TRUE);
            $this->item_model->restore_state_from_session('filter.', 'post');
        }

        // Verificar se há filtros aplicados (incluindo via sessão)
        $has_filters = $this->item_model->get_state('filter.filtered');
        $has_pagination = $this->input->get('per_page');

        if ($has_filters || $has_pagination) {
            $this->load->library('pagination');

            $pagination_data = $this->load_filtered_items();
            $data['itens'] = $pagination_data['itens'];
            $data['total_rows'] = $pagination_data['total_rows'];

            // Debug: verificar dados
            log_message('debug', 'Pagination Data: ' . json_encode($pagination_data));
            $data['current_page'] = $pagination_data['current_page'];
            $data['per_page'] = $pagination_data['per_page'];
            $data['total_pages'] = $pagination_data['total_pages'];

            $config['base_url'] = base_url('atribuir_grupo');
            $config['total_rows'] = $data['total_rows'];
            $config['per_page'] = $data['per_page'];
            $config['page_query_string'] = true;
            $config['query_string_segment'] = 'per_page';
            // Aumentar o tamanho do link para melhor navegação
            $config['full_tag_open'] = '<ul class="pagination">';
            $config['full_tag_close'] = '</ul>';
            $config['num_links'] = 5;
            $config['use_page_numbers'] = true;

            $this->pagination->initialize($config);

            $data['pagination'] = $this->pagination->create_links();
        } else {
            // Quando não há filtros aplicados, não exibir nenhum resultado
            $data['itens'] = [];
            $data['total_rows'] = 0;
            $data['current_page'] = 1;
            $data['per_page'] = 50;
            $data['total_pages'] = 1;
            $data['pagination'] = '';
        }

        $userData = $this->usuario_model->get_entry(sess_user_id());

        $user = new stdClass();

        $user->id_empresa = $userData->id_empresa;
        $user->id_perfil = $userData->id_perfil;

        $data['user'] = $user;

        $ownersToTransfer = $this->empresa_model->get_owners_by_empresa($empresa);

        $data['entry'] = $empresa;
        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;
        $data['campos_adicionais'] = explode('|', $empresa->campos_adicionais);
        $data['funcoes_adicionais'] = explode('|', $empresa->funcoes_adicionais);
        $data['integracao_simplus'] = $empresa->integracao_simplus;
        $data['ownersToTransfer'] = $ownersToTransfer;

        $data['has_status_triagem_diana'] = in_array('status_triagem_diana', $data['funcoes_adicionais']) ?
            true :
            false;

        $data['empresa_pais'] = $this->empresa_pais_model->get_entries_by_empresa($empresa->id_empresa);

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Dados Técnicos', '/atribuir_grupo/');

        $this->usuario_model->set_state('filter.id_empresa', $id_empresa);
        $this->usuario_model->set_state('order_by', array('u.id_perfil' => 'desc', 'nome' => 'asc'));

        $data['suframa_produtos_empresa'] = $this->suframa_model->get_suframa_produtos_by_empresa();

        $data['has_diana'] = customer_can('has_diana', false, false);

        // Dados do usuário para lógica de bloqueio dos checkboxes
        $data['id_usuario_logado'] = sess_user_id();
        $data['usuario_desbloqueador'] = customer_has_role('desblquear_itens', sess_user_id()) ? 1 : 0;

        // Preparar dados de filtros
        $filter_data = $this->prepare_filter_data($campos_adicionais, $data['funcoes_adicionais']);
        $data['filtros_salvos'] = $filter_data['filtros_salvos'];
        $data['filtros'] = $filter_data['filtros'];
        $data['basic_filters'] = $filter_data['basic_filters'];
        $data['advanced_filters'] = $filter_data['advanced_filters'];
        $data['search'] = $this->item_model->get_state('filter.search');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-select/bootstrap-select.min.js',
            'bootstrap-notify.min.js',
            'jquery.cookie.js',
            'sweetalert.min.js',
            'jquery.mask.min.js',
            'b3-datetimepicker.min.js'
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'animate.css',
            'sweetalert.css',
            'b3-datetimepicker.min.css'
        ));

        $this->render('atribuir_grupo/index_new', $data);
    }

    public function anotacoes_inserir()
    {
        $this->load->model(array(
            'item_model'
        ));

        $editar_anotacao = $this->input->post("editar_anotacao");
        $anotacao = $this->input->post("anotacao");
        $part_number = $this->input->post("part_number");
        $estabelecimento = $this->input->post("estabelecimento");
        $anotacao = $this->input->post("anotacao");
        $id_empresa = sess_user_company();
        $id_user = sess_user_id();


        $data = [
            'consultor_id' => $id_user,
            'data' => date('Y-m-d H:i:s'),
            'anotacao' => $anotacao,
            'part_number' => $part_number,
            'estabelecimento' => $estabelecimento,
            'id_empresa' => $id_empresa
        ];

        if (!empty($editar_anotacao)) {
            $result = $this->item_model->anotacoes_delete($editar_anotacao);
        }

        $result = $this->item_model->anotacoes_insert($data);

        $result = $this->item_model->get_anotacoes($part_number, $id_empresa, $estabelecimento, $id_user, $anotacao);
        $data_anotacao = null;
        if ($result->data) {
            $date = strtotime($result->data);
            $data_anotacao = date('d/m/Y H:i:s', $date);
        }

        $userName = $this->usuario_model->get_entry($result->consultor_id);

        $data = [
            'status' => 'success',
            'id' => $result->id,
            'data' => $data_anotacao,
            'anotacao' => $result->anotacao,
            'consultor' => $userName->nome,
        ];
        if ($result) {
            $data['status'] = 'success';
            echo json_encode($data);
        } else {
            $data['status'] = 'error';
            echo json_encode($data);
        }
    }

    public function anotacoes_deletar()
    {
        $id = $this->input->post("id");
        $result =  $this->item_model->anotacoes_delete($id);

        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }

    public function ajax_get_prioridades()
    {

        $this->load->model(array(
            'empresa_model',
            'empresa_prioridades_model'
        ));

        //$id_empresa = $this->input->get('id_empresa');
        $id_empresa = sess_user_company();

        $entries = $this->empresa_prioridades_model->get_entry($id_empresa);
        if (empty($entries)) {
            echo json_encode(array(
                'error' => true,
                'msg' => 'Sem dados disponíveis!',
                'data' => array()
            ));
            return true;
        }
        echo json_encode($entries);
        return true;
    }

    public function xhr_get_responsaveis()
    {
        $this->load->model(array('usuario_model'));

        $tipo_responsavel = $this->input->post("tipo_responsavel");

        $this->usuario_model->set_state('filter.id_empresa', sess_user_company());
        $this->usuario_model->set_state('order_by', array('u.id_perfil' => 'desc', 'nome' => 'asc'));

        $lista_usuarios = $this->usuario_model->get_entries_by_role($tipo_responsavel, null, true, null, 'ativo');

        $id_perfil = null;
        $select_body = "";

        $lista_usuarios = array_filter($lista_usuarios, function ($usuario) {
            return !customer_has_role('sysadmin', $usuario->id_usuario) && !customer_has_role('becomex_pmo', $usuario->id_usuario);
        });

        foreach ($lista_usuarios as $k => $usuario) {
            if (
                customer_has_role('cliente_pmo', $usuario->id_usuario)
                && (has_role('fiscal') || has_role('engenheiro'))
            ) {
                $select_body .= "<optgroup label='Gerente de Projetos'>";
                $select_body .= "<option style='padding-left: 44px;' value='{$usuario->id_usuario}' data-subtext='{$usuario->email}'>{$usuario->nome}</option>";
                $select_body .= "</optgroup>";
            } else {
                if ($id_perfil == null || ($id_perfil !== $usuario->id_perfil)) {
                    $select_body .= $id_perfil !== null ? "</optgroup>" : '';
                    $select_body .= "<optgroup label='{$usuario->perfil_descricao}'>";

                    $id_perfil = $usuario->id_perfil;
                }

                $select_body .= "<option style='padding-left: 44px;' data-id-perfil='{$usuario->id_perfil}' value='{$usuario->id_usuario}' data-subtext='{$usuario->email}'>{$usuario->nome}</option>";

                $select_body .= (count($lista_usuarios) + 1) == $k ? "</optgroup>" : "";
            }
        }

        if (!empty($select_body)) {
            $select_body = '<option disabled><span class="label label-success" style="font-size: 12px; border-radius: 10px;">Usuários ativos</span></option>' . $select_body;
        }

        $data['select_body'] = $select_body;

        return response_json($data);
    }

    public function deletar()
    {
        if (!customer_has_role('desvincular_grupo', sess_user_id())) {
            show_permission();
        }

        $data = array();

        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Desvincular grupo', '/atribuir_grupo/deletar');

        $this->include_js('jquery.expander.min.js');

        $this->render('atribuir_grupo/deletar', $data);
    }

    public function ajax_get_tags($filter, $id_usuario = NULL)
    {
        $data = array();

        if ($filter == 'all') {
            $this->item_model->set_state('filter.show_all', 1);
        } else {
            $this->item_model->set_state('filter.show_all', 0);
        }

        if (!empty($id_usuario)) {
            $tags = $this->item_model->get_itens_tags(sess_user_company(), $id_usuario);
        } else {
            $tags = $this->item_model->get_itens_tags(sess_user_company());
        }

        foreach ($tags as $tag) {
            $data[$tag->tag] = $tag;
        }

        echo json_encode($data);
        return TRUE;
    }

    /**
     * Método específico para o modal de filtros - retorna tags baseado no usuário selecionado
     */
    public function ajax_get_tags_modal()
    {
        try {
            $usuario = $this->input->post('usuario');
            $filter_options = $this->input->post('filter_options') === 'true';

            $data = [];

            if ($filter_options) {
                $this->item_model->set_state('filter.show_all', 0);
            } else {
                $this->item_model->set_state('filter.show_all', 1);
            }

            if (!empty($usuario) && $usuario !== '-1') {
                $tags = $this->item_model->get_itens_tags(sess_user_company(), $usuario);
            } else {
                $tags = $this->item_model->get_itens_tags(sess_user_company());
            }

            foreach ($tags as $tag) {
                $data[$tag->tag] = $tag;
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar tags: ' . $e->getMessage()
                ]));
        }
    }

    public function ajax_get_lessin()
    {
        $data = array();

        $this->load->library("Item/LessinUseCases");

        $data['items'] = $this->lessinusecases->get_items();

        return response_json(array(
            'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_lessin', array(
                'items' => $data['items']
            ), true),
        ));
    }

    public function log_xls()
    {
        error_reporting(0);
        set_time_limit(0);
        ini_set('memory_limit', '2048M');
        if ($get = $this->input->get()) {

            if ($this->input->is_set('evento')) {
                $this->item_model->set_state('filter.evento', $get['evento']);
            } else {
                $this->item_model->unset_state('filter.evento');
            }

            $prioridade = is_array($get['prioridade']) ? $get['prioridade'] : array();

            if (!empty($prioridade) && !in_array("none", $prioridade)) {
                $this->item_model->set_state('filter.prioridade', $prioridade);
            } else {
                $this->item_model->unset_state('filter.prioridade');
            }

            if ($get['status']) {
                $this->item_model->set_state('filter.status', $get['status']);
            } else {
                $this->item_model->unset_state('filter.status');
            }

            if ($get['owner']) {
                $this->item_model->set_state('filter.owner', $get['owner']);
            } else {
                $this->item_model->unset_state('filter.owner');
            }

            $this->item_model->set_state('filter.tag', $get['tag']);

            if (isset($get['atribuido_para']) && $get['atribuido_para'] !== '-1') {
                $this->item_model->set_state('filter.id_usuario', $get['atribuido_para']);
            }

            if ($this->input->is_set('sistemas_origens')) {
                $this->item_model->set_state('filter.sistema_origem', $this->input->post('sistemas_origens'));
            } else {
                $this->item_model->unset_state('filter.sistema_origem');
            }

            $id_empresa = sess_user_company();
            $this->load->model('empresa_model');
            $empresa = $this->empresa_model->get_entry($id_empresa);

            $campos_adicionais = explode('|', $empresa->campos_adicionais);
            $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais);

            $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $campos_adicionais);

            $logs = $this->item_model->get_entries_for_xls($id_empresa);

            $this->load->library('Excel');

            $this->excel->setActiveSheetIndex(0);
            $this->excel->getActiveSheet()->setTitle('Log - Atribuição de grupos');

            $this->excel->getActiveSheet()->setCellValue('A1', 'Part number');
            $this->excel->getActiveSheet()->setCellValue('B1', 'Descrição do item');
            $this->excel->getActiveSheet()->setCellValue('C1', 'Data de Criação');
            $this->excel->getActiveSheet()->setCellValue('D1', 'Data de Modificação');
            $this->excel->getActiveSheet()->setCellValue('E1', 'Motivo');
            $this->excel->getActiveSheet()->setCellValue('F1', 'Estabelecimento');
            $this->excel->getActiveSheet()->setCellValue('G1', 'Part number similar');
            $this->excel->getActiveSheet()->setCellValue('H1', 'NCM');
            $this->excel->getActiveSheet()->setCellValue('I1', 'NCM Fornecedor');
            $this->excel->getActiveSheet()->setCellValue('J1', 'Evento');
            $this->excel->getActiveSheet()->setCellValue('K1', 'TAG');
            $this->excel->getActiveSheet()->setCellValue('L1', 'Status');
            $this->excel->getActiveSheet()->setCellValue('M1', 'Descrição completa');
            $this->excel->getActiveSheet()->setCellValue('N1', 'Função');
            $this->excel->getActiveSheet()->setCellValue('O1', 'Aplicação');
            $this->excel->getActiveSheet()->setCellValue('P1', 'Marca');
            $this->excel->getActiveSheet()->setCellValue('Q1', 'Material constitutivo');
            $this->excel->getActiveSheet()->setCellValue('R1', 'Memória de classificação');
            $this->excel->getActiveSheet()->setCellValue('S1', 'Informações adicionais');
            $this->excel->getActiveSheet()->setCellValue('T1', 'Observações');
            $this->excel->getActiveSheet()->setCellValue('U1', 'Peso');
            $this->excel->getActiveSheet()->setCellValue('V1', 'Prioridade');
            $this->excel->getActiveSheet()->setCellValue('W1', 'Perguntas & Respostas');
            $this->excel->getActiveSheet()->setCellValue('X1', 'Status do item');
            $this->excel->getActiveSheet()->setCellValue('Y1', 'Owner');
            $this->excel->getActiveSheet()->setCellValue('Z1', 'Novo Material');
            $this->excel->getActiveSheet()->setCellValue('AA1', 'Responsável Fiscal');
            $this->excel->getActiveSheet()->setCellValue('AB1', 'Responsável Engenharia');
            $this->excel->getActiveSheet()->setCellValue('AC1', 'Responsável Perguntas');

            if ($hasDescricaoGlobal) {
                $this->excel->getActiveSheet()->setCellValue('AD1', 'Descrição global');
                $this->excel->getActiveSheet()->setCellValue('AE1', 'Indicador (COMEX)');
                $this->excel->getActiveSheet()->setCellValue('AF1', 'DI (COMEX)');
                $this->excel->getActiveSheet()->setCellValue('AG1', 'Data da Última DI (COMEX)');
                $this->excel->getActiveSheet()->setCellValue('AH1', 'Drawback (COMEX)');
                $this->excel->getActiveSheet()->setCellValue('AI1', 'NCM (COMEX)');
                if ($hasPnPrimarioSecundario) {
                    $this->excel->getActiveSheet()->setCellValue('AJ1', 'PN Secundário');
                }
            } else {
                $this->excel->getActiveSheet()->setCellValue('AD1', 'Indicador (COMEX)');
                $this->excel->getActiveSheet()->setCellValue('AE1', 'DI (COMEX)');
                $this->excel->getActiveSheet()->setCellValue('AF1', 'Data da Última DI (COMEX)');
                $this->excel->getActiveSheet()->setCellValue('AG1', 'Drawback (COMEX)');
                $this->excel->getActiveSheet()->setCellValue('AH1', 'NCM (COMEX)');
                if ($hasPnPrimarioSecundario) {
                    $this->excel->getActiveSheet()->setCellValue('AI1', 'PN Secundário');
                }
            }

            if ($hasDescricaoGlobal) {
                if ($hasPnPrimarioSecundario) {
                    $this->excel->getActiveSheet()->getStyle('A1:AJ1')->getFont()->setBold(true);
                    $this->excel->getActiveSheet()->getStyle('A1:AJ1')->getFill()
                        ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                        ->getStartColor()->setARGB('F9FF00');
                } else {
                    $this->excel->getActiveSheet()->getStyle('A1:AI1')->getFont()->setBold(true);
                    $this->excel->getActiveSheet()->getStyle('A1:AI1')->getFill()
                        ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                        ->getStartColor()->setARGB('F9FF00');
                }
            } else {
                if ($hasPnPrimarioSecundario) {
                    $this->excel->getActiveSheet()->getStyle('A1:AI1')->getFont()->setBold(true);
                    $this->excel->getActiveSheet()->getStyle('A1:AI1')->getFill()
                        ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                        ->getStartColor()->setARGB('F9FF00');
                } else {
                    $this->excel->getActiveSheet()->getStyle('A1:AH1')->getFont()->setBold(true);
                    $this->excel->getActiveSheet()->getStyle('A1:AH1')->getFill()
                        ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                        ->getStartColor()->setARGB('F9FF00');
                }
            }

            $this->excel->getActiveSheet()->getStyle('A1:AJ1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

            foreach (range('A', 'AJ') as $columnID) {
                $this->excel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
                $this->excel->getActiveSheet()
                    ->getStyle($columnID)
                    ->getAlignment()
                    ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
            }

            if (!empty($logs)) {
                $horizontal_left = array(
                    'alignment' => array(
                        'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                    ),
                );

                $i = count($logs) + 1;
                $this->excel->getActiveSheet()->getStyle('A1:A' . $i)->applyFromArray($horizontal_left);
                $key = 0;

                while ($log = $logs->unbuffered_row()) {

                    $perguntasRespostas = "";
                    $responsavelPergunta = "";
                    $id_responsavel_pergunta = [];
                    $key++;
                    $this->ctr_resposta_model->set_state('filter.partnumber', $log->part_number);
                    $this->ctr_resposta_model->set_state('filter.estabelecimento', $log->estabelecimento);

                    $historicoItem = $this->ctr_resposta_model->getHistoricoItem();

                    if (!empty($historicoItem)) {
                        foreach ($historicoItem as $k => $historico) {
                            $perguntasRespostas .= ($k + 1) . " - {$historico->pergunta}\n";
                            $perguntasRespostas .= "R: " . (!empty($historico->resposta) ? "$historico->resposta \n" : " \n");

                            if (!empty($historico->id_responsavel_pergunta)) {
                                if (!in_array($historico->id_responsavel_pergunta, $id_responsavel_pergunta)) {
                                    $id_responsavel_pergunta[] = $historico->id_responsavel_pergunta;
                                    $responsavelPergunta .= $historico->nome_responsavel_pergunta . " - " . $historico->email_responsavel_pergunta . "; \n";
                                }
                            }
                        }
                    }

                    $log->descricao = html_entity_decode($log->descricao, ENT_QUOTES);

                    $log->motivo = str_replace('<br>', "\n", $log->motivo);
                    $log->motivo = strip_tags($log->motivo);
                    $log->motivo = html_entity_decode($log->motivo, ENT_QUOTES);

                    $estab = !empty($log->estabelecimento) ? $log->estabelecimento : 'N/A';

                    if ($log->data_di) {
                        $date = strtotime($log->data_di);
                        $data_di = date('d/m/Y', $date);
                    }

                    $dat_criacao = "";
                    if (!empty($log->dat_criacao)) {
                        $dat_criacao = date("d/m/Y", strtotime($log->dat_criacao));
                    }

                    $data_modificacao = "";
                    if (!empty($log->data_modificacao)) {
                        $data_modificacao = date("d/m/Y", strtotime($log->data_modificacao));
                    }

                    $i = $key + 1;

                    $nomeResponsavelFiscal = $log->resp_fiscal_nome ? $log->resp_fiscal_nome : 'Usuário Não Encontrado';
                    $emailResponsavelFiscal = $log->resp_fiscal_email ? $log->resp_fiscal_email : '';

                    $nomeResponsavelEngenharia = $log->resp_engenharia_nome ? $log->resp_engenharia_nome : 'Usuário Não Encontrado';
                    $emailResponsavelEngenharia = $log->resp_engenharia_email ? $log->resp_engenharia_email : '';

                    $status_map = [
                        '1' => 'Pendente de Homologação',
                        '2' => 'Homologado',
                        '3' => 'Reprovado',
                        '4' => 'Inativo',
                        '5' => 'Em Revisão',
                        '6' => 'Em Análise',
                        '7' => 'Pendente de Informações',
                        '8' => 'Perguntas Respondidas',
                        '9' => 'Revisar Informações ERP',
                        '10' => 'Homologado em Revisão',
                        '11' => 'Revisar Informações Técnicas',
                        '12' => 'Informações ERP Revisadas',
                        '13' => 'Aguardando definição responsável',
                        '14' => 'Aguardando Descrição',
                        '15' => 'Perguntas Respondidas (Novas)'
                    ];
                    $status_fiscal = isset($status_map[$log->id_status]) ? $status_map[$log->id_status] : '';


                    $this->excel->getActiveSheet()->setCellValueExplicit('A' . $i, $log->part_number, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('B' . $i, $log->descricao, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('C' . $i, $dat_criacao, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('D' . $i, $data_modificacao, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('E' . $i, $log->motivo, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('F' . $i, $log->estabelecimento, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('G' . $i, $log->part_number_similar, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('H' . $i, $log->ncm, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('I' . $i, $log->ncm_fornecedor, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('J' . $i, $log->evento, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('K' . $i, $log->tag, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('L' . $i, $log->status, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('M' . $i, $log->descricao_proposta_completa, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('N' . $i, $log->funcao, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('O' . $i, $log->aplicacao, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('P' . $i, $log->marca, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('Q' . $i, $log->material_constitutivo, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('R' . $i, $log->memoria_classificacao, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('S' . $i, $log->inf_adicionais, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('T' . $i, $log->observacoes, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('U' . $i, $log->peso, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('V' . $i, $log->empresa_prioridade, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('W' . $i, $perguntasRespostas, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('X' . $i, $status_fiscal, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('Y' . $i, $log->owner_codigo . ' - ' . $log->owner_descricao . ' - ' . $log->responsaveis_gestores_nomes, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('Z' . $i, isset($log->integracao_novo_material) ? (($log->integracao_novo_material == 'S') ? 'Sim' : 'Não') : null, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('AA' . $i, $nomeResponsavelFiscal . " - " . $emailResponsavelFiscal, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('AB' . $i, $nomeResponsavelEngenharia . " - " . $emailResponsavelEngenharia, PHPExcel_Cell_DataType::TYPE_STRING);
                    $this->excel->getActiveSheet()->setCellValueExplicit('AC' . $i, $responsavelPergunta, PHPExcel_Cell_DataType::TYPE_STRING);

                    if ($hasDescricaoGlobal) {
                        $this->excel->getActiveSheet()->setCellValueExplicit('AD' . $i, $log->descricao_global, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AE' . $i, $log->indicador_ecomex, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AF' . $i, $log->num_di, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AG' . $i, isset($log->data_di) ? ($data_di) : null, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AH' . $i, isset($log->ind_drawback) ? (($log->ind_drawback == 1 || $log->ind_drawback == 'S') ? 'S' : 'N') : null, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AI' . $i, $log->ncm_ecomex, PHPExcel_Cell_DataType::TYPE_STRING);

                        if ($hasPnPrimarioSecundario) {
                            $this->excel->getActiveSheet()->setCellValueExplicit('AJ' . $i, $log->pn_secundario_ipn, PHPExcel_Cell_DataType::TYPE_STRING);
                        }
                    } else {
                        $this->excel->getActiveSheet()->setCellValueExplicit('AD' . $i, $log->indicador_ecomex, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AE' . $i, $log->num_di, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AF' . $i, isset($log->data_di) ? ($data_di) : null, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AG' . $i, isset($log->ind_drawback) ? (($log->ind_drawback == 1 || $log->ind_drawback == 'S') ? 'S' : 'N') : null, PHPExcel_Cell_DataType::TYPE_STRING);
                        $this->excel->getActiveSheet()->setCellValueExplicit('AH' . $i, $log->ncm_ecomex, PHPExcel_Cell_DataType::TYPE_STRING);
                        if ($hasPnPrimarioSecundario) {
                            $this->excel->getActiveSheet()->setCellValueExplicit('AI' . $i, $log->pn_secundario_ipn, PHPExcel_Cell_DataType::TYPE_STRING);
                        }
                    }

                    // $this->excel->getActiveSheet()->getStyle('C' . $i)->getAlignment()->setWrapText(true);
                    $this->excel->getActiveSheet()->getStyle('W' . $i)->getAlignment()->setWrapText(true);

                    if ($hasDescricaoGlobal) {
                        $this->excel->getActiveSheet()->getStyle('Z' . $i)->getAlignment()->setWrapText(true);
                    }
                }
            }

            $filename = 'log_' . date('Y-m-d_H-i-s') . '.xlsx';

            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
            $objWriter->save('php://output');
        }
    }

    public function ajax_xls_log_status()
    {
        // Esse método é obrigatório para o
        // download do XLS + prevent doubleclick.
        //
        // Não remover o método!
    }

    public function xls_multipaises()
    {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');
        if ($get = $this->input->get()) {

            if ($this->input->is_set('evento')) {
                $this->item_model->set_state('filter.evento', $get['evento']);
            } else {
                $this->item_model->unset_state('filter.evento');
            }

            if (isset($get['prioridade'])) {
                $prioridade = is_array($get['prioridade']) ? $get['prioridade'] : array();

                if (!empty($prioridade) && !in_array("none", $prioridade)) {
                    $this->item_model->set_state('filter.prioridade', $prioridade);
                } else {
                    $this->item_model->unset_state('filter.prioridade');
                }
            }

            if (isset($get['status'])) {
                $this->item_model->set_state('filter.status', $get['status']);
            } else {
                $this->item_model->unset_state('filter.status');
            }

            if (isset($get['owner'])) {
                $this->item_model->set_state('filter.owner', $get['owner']);
            } else {
                $this->item_model->unset_state('filter.owner');
            }

            $this->item_model->set_state('filter.tag', $get['tag']);

            if (isset($get['atribuido_para']) && $get['atribuido_para'] !== '-1') {
                $this->item_model->set_state('filter.id_usuario', $get['atribuido_para']);
            }

            $id_empresa = sess_user_company();

            $this->load->model(
                array(
                    'empresa_model',
                    'item_pais_model',

                )
            );

            $itens = $this->item_model->get_entries_for_xls($id_empresa);

            $logs = $this->item_pais_model->get_entries_for_xls($id_empresa);

            $headerRow = array(
                array(
                    'label' => 'PART NUMBER BRASIL',
                    'field' => 'part_number',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'ESTABELECIMENTO',
                    'field' => 'estabelecimento',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'DESCRIÇÃO',
                    'field' => 'descricao',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'SIGLA DO PAÍS',
                    'field' => 'sigla',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'CÓDIGO DO PAÍS',
                    'field' => 'codigo',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'NOME DO PAÍS',
                    'field' => 'nome',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'CÓDIGO CLASSIFICAÇÃO',
                    'field' => 'codigo_classificacao',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'DESCRIÇÃO CURTA',
                    'field' => 'descricao_curta',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'DESCRIÇÃO COMPLETA',
                    'field' => 'descricao_completa',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'L.I.',
                    'field' => 'li',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'INFORMAÇÃO ADICIONAL',
                    'field' => 'informacao_adicional',
                    'col_format' => 'texto',
                    'col_width' => 30
                )

            );



            $cabecalho = $this->extract_fields_from_cols($headerRow);

            $config = array(
                'filename'      => "paises_dados_tecnicos_" . date('Y-m-d_H-i-s'),
                'titulo'        => "Paises Dados Técnicos",
                'nome_planilha' => "Paises Dados Técnicos",
                'colunas'       => $headerRow,
                'cabecalho'     => $cabecalho,
                'filter_suffix' => "HOM_"
            );

            require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';
            $relatorio = new XlsxGenerator($config['filename']);
            $writer = $relatorio->init($config['filename']);

            $relatorio->filename = $config['filename'];

            // Estrutura XLSX
            $widths = $relatorio->getWidth($config);
            $fields = array();

            $writer->addImage(FCPATH . 'assets/img/header/logo.png', 1, array('widths' => $widths));

            if (isset($config['colunas'])) {
                foreach ($config['colunas'] as $coluna) {
                    if (isset($coluna['label']))
                        $fields[$coluna['label']] = isset($coluna['col_format']) ? $coluna['col_format'] : 'string';
                }
            }

            $headerStyle = array(
                'font' => 'Arial',
                'font-size' => 12,
                'font-style' => 'bold',
                'color' => '#000000',
                'fill' => '#F9FF00',
                'halign' => 'center',
                'valign' => 'center',
                'border' => 'bottom',
                'wrap_text' => 'true',
                'widths' => $widths
            );


            $writer->writeSheetHeader($config['nome_planilha'], $fields, $headerStyle);

            if (isset($config['colunas']) &&  (count($config['colunas']) <= 24) && (count($config['colunas']) >= 10)) {
                $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
            } else if (isset($config['colunas']) && count($config['colunas']) <= 24) {
                $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, 24);
            } else {
                $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
            }

            $defaultStyle = array(
                'font' => 'Arial',
                'font-size' => 11
            );

            $itemRows = array();

            $logsbuff = $logs->result();

            $k = 0;
            $key = 0;

            while ($item = $itens->unbuffered_row()) {

                $tem_paises = 0;
                foreach ($logsbuff as $log) {

                    if ($item->part_number == $log->part_number) {

                        $key++;
                        $k = $key + 1;

                        $itemRows[$k] = array(
                            $log->part_number,
                            $log->estabelecimento,
                            $item->descricao,
                            $log->sigla,
                            $log->codigo,
                            $log->nome,
                            $log->codigo_classificacao,
                            $log->descricao_curta,
                            $log->descricao_completa,
                            $log->li,
                            $log->informacao_adicional
                        );
                        $tem_paises++;
                    }
                }
                if ($tem_paises == 0) {
                    $key++;
                    $k = $key + 1;
                    $itemRows[$k] = array(
                        $item->part_number,
                        $item->estabelecimento,
                        $item->descricao,
                        ' - ',
                        ' - ',
                        ' - ',
                        ' - ',
                        ' - ',
                        ' - ',
                        ' - ',
                        ' - '
                    );
                }
            }


            $k = 2;
            foreach ($itemRows as $item) {
                $row = array();
                foreach ($headerRow as $i => $value) {
                    $row += array($headerRow[$i]['field'] => $itemRows[$k][$i]);
                }
                $k++;
                $writer->writeSheetRow($config['nome_planilha'], $row, $defaultStyle);
            }

            return $relatorio->download();
        }
    }

    private function extract_fields_from_cols($cols, $line_break = ' ', $label_key_override = 'label_exportar')
    {
        if (empty($cols)) {
            return array();
        }

        $fields = array();

        foreach ($cols as $col) {
            $label_key = 'label';

            if (!empty($label_key_override) && array_key_exists($label_key_override, $col)) {
                $label_key = $label_key_override;
            }

            if (!empty($line_break)) {
                $col[$label_key] = str_replace('<br>', $line_break, $col[$label_key]);
            }

            $fields[$col['field']] = $col[$label_key];
        }

        return $fields;
    }

    public function ajax_xls_log_status_multipaises()
    {
        // Esse método é obrigatório para o
        // download do XLS + prevent doubleclick.
        //
        // Não remover o método!
    }

    public function ajax_get_itens()
    {
        if ($post = $this->input->post()) {
            $data = array();

            $id_empresa = sess_user_company();
            $this->load->model('empresa_model');
            $empresa = $this->empresa_model->get_entry($id_empresa);

            $campos_adicionais = explode('|', $empresa->campos_adicionais);
            $hasOwner = in_array('owner', $campos_adicionais);

            // Garantir que campos obrigatórios existam
            $post['order'] = $post['order'] ?? ['order' => 'part_number', 'by' => 'asc'];
            $post['item_input'] = $post['item_input'] ?? '';
            $post['tag'] = $post['tag'] ?? '';

            // Aplicar filtros usando o novo sistema
            // Verificar se não foi já processado no index() para evitar duplo processamento
            if (!$this->input->post('already_processed')) {
                $this->apply_default_filters($post);
            }

            $order_by = $post['order']['order'] . ' ' . $post['order']['by'];

            // Garantir que order_by não seja vazio
            if (empty(trim($order_by))) {
                $order_by = 'part_number asc';
            }

            // Verificar permissões e carregar dados de owner se necessário
            $owner_user = null;
            $part_numbers_created_for_user = array();

            if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
                $owner_user = $this->item_model->get_user_owner_codes(sess_user_id());
                $part_numbers_created_for_user = $this->item_model->get_items_created_user(sess_user_id());

                if (!empty($part_numbers_created_for_user)) {
                    $part_numbers_created_for_user = array_column($part_numbers_created_for_user, 'part_number');
                }
            }

            // Carregar itens com os filtros aplicados
            if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
                $data['itens'] = $this->item_model->get_entries_by_pn_or_desc(
                    $post['item_input'],
                    $post['tag'],
                    $order_by,
                    TRUE,
                    NULL,
                    NULL,
                    $owner_user,
                    $part_numbers_created_for_user
                );
            } else {
                $data['itens'] = $this->item_model->get_entries_by_pn_or_desc(
                    $post['item_input'],
                    $post['tag'],
                    $order_by,
                    TRUE,
                    NULL,
                    NULL
                );
            }

            // Carregar dados de SLA
            $data['sla_data'] = $this->load_sla_data($data['itens']);


            return response_json($data);
        }
    }

    public function ajax_get_item()
    {
        if ($post = $this->input->post()) {

            $data = array();

            $data['item'] = $this->item_model->get_entry($post['part_number'], sess_user_company(), $post['estabelecimento']);

            return response_json($data);
        }
    }

    public function ajax_desbloqueia_item()
    {
        if ($post = $this->input->post()) {

            $data = array();
            $itens = $post['itens_checked'];


            foreach ($itens as $item) {

                $data += $this->item_model->set_desbloqueia_item($item['part_number'], sess_user_company(), $item['estabelecimento']);
            }

            echo json_encode($data);
            return TRUE;
        }
    }

    public function get_part_number()
    {
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $funcoes_adicionais = explode("|", $empresa->funcoes_adicionais);
        if (in_array("integracao_ecomex", $funcoes_adicionais)) {
            $this->load->model('comex_model');
            $part_number = $this->input->get('part_number');
            $estabelecimento = $this->input->get('estabelecimento');
            $id_empresa = sess_user_company();
            $ecomex = $this->comex_model->get_entry($part_number, $id_empresa, $estabelecimento);
            echo json_encode($ecomex);
            return TRUE;
        } else {
            echo 'sem_ecomex';
            return TRUE;
        }
    }

    public function ajax_update_items()
    {
        $data = array(
            'status' => 200,
            'message' => "Itens atualizados!"
        );

        $this->load->model('empresa_prioridades_model');

        try {
            $post = json_decode(file_get_contents('php://input'), true);

            $dbdata = array();
            $motivo = '';
            $id_empresa = sess_user_company();
            $prioridade = $post['prioridade'];
            $old_prioridades = array();

            foreach ($post['items'] as $item) {
                if (!empty($item['partnumber'])) {
                    $estabelecimento = !empty($item['estabelecimento']) ? $item['estabelecimento'] : null;
                    $item_db = $this->item_model->get_entry($item['partnumber'], $id_empresa, $estabelecimento);
                    $prioridade_atual_item = $this->empresa_prioridades_model->get_old_prioridade($item['partnumber'], $id_empresa, $estabelecimento);
                    $old_prioridades[$item['partnumber']] = $prioridade_atual_item[0]->prioridade_atual;
                }
            }


            if (!empty($post['peso']) || $post['peso'] === "0") {
                $dbdata['peso'] = $post['peso'];

                if (isset($item_db) && $item_db->peso != $dbdata['peso'] && trim($dbdata['peso']) != '0,000') {
                    $motivo = '<strong>Peso:</strong> ' . "<em>{$item_db->peso}</em> &rarr;" . $dbdata['peso'] . '<br>';
                }
            }

            foreach ($post['items'] as $item) {
                if (!empty($item['partnumber'])) {
                    $motivo_prioridade = '';
                    if (!empty($prioridade['key'])) {
                        $dbdata['id_prioridade'] = $prioridade['key'];
                        if ($old_prioridades[$item['partnumber']] != $prioridade['label']) {
                            $motivo_prioridade = "<strong>Prioridade:</strong> <em>{$old_prioridades[$item['partnumber']]}</em> &rarr; {$prioridade['label']}" . '<br>';
                        }
                    }

                    if (trim($motivo) != "" || trim($motivo_prioridade) != "") {
                        $response = $this->item_model->update_item($item['partnumber'], sess_user_company(), $dbdata, $motivo . $motivo_prioridade, $item['estabelecimento']);
                    } else {
                        $data = array(
                            'status' => 422,
                            'message' => "Dados iguais aos cadastrados."
                        );
                        $response = true;
                    }
                    if (!$response) {
                        throw new Exception("Não foi possível atualizar o item.");
                    }
                }
            }
        } catch (Exception $e) {
            $data = array(
                'status' => 422,
                'message' => $e->getMessage()
            );
        }

        response_json($data);
    }

    // FUNÇÃO ESPECIFICA PARA A SIMPLUS, retorna os campos extras.
    public function ajax_get_item_by_pn_company()
    {
        if ($post = $this->input->post()) {
            $this->load->helper('formatador_helper');

            $data = array();
            $pn = $post['part_number'];
            $user_company = sess_user_company();
            $estabelecimento = $post['estabelecimento'];

            $item = $this->item_model->get_entry_by_pn_company($pn, $user_company, $estabelecimento);

            $item->peso = format_peso($item->peso);

            if (!empty($item->midias)) {
                $url_images = explode(',', $item->midias);
                if (is_array($url_images)) {
                    $item->midias = $url_images;
                } else {
                    $item->midias = array($url_images);
                }
            }

            $data['item'] = $item;

            echo $this->load->view('atribuir_grupo/modal-campos-extra-simplus', $data, TRUE);
        }
    }

    public function ajax_get_item_by_pn()
    {
        $id_user = sess_user_id();

        if ($post = $this->input->post()) {
            $this->load->model('usuario_model');

            $this->load->model([
                'anexo_model',
                'ctr_pendencias_pergunta_model',
                'empresa_model',
                'item_model'
            ]);
            $this->load->helper('formatador_helper');

            $data = array();
            $pn = $post['part_number'];
            $user_company = sess_user_company();
            $estabelecimento = $post['estabelecimento'];

            $item = $this->item_model->get_entry($pn, $user_company, $estabelecimento);

            $usuarios_seguidores = $this->item_model->get_usuarios_seguidores($pn, $user_company, $estabelecimento);
            $data['is_admin'] = has_role('sysadmin');
            $data['user_logado'] = $id_user;
            // $item->seguidores = $usuarios_seguidores;
            $data['usuarios_seguidores'] = $usuarios_seguidores;

            $owner_atual = $this->item_model->get_data_owner($item->cod_owner);
            $data['owner_atual'] = $owner_atual;

            $item->peso = format_peso($item->peso);

            $empresa = $this->empresa_model->get_entry(sess_user_company());

            $data['campos_adicionais'] = explode('|', $empresa->campos_adicionais);

            $data['item'] = $item;
            $data['owner_item'] = $this->item_model->getOwnerByItem($item->cod_owner);

            $data['can_ncm_fornecedor'] = customer_can('ncm_fornecedor');

            $data['partnumber'] = $post['part_number'];
            $data['estabelecimento'] = $post['estabelecimento'];

            $anotacoes = $this->item_model->get_anotacoes($pn, $user_company, $estabelecimento, $id_user);
            $data['anotacoes'] = $anotacoes;

            $this->ctr_pendencias_pergunta_model->set_state("filter.partnumbers", $post['part_number']);
            $this->ctr_pendencias_pergunta_model->set_state('filter.pendente', 1);

            $partnumberComPerguntasPendentes = $this->ctr_pendencias_pergunta_model->getPartnumbersComPerguntasPendentes();

            $data['partnumberComPerguntasPendentes'] = $partnumberComPerguntasPendentes;
            $data['perguntasPendentes'] = count($partnumberComPerguntasPendentes) ? true : false;
            $data['permissionDelete'] = customer_has_role('excluir_perguntas_respostas', $id_user) ? 1 : 0;

            echo $this->load->view('atribuir_grupo/modal-campos-extra', $data, TRUE);
        }
    }

    public function ajax_get_cad_itens()
    {
        $this->load->model('cad_item_model');

        if ($post = $this->input->post()) {
            $data = array();

            $id_empresa = sess_user_company();

            $order_by = $post['order']['order'] . ' ' . $post['order']['by'];

            if ($this->input->is_set('grupo')) {
                $grupo = $this->input->post('grupo');
            } else {
                $grupo = NULL;
            }

            $this->cad_item_model->set_state('filter.has_descricao_mercado_local', TRUE);

            $arr_filters = $this->set_filter_part_number($post['item_input']);

            $this->cad_item_model->set_state('filter.part_numbers', $arr_filters['part_numbers']);
            $this->cad_item_model->set_state('filter.generic_part_numbers', $arr_filters['generic_part_numbers']);
            $this->cad_item_model->set_state('filter.homologados', isset($post['homologados']) && $post['homologados'] ? TRUE : FALSE);

            $data['itens'] = $this->cad_item_model->get_entries_by_pn_or_desc($post['item_input'], $id_empresa, $grupo, $order_by);

            echo json_encode($data);
            return TRUE;
        }
    }

    public function ajax_get_grupos_tarifarios()
    {
        $this->load->model('grupo_tarifario_model');

        if ($post = $this->input->post()) {
            $data = array();
            $order_by = array();

            if ($this->input->is_set('group_by')) {
                $group_by = $post['group_by'];
                $order_by[] = array('order' => 'caracteristica', 'by' => 'asc');
            } else {
                $group_by = NULL;
            }

            if ($this->input->is_set('caracteristica')) {
                if ($this->input->post('caracteristica') == 'Sem característica') {
                    $caracteristica = FALSE;
                } else {
                    $caracteristica = $this->input->post('caracteristica');
                }

                $order_by[] = array('order' => $post['order']['order'], 'by' => $post['order']['by']);

                if ($post['order']['order'] == 'ncm_recomendada') {
                    $order_by[] = array('order' => 'descricao', 'by' => 'ASC');
                }
            } else {
                $caracteristica = NULL;
            }

            $tag = isset($post['tag']) ? $post['tag'] : '';
            $grupo = isset($post['grupo_input']) ? $post['grupo_input'] : '';

            $this->grupo_tarifario_model->set_state('filter.hide_groups', TRUE);
            $this->grupo_tarifario_model->set_state('filter.ativo', 1);
            $data['grupos'] = $this->grupo_tarifario_model->get_entries_by_desc($grupo, $tag, $order_by, $group_by, $caracteristica);

            return response_json($data);
        }
    }

    public function ajax_get_hidden_groups()
    {
        $this->load->model('grupo_tarifario_model');
        if ($post = $this->input->post()) {
            $data = array();

            $order_by = 'descricao ' . $post['order']['by'];
            $tag = isset($post['tag']) ? $post['tag'] : '';
            $grupo = isset($post['grupo_input']) ? $post['grupo_input'] : '';
            $data['hidden_grupos'] = $this->grupo_tarifario_model->get_hidden_grupos(sess_user_company(), $tag, $order_by, $grupo, sess_user_id());

            echo json_encode($data);
            return TRUE;
        }
    }

    public function ajax_get_ncm_info()
    {
        $post = $this->input->post();

        if (empty($post)) {
            $post = json_decode(file_get_contents('php://input'), true);
        }

        if (isset($post['ncm']) && !empty($post['ncm'])) {
            $ncm_entries = $this->ncm_model->get_entries_levels($post['ncm']);

            $data['ncm_entries'] = $ncm_entries;
            $data['ncm'] = $post['ncm'];

            $this->load->view('homologacao/ncm_table', $data);
        }
    }

    public function ajax_hide_group()
    {
        if ($post = $this->input->post()) {
            if ($this->input->is_set('id_grupo_tarifario')) {
                $id_grupo_tarifario = $post['id_grupo_tarifario'];
                $pre_agrupamento = $post['tag'];

                $this->load->model('oculto_atribuir_grupo_model');
                $dbdata = array(
                    'id_empresa' => sess_user_company(),
                    'id_grupo_tarifario' => $id_grupo_tarifario,
                    'id_usuario' => sess_user_id(),
                    'pre_agrupamento' => $pre_agrupamento
                );

                $this->oculto_atribuir_grupo_model->delete($id_grupo_tarifario, sess_user_company(), sess_user_id(), $pre_agrupamento);
                $this->oculto_atribuir_grupo_model->save($dbdata);

                echo 'success';
                return TRUE;
            }
        }
    }

    public function ajax_show_group()
    {
        if ($post = $this->input->post()) {
            if ($this->input->is_set('id_grupo_tarifario')) {
                $id_grupo_tarifario = $post['id_grupo_tarifario'];
                $pre_agrupamento = $post['tag'];

                $this->load->model('oculto_atribuir_grupo_model');
                $this->oculto_atribuir_grupo_model->delete($id_grupo_tarifario, sess_user_company(), sess_user_id(), $pre_agrupamento);

                echo 'success';
                return TRUE;
            }
        }
    }

    private function salvar_ex_ii_ipi($cad_item, $post)
    {
        $motivo = array();
        $update = false;
        $motivo['titulo'] = 'vinculacao_ex';

        if (isset($post['num_ex_ii']) && !empty($post['num_ex_ii'])) {
            $dbdata['num_ex_ii'] = $post['num_ex_ii'];

            if ($post['num_ex_ii'] == -1) {
                $descricao_ii = 'Item não atende EX.';
            } else {
                $ii = $this->ex_tarifario_model->get_ex_ii_by_ncm($post['num_ex_ii'], $cad_item->ncm_proposto);
                $descricao_ii = "<strong>EX:</strong> {$ii->num_ex}
                    <br /><strong>Descrição:</strong> {$ii->descricao_linha1}";
            }

            $motivo['descricao'] = '<strong>EX de II vinculado</strong><br />' . $descricao_ii;
            $update = true;
        }

        if ($post['num_ex_ipi'] && !empty($post['num_ex_ipi'])) {
            $dbdata['num_ex_ipi'] = $post['num_ex_ipi'];

            // Descrição EX de IPI.
            if ($post['num_ex_ipi'] == -1) {
                $descricao_ipi = 'Item não atende EX.';
            } else {
                $ipi = $this->ex_tarifario_model->get_ex_ipi_by_ncm($post['num_ex_ipi'], $cad_item->ncm_proposto);
                $descricao_ipi = "<strong>EX:</strong> {$ipi->num_ex}
                    <br /><strong>Descrição:</strong> {$ipi->descricao_linha1}";
            }

            $motivo['descricao'] = '<strong>EX de IPI vinculado</strong><br />' . $descricao_ipi;
            $update = true;
        }

        return $update ? $this->cad_item_model->update_item($cad_item->part_number, $cad_item->id_empresa, $dbdata, $motivo, $cad_item->estabelecimento) : false;
    }

    public function salvar()
    {
        if (has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor'))) {
            show_permission();
        }
        $id_empresa = sess_user_company();
        $return = false;

        $this->load->helper('formatador_helper');

        $post = $this->input->post();
        if (!empty($post)) {

            $count = 0;
            $igual = [];

            if (!empty($post['raw_attrs'])) {
                // Ensure raw_attrs is an array before applying array_unique
                if (is_string($post['raw_attrs'])) {
                    $post['raw_attrs'] = [$post['raw_attrs']];
                }
                $distinct = array_unique($post['raw_attrs']);
                $post['raw_attrs'] = $distinct;
            }

            $this->load->model(array(
                'cad_item_model',
                'grupo_tarifario_model',
                'ex_tarifario_model',
                'item_log_model',
                'nve_atributo_model',
                'cad_item_nve_model',
                'item_model',
                'cad_item_homologacao_model',
                'cad_item_attr_model',
                'app_ia_segmento_model',
                'app_ia_segmento_modelo_model',
                'app_ia_retreino_log_model',
                'empresa_model',
                'lessin_model',
                'ctr_pendencias_pergunta_model'
            ));

            $empresa = $this->empresa_model->get_entry(sess_user_company());
            $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
            $can_formatar_texto = company_can("formatar_texto", $funcoes_adicionais);
            $campos_adicionais = explode('|', $empresa->campos_adicionais);
            $hasOwner = in_array('owner', $campos_adicionais);

            if ($this->input->is_set('grupo')) {
                $grupo = $this->grupo_tarifario_model->get_entry($post['grupo']);
            }

            if (strlen($post['observacoes']) > 3000) {
                $this->message_next_render('<strong>Oops!</strong> É necessário que a observação tenha no máximo 3000 caracteres.', 'error');
            } else {
                if (!empty($post['delete'])) {
                    $this->load->library("Item/Status");
                    if (isset($post['itens']) && !empty($post['itens'])) {
                        if (is_string($post['itens'])) {
                            $post['itens'] = [$post['itens']];
                        }
                    }
                    foreach ($post['itens'] as $item) {

                        // $check_perguntas = $this->ctr_pendencias_pergunta_model->getPerguntasByPn($item['part_number'],$item['estabelecimento']);
                        $respostas_pendentes = $this->status->get_perguntas_pendentes(TRUE, $item['part_number'], $item['estabelecimento'], $id_empresa);
                        $perguntas_do_item   = $this->status->get_perguntas_pendentes(FALSE, $item['part_number'], $item['estabelecimento'], $id_empresa, FALSE);

                        // $check_cad_item = $this->cad_item_model->get_entry_by_pn($item['part_number'], sess_user_company(),$item['estabelecimento']);
                        //$check_perguntas = $this->ctr_pendencias_pergunta_model->getPerguntasByPn($item['part_number'],$item['estabelecimento']);
                        $check_cad_item = $this->cad_item_model->get_entry_by_pn($item['part_number'], sess_user_company(), $item['estabelecimento']);

                        if (!empty($perguntas_do_item->perguntas)  && empty($respostas_pendentes->perguntas) && !empty($check_cad_item)) {
                            $passou_perguntas_novas = $this->item_log_model->get_like_status('Perguntas Respondidas (Novas)', $item['part_number'], $item['estabelecimento'], $id_empresa);

                            if ($hasOwner) {
                                if ($passou_perguntas_novas > 0) {
                                    $this->status->set_status("perguntas_respondidas_novas");
                                } else {
                                    $this->status->set_status("respondido");
                                }
                            } else {
                                $this->status->set_status("respondido");
                            }
                        } else if (!empty($perguntas_do_item->perguntas)   && !empty($respostas_pendentes->perguntas)) {
                            $this->status->set_status("pendente_duvidas");
                        } else {
                            $this->status->set_status("em_analise");
                        }

                        $this->status->update_item($item['part_number'], $item['estabelecimento']);

                        $it = $this->cad_item_model->get_entry_by_pn($item['part_number'], sess_user_company());
                        $grupo = $this->grupo_tarifario_model->get_entry($it->id_grupo_tarifario);

                        if ($this->cad_item_model->delete($item['part_number'], sess_user_company(), $item['estabelecimento'])) {
                            $this->cad_item_model->delete_attr($check_cad_item->id_item);

                            if (isset($it->id_item) && !empty($it->id_item)) {
                                $this->cad_item_nve_model->drop_itens($it->id_item);
                                $this->cad_item_homologacao_model->drop_item($it->id_item);
                            }

                            $log_data['titulo'] = 'desvinculargrupo';
                            $log_data['motivo'] = '<strong>Part Number:</strong> ' . $item['part_number'] . '<br><strong>Estabelecimento: </strong> ' . $item['estabelecimento'] . '<br> <strong>Grupo atribuído:</strong> ' . $grupo->descricao . ' <br><small><strong>NCM Recomendada: </strong>' . $grupo->ncm_recomendada . '</small><br><strong>Observações: </strong>' . $post['observacoes'];
                            $log_data['part_number'] = $item['part_number'];
                            $log_data['estabelecimento'] = $item['estabelecimento'];
                            $log_data['id_usuario'] = sess_user_id();
                            $log_data['id_empresa'] = sess_user_company();
                            $log_data['criado_em'] = date('Y-m-d H:i:s');

                            $this->item_log_model->save($log_data);

                            $this->message_next_render('<strong>OK!</strong> Item desvinculado do grupo tarifário');
                        }
                    }
                } else {
                    $lessinResponse = array("error" => false);
                    $itensSalvos = [];
                    if (!empty($post['itens'])) {
                        if (is_string($post['itens'])) {
                            $post['itens'] = [$post['itens']];
                        }
                        foreach ($post['itens'] as $item) {
                            if (!$this->cad_item_model->check_item_exists($item['part_number'], sess_user_company(), $item['estabelecimento'])) {
                                $subsidio = isset($post['subsidio']) && !empty($post['subsidio']) ? $post['subsidio'] : $grupo->subsidio;
                                $caracteristica = isset($post['caracteristica']) && !empty($post['caracteristica']) ? $post['caracteristica'] : $grupo->caracteristica;

                                $descricao_proposta_completa = isset($post['descricao_proposta_completa']) && !empty($post['descricao_proposta_completa']) ? formatar_texto($can_formatar_texto, $post['descricao_proposta_completa']) : '';
                                $descricao = isset($post['descricao']) && !empty($post['descricao']) ? formatar_texto($can_formatar_texto, $post['descricao']) : '';
                                $evento = isset($post['evento']) && !empty($post['evento']) ? formatar_texto($can_formatar_texto, $post['evento']) : '';
                                $descricao_mercado_local = isset($post['descricao_mercado_local']) && !empty($post['descricao_mercado_local']) ? formatar_texto($can_formatar_texto, $post['descricao_mercado_local']) : '';
                                $is_drawback = isset($post['is_drawback']) && $post['is_drawback'] == 1 ? 1 : 0;

                                $item_row = $this->item_model->get_entry($item['part_number'], sess_user_company(), $item['estabelecimento']);
                                $memoria_classificacao = isset($post['memoria_classificacao']) && !empty($post['memoria_classificacao']) ? $post['memoria_classificacao'] : $item_row->memoria_classificacao;

                                if (isset($post['preencher_desc_resumida']) && !empty($post['preencher_desc_resumida']) && count($post['itens']) > 1) {
                                    $descricao = formatar_texto($can_formatar_texto, $item_row->descricao);
                                    $descricao_mercado_local = strtoupper(remove_acentos($descricao));
                                } else if (isset($post['concatenar_campos']) && !empty($post['concatenar_campos']) && count($post['itens']) > 1) {
                                    $descricao = $item_row->descricao;
                                    $descricao_mercado_local = formatar_texto($can_formatar_texto, $descricao);
                                }

                                $cad_item = array(
                                    'part_number'                 => $item['part_number'],
                                    'id_grupo_tarifario'          => $grupo->id_grupo_tarifario,
                                    'ncm_proposto'                => $grupo->ncm_recomendada,
                                    'id_empresa'                  => sess_user_company(),
                                    'origem'                      => 'atribuicao_manual',
                                    'estabelecimento'             => $item['estabelecimento'],
                                    'id_resp_fiscal'              => $item_row->id_resp_fiscal,
                                    'id_resp_engenharia'          => $item_row->id_resp_engenharia,
                                    'funcao'                      => (!empty($post['funcao']) ? formatar_texto($can_formatar_texto, $post['funcao']) : null),
                                    'inf_adicionais'              => (!empty($post['inf_adicionais']) ? formatar_texto($can_formatar_texto, $post['inf_adicionais']) : null),
                                    'aplicacao'                   => (!empty($post['aplicacao']) ? formatar_texto($can_formatar_texto, $post['aplicacao']) : null),
                                    'marca'                       => (!empty($post['marca']) ? formatar_texto($can_formatar_texto, $post['marca']) : null),
                                    'material_constitutivo'       => (!empty($post['material_constitutivo']) ? formatar_texto($can_formatar_texto, $post['material_constitutivo']) : null),
                                    'caracteristicas'             => (!empty($caracteristica)     ? $caracteristica    : null),
                                    'subsidio'                    => (!empty($subsidio)           ? $subsidio          : null),
                                    'dispositivo_legal'           => (!empty($grupo->dispositivo_legal)  ? $grupo->dispositivo_legal : null),
                                    'solucao_consulta'            => (!empty($grupo->solucao_consulta)   ? $grupo->solucao_consulta  : null),
                                    'memoria_classificacao'       => (!empty($memoria_classificacao)   ? $memoria_classificacao  : null),
                                    'descricao_mercado_local'     => (!empty($descricao_mercado_local) ? formatar_texto($can_formatar_texto, $descricao_mercado_local) : null),
                                    'suframa_destaque'            => !empty($post['suframa_destaque']) ? $post['suframa_destaque'] : NULL,
                                    'suframa_ppb'                 => !empty($post['suframa_ppb']) ? $post['suframa_ppb'] : NULL,
                                    'suframa_descricao'           => !empty($post['suframa_descricao']) ? $post['suframa_descricao'] : NULL,
                                    'suframa_produto'             => !empty($post['suframa_produto']) ? $post['suframa_produto'] : NULL,
                                    'suframa_codigo'              => !empty($post['suframa_codigo']) ? $post['suframa_codigo'] : NULL,
                                    // 'nve'                         => !empty($post['nve']) ? $post['nve'] : NULL,
                                    'li'                          => !empty($post['li']) ? $post['li'] : NULL,
                                    'li_orgao_anuente'            => !empty($post['li_orgao_anuente']) ? $post['li_orgao_anuente'] : NULL,
                                    'li_destaque'                 => !empty($post['li_destaque']) ? $post['li_destaque'] : NULL,
                                    'antidumping'                 => !empty($post['antidumping']) ? $post['antidumping'] : NULL,
                                    'id_classificacao_energetica' => !empty($post['classificacao_energetica']) ? $post['classificacao_energetica'] : NULL,
                                    'predicao_ordem'              => $post['has_predicao'] == false || $post['has_predicao'] == 'false' ? NULL : (int) $post['predicao_ordem'],
                                    'has_predicao'                => $post['has_predicao'] == false || $post['has_predicao'] == 'false' ? false : true
                                );

                                if (empty($post['funcao']) && !empty($item_row->funcao)) {
                                    $cad_item['funcao'] = formatar_texto($can_formatar_texto, $item_row->funcao);
                                }

                                if (empty($post['inf_adicionais']) && !empty($item_row->inf_adicionais)) {
                                    $cad_item['inf_adicionais'] = formatar_texto($can_formatar_texto, $item_row->inf_adicionais);
                                }

                                if (empty($post['aplicacao']) && !empty($item_row->aplicacao)) {
                                    $cad_item['aplicacao'] = formatar_texto($can_formatar_texto, $item_row->aplicacao);
                                }

                                if (empty($post['material_constitutivo']) && !empty($item_row->material_constitutivo)) {
                                    $cad_item['material_constitutivo'] = formatar_texto($can_formatar_texto, $item_row->material_constitutivo);
                                }

                                if (empty($post['marca']) && !empty($item_row->marca)) {
                                    $cad_item['marca'] = formatar_texto($can_formatar_texto, $item_row->marca);
                                }

                                if (!empty($post['funcao'])) {
                                    $cad_item['houve_funcao_manual'] = 1;
                                }

                                if (!empty($post['inf_adicionais'])) {
                                    $cad_item['houve_inf_adicionais_manual'] = 1;
                                }

                                if (!empty($post['aplicacao'])) {
                                    $cad_item['houve_aplicacao_manual'] = 1;
                                }

                                if (!empty($post['marca'])) {
                                    $cad_item['houve_marca_manual'] = 1;
                                }

                                if (!empty($post['material_constitutivo'])) {
                                    $cad_item['houve_material_constitutivo_manual'] = 1;
                                }

                                $dados_para_concactenar = [
                                    'suframa_descricao' => $post['suframa_descricao'],
                                    'descricao' => formatar_texto($can_formatar_texto, $item_row->descricao),
                                    'subsidio' => $subsidio,
                                    'funcao' => !empty($post['funcao']) ? formatar_texto($can_formatar_texto, $post['funcao']) : formatar_texto($can_formatar_texto, $item_row->funcao),
                                    'aplicacao' => !empty($post['aplicacao']) ? formatar_texto($can_formatar_texto, $post['aplicacao']) : formatar_texto($can_formatar_texto, $item_row->aplicacao),
                                    'material_constitutivo' => !empty($post['material_constitutivo']) ? formatar_texto($can_formatar_texto, $post['material_constitutivo']) : formatar_texto($can_formatar_texto, $item_row->material_constitutivo),
                                    'marca' => !empty($post['marca']) ? formatar_texto($can_formatar_texto, $post['marca']) : formatar_texto($can_formatar_texto, $item_row->marca),
                                    'li_destaque' => $post['li_destaque'],
                                    'nve' => isset($post['nve']) ? $post['nve'] : null,
                                    'part_number' => $cad_item['part_number']
                                ];

                                if (isset($post['concatenar_campos']) && !empty($post['concatenar_campos']) && count($post['itens']) > 1) {
                                    $descricao_proposta_completa = formatar_texto($can_formatar_texto, $this->concactenar_dados_descricao_proposta_completa($dados_para_concactenar));
                                }

                                $this->load->library("Item/Status");

                                $already_updated = false;

                                //atualizando os valores da tabela item, ao invés da tabela cad item (para não duplicar os valores entre as tabelas)

                                if (isset($post['observacoes_mestre']) && !empty($post['observacoes_mestre'])) {
                                    $already_updated = true;

                                    $id_status = $this->status->set_status(!empty($descricao_mercado_local) ? "homologar" : "revisao");
                                    $this->status->update_item($item['part_number'], $item['estabelecimento'], sess_user_company());

                                    $this->item_model->update_item($item['part_number'], sess_user_company(), array(
                                        'observacoes' => $post['observacoes_mestre'],
                                        'id_status' => $id_status
                                    ), 'Atualizando valor do campo Observações e ID Status através da atribuição do Item', $item['estabelecimento']);
                                }

                                if ($return = $this->cad_item_model->saveCadItem($cad_item)) {

                                    $item_return = $this->cad_item_model->get_entry($return);

                                    // Atribuir o wf_status_atributos ao item
                                    $this->setar_wf_status_atributos($item, $item_return);

                                    $itensSalvos[] = $item_return;
                                    if (!$already_updated) {
                                        $this->status->set_status(!empty($descricao_mercado_local) ? "homologar" : "revisao");
                                        $this->status->update_item($cad_item['part_number'], $cad_item['estabelecimento'], sess_user_company());
                                    }

                                    if (isset($post['num_ex_ii']) || isset($post['num_ex_ipi'])) {
                                        $this->salvar_ex_ii_ipi($item_return, $post);
                                    }

                                    if (!empty($post['nve'])) {
                                        if (is_string($post['nve'])) {
                                            $post['nve'] = [$post['nve']];
                                        }

                                        foreach ($post['nve'] as $nve) {
                                            $dbdata_nve = array(
                                                'nve_atributo' => $nve['key'],
                                                'nve_valor' => $nve['value'],
                                                'id_item' => $item_return->id_item
                                            );

                                            if ($this->cad_item_nve_model->save($dbdata_nve)) {
                                                $nve = $this->nve_atributo_model->get_valor($item_return->ncm_proposto, @$nve['key'], @$nve['attr']);

                                                $log_data_nve['titulo'] = 'vinculacao_nve';
                                                $log_data_nve['motivo'] = '
                                                    <strong>Atributo:</strong> ' . @$nve['key'] . '<br />
                                                    <strong>Código:</strong> ' . @$nve['attr'] . '<br />
                                                    <strong>Descrição: </strong> ' . @$nve->nm_especif_ncm;

                                                $this->item_log_model->save($log_data_nve);
                                            }
                                        }
                                    }

                                    $lessin = null;

                                    if (!empty($descricao_proposta_completa) || !empty($evento) || !empty($descricao) || !empty($memoria_classificacao) || in_array('lessin', $funcoes_adicionais)) {
                                        $updateItem = [];
                                        if (!empty($descricao_proposta_completa)) {
                                            $updateItem['descricao_proposta_completa'] = formatar_texto($can_formatar_texto, $descricao_proposta_completa);
                                        }

                                        if (!empty($evento)) {
                                            $updateItem['evento'] = formatar_texto($can_formatar_texto, $evento);
                                        }

                                        if (!empty($descricao)) {
                                            $updateItem['descricao'] = formatar_texto($can_formatar_texto, $descricao);
                                        }

                                        if (!empty($memoria_classificacao)) {
                                            $updateItem['memoria_classificacao'] = $memoria_classificacao;
                                        }


                                        $updateItem['is_drawback'] = $is_drawback;


                                        if (in_array('lessin', $funcoes_adicionais)) {
                                            $exII = false;

                                            if ($post['num_ex_ii'] > 0 && !empty($post['num_ex_ii'])) {
                                                $exII = $post['num_ex_ii'];
                                            }

                                            try {
                                                $lessin = $this->lessin_model->utiliza_lessin(array('ncm' => $cad_item['ncm_proposto']), $exII);

                                                $updateItem['regra_aplicada'] = @$lessin['regra'];
                                                $updateItem['lista_becomex'] = isset($lessin['utiliza_lessin']) && $lessin['utiliza_lessin'] ? 'SIM' : 'NÃO';

                                                if ($lessin['utiliza_lessin'] == 'NÃO' && strtolower($item_row->lista_cliente) == 'sim') {
                                                    $this->lessin_model->set_state('filter.ncm', $cad_item['ncm_proposto']);
                                                    $lessinRow = $this->lessin_model->get_item();
                                                    $updateItem['id_lessin'] = isset($lessinRow->id) ? $lessinRow->id : null;
                                                } else {
                                                    $updateItem['id_lessin'] = @$lessin['id'];
                                                }
                                            } catch (Exception $e) {
                                                $lessinResponse["error"] = true;
                                                $lessinResponse["message"] = "O item foi atribuído, mas algo inesperado aconteceu no tratamento Lessin.";
                                            }
                                        }

                                        $this->item_model->update_item($item_return->part_number, $item_return->id_empresa, $updateItem, false, $item['estabelecimento']);
                                    }

                                    $motivo = '<strong>Part Number:</strong> ' . $item['part_number'] . '<br><strong>Estabelecimento: </strong>' . $item['estabelecimento'] . '<br> <strong>Grupo atribuído:</strong> ' . $grupo->descricao . ' <br><small><strong>NCM Recomendada: </strong>' . $grupo->ncm_recomendada . '</small><br><strong>Observações: </strong>' . $post['observacoes'];

                                    if (!empty($post['funcao'])) {
                                        $motivo .= '<br><strong>Função:</strong> ' . formatar_texto($can_formatar_texto, $post['funcao']);
                                    }

                                    if (!empty($post['inf_adicionais'])) {
                                        $motivo .= '<br><strong>Informações Adicionais:</strong> ' . formatar_texto($can_formatar_texto, $post['inf_adicionais']);
                                    }

                                    if (!empty($post['aplicacao'])) {
                                        $motivo .= '<br><strong>Aplicação:</strong> ' . formatar_texto($can_formatar_texto, $post['aplicacao']);
                                    }

                                    if (!empty($post['marca'])) {
                                        $motivo .= '<br><strong>Marca:</strong> ' . formatar_texto($can_formatar_texto, $post['marca']);
                                    }

                                    if (!empty($post['material_constitutivo'])) {
                                        $motivo .= '<br><strong>Material Constitutivo:</strong> ' . formatar_texto($can_formatar_texto, $post['material_constitutivo']);
                                    }

                                    if (!empty($subsidio)) {
                                        $motivo .= '<br><strong>Subsidio:</strong> ' . $subsidio;
                                    }

                                    if (!empty($caracteristica)) {
                                        $motivo .= '<br><strong>Característica:</strong> ' . $caracteristica;
                                    }

                                    if (!empty($memoria_classificacao)) {
                                        $motivo .= '<br><strong>Memória de Classificação:</strong> ' . $memoria_classificacao;
                                    }

                                    if (!empty($descricao_mercado_local)) {
                                        $motivo .= '<br><strong>Descrição proposta resumida:</strong> ' . formatar_texto($can_formatar_texto, $descricao_mercado_local);
                                    }

                                    if (!empty($evento)) {
                                        $motivo .= '<br><strong>Evento:</strong> ' . formatar_texto($can_formatar_texto, $evento);
                                    }

                                    if (!empty($descricao)) {
                                        $motivo .= '<br><strong>Descrição:</strong> ' . formatar_texto($can_formatar_texto, $descricao);
                                    }

                                    if (!empty($descricao_proposta_completa)) {
                                        $motivo .= '<br><strong>Descrição proposta completa:</strong> ' . formatar_texto($can_formatar_texto, $descricao_proposta_completa);
                                    }

                                    if (!empty($post['nve'])) {
                                        if (is_string($post['nve'])) {
                                            $post['nve'] = [$post['nve']];
                                        }
                                        $post['nve'] = (array_key_exists(0, $post['nve'])) ? $post['nve'][0] : $post['nve'];
                                        $motivo .= '<br><strong>NVE:</strong> ' . implode(",", $post['nve']);
                                    }
                                    if (!empty($post['li'])) {
                                        $motivo .= '<br><strong>LI:</strong> ' . $post['li'];
                                    }
                                    if (!empty($post['li_orgao_anuente'])) {
                                        $motivo .= '<br><strong>Orgão Anuente:</strong> ' . $post['li_orgao_anuente'];
                                    }
                                    if (!empty($post['li_destaque'])) {
                                        $motivo .= '<br><strong>LI Destaque:</strong> ' . $post['li_destaque'];
                                    }
                                    if (!empty($post['antidumping'])) {
                                        $motivo .= '<br><strong>ANTIDUMPING:</strong> ' . $post['antidumping'];
                                    }

                                    if (!empty($lessin)) {
                                        $utilizaLessin = isset($updateItem['lista_becomex']) && !empty($updateItem['lista_becomex']) ? 'SIM' : 'NÃO';
                                        $motivo .= '<br/><strong>LESSIN Lista Becomex:</strong>' . $utilizaLessin . ' - Regra aplicada:' . @$lessin['regra'];
                                    }

                                    $log_data['titulo'] = 'atribuicaogrupo';
                                    $log_data['motivo'] = $motivo;
                                    $log_data['part_number'] = $item['part_number'];
                                    $log_data['estabelecimento'] = $item['estabelecimento'];
                                    $log_data['id_usuario'] = sess_user_id();
                                    $log_data['id_empresa'] = sess_user_company();
                                    $log_data['criado_em'] = date('Y-m-d H:i:s');

                                    $this->item_log_model->save($log_data);
                                }
                            }
                        }
                    }

                    if (!empty($post['raw_attrs'])) {
                        $this->cad_item_attr_model->set_state('filter.vinculacao', false);
                        $this->cad_item_attr_model->save_attrs($post['raw_attrs'], $itensSalvos);
                    }

                    if ($return && !$lessinResponse["error"]) {
                        $this->message_next_render('<strong>OK!</strong> Atribuição realizada com sucesso');
                    } else {
                        $alert_message = $lessinResponse["error"] ?
                            $lessinResponse["message"] :
                            '<strong>Oops!</strong> Ocorreu um erro ao atribuir o grupo';

                        $this->message_next_render($alert_message, 'error');
                    }
                }
            }
        }

        $itens = [];

        if (isset($post['filtros']) && is_array($post['filtros'])) {
            $filtros = $post['filtros'];
            $this->item_model->set_state('filter.atribuido_para', $filtros['atribuido_para'] ?? null);
            $this->item_model->set_state('filter.tag', $filtros['tag'] ?? null);
            $this->item_model->set_state('filter.item_atribuido', true);

            $itens = $this->item_model->get_entries_by_pn_or_desc(null, $filtros['tag'] ?? null);
        }

        if (empty($itens)) {
            $this->item_model->unset_state('filter.atribuido_para');
            $this->item_model->unset_state('filter.tag');
            $this->item_model->set_state('filter.item_atribuido', false);
        }

        return response_json([
            'msg' => 'Atribuição realizada com sucesso',
            'itens' => $itens
        ], 200);
    }

    public function alterar_tag()
    {
        if (has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor'))) {
            show_404();
        }

        if ($post = $this->input->post()) {

            if (!empty($post['motivo'])) {
                $old_tag = $this->item_model->get_item_tag($post['item'], sess_user_company(), $post['estabelecimento']);

                $this->item_model->save(
                    array('tag' => $post['tag'], 'status' => '2-sugerido_cliente'),
                    array(
                        'part_number' => addslashes($post['item']),
                        'id_empresa' => sess_user_company(),
                        'estabelecimento' => !empty($post['estabelecimento']) ? $post['estabelecimento'] : ''
                    )
                );

                $this->load->model('item_log_model');

                if ($post['tag'] == 'becomex') {
                    $tag = 'BECOMEX - Grupo tarifário não encontrado';
                } else if ($post['tag'] == 'cliente') {
                    $tag = 'CLIENTE - Dúvida no item';
                } else {
                    $tag = $post['tag'];
                }

                if ($old_tag->tag == 'becomex') {
                    $old_tag = 'BECOMEX - Grupo tarifário não encontrado';
                } else if ($old_tag->tag == 'cliente') {
                    $old_tag = 'CLIENTE - Dúvida no item';
                } else {
                    $old_tag = $old_tag->tag;
                }

                $log_data['titulo'] = 'alterartag';
                $log_data['motivo'] = '<strong>Part Number:</strong> ' . $post['item'] . '<br><strong>Estabelecimento: </strong>' . $post['estabelecimento'] . '<br><strong>De: </strong>' . $old_tag . '<br> <strong>Para:</strong> ' . $tag . '<br><strong>Observações: </strong>' . $post['motivo'];
                $log_data['part_number'] = $post['item'];
                $log_data['estabelecimento'] = $post['estabelecimento'];
                $log_data['id_usuario'] = sess_user_id();
                $log_data['id_empresa'] = sess_user_company();
                $log_data['criado_em'] = date('Y-m-d H:i:s');

                $this->item_log_model->save($log_data);

                echo 'success';
                return TRUE;
            }
        }
    }

    public function set_filter_part_number($part_numbers)
    {
        $part_numbers_copy = $part_numbers;
        $id_empresa = sess_user_company();

        if (!empty($part_numbers)) {
            $arr_filter = array();

            if (!is_array($part_numbers) && !empty($part_numbers)) {
                $separator = get_company_separator($id_empresa);
                if (strpos($part_numbers, "\n") !== false) {
                    $separator = !empty($separator) ? $separator : "\n";
                }
                $part_numbers = str_replace(array("\t", "\r\n", "\s", "\n"), $separator, $part_numbers);

                $matches = array();
                //Part numbers entre aspas duplas
                preg_match_all('/"([^"]*)"/', $part_numbers, $matches);
                $btw_quotes = $matches[1];

                //Part numbers com wildcard *
                $part_numbers = str_replace("*", "%", $part_numbers);
                $part_numbers = preg_replace('/"([^"]*)"/', "", $part_numbers);

                //Part numbers entre aspas simples
                $matches_simple = array();
                preg_match_all('~\'(.*?)\'~', $part_numbers, $matches_simple);
                $btw_simple_quotes = $matches_simple[1];

                //Retira da string todos os partnumbers entre aspas simples
                $part_numbers = preg_replace('~\'(.*?)\'~', "", $part_numbers);

                $part_numbers = !empty($separator) && $separator != ' '  && count(explode($separator, addslashes($part_numbers))) > 2 ? explode($separator, addslashes($part_numbers)) : [$part_numbers];

                $part_numbers = array_filter($part_numbers);

                if (!empty($btw_quotes)) {
                    $addslashes_btw_quotes = implode(',', $btw_quotes);
                    $btw_quotes = explode(",", addslashes($addslashes_btw_quotes));
                }

                $part_numbers = array_merge($part_numbers, $btw_quotes);
                $part_numbers = array_merge($part_numbers, $btw_simple_quotes);

                $generic_part_numbers = array();

                foreach ($part_numbers as $key => $part_number) {

                    if (strpos($part_number, "%")) {
                        $generic_part_numbers[] = $part_number;
                        unset($part_numbers[$key]);
                    }
                }

                if (!empty($part_numbers)) {
                    $arr_filters['part_numbers'] = $part_numbers;
                } else {
                    $arr_filters['part_numbers'] = NULL;
                }

                if (!empty($generic_part_numbers)) {
                    $arr_filters['generic_part_numbers'] = $generic_part_numbers;
                } else {
                    $arr_filters['generic_part_numbers'] = NULL;
                }
            }
            return $arr_filters;
        }
    }

    public function atualizar_owner()
    {

        $this->load->model('item_model');
        $this->load->model('item_log_model');

        $itens = $this->input->post('itens');
        $novo_owner = $this->input->post('owner_responsavel');
        $novo_owner_data = $this->item_model->get_data_owner($novo_owner);
        $motivo = $this->input->post('motivo_owner');
        $id_empresa = sess_user_company();
        $id_usuario = sess_user_id();
        $responsavel_atual = '';
        $responsavel_novo = '';

        foreach ($novo_owner_data as $owner_novo) {
            if (isset($owner_novo->responsavel_gestor) && $owner_novo->responsavel_gestor == 1) {
                $responsavel_novo = $owner_novo->nome;
                break;
            }
        }

        foreach ($itens as $item) {

            $owner_atual = $this->item_model->get_owners($item['part_number'], $id_empresa, $item['estabelecimento']);

            if (empty($owner_atual))
                continue;

            foreach ($owner_atual as $owner) {
                if (isset($owner->responsavel_gestor) && $owner->responsavel_gestor == 1) {
                    $responsavel_atual = $owner->nome;
                    break;
                }
            }

            $log_data['part_number'] = $item['part_number'];
            $log_data['estabelecimento'] = $item['estabelecimento'];
            $log_data['titulo'] = 'atualizacao';
            $log_data['motivo'] = $this->generate_motivo_string($owner_atual[0], $novo_owner, $novo_owner_data[0], $responsavel_atual, $responsavel_novo, $motivo);
            $log_data['id_usuario'] = $id_usuario;
            $log_data['id_empresa'] = $id_empresa;
            $log_data['criado_em'] = date('Y-m-d H:i:s');

            $this->item_log_model->save($log_data);
        }

        $result = $this->item_model->atualizar_owner($itens, $novo_owner, $id_empresa);

        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }

    private function generate_motivo_string($owner_atual, $novo_owner, $novo_owner_data, $responsavel_atual, $responsavel_novo, $motivo)
    {
        $motivo_string = 'Transferência de Owner de: ' . '<strong>' . $owner_atual->codigo . ' - ' . $owner_atual->descricao . ' - ' . $responsavel_atual . '</strong>' . '<br>' . ' Para: ' . '<strong>' . $novo_owner . ' - ' . $novo_owner_data->descricao . ' - ' . $responsavel_novo . '</strong>' . '<br><strong>Motivo: </strong>' . $motivo;

        return $motivo_string;
    }

    public function transferir_usuario()
    {
        if ($post = $this->input->post()) {
            $dbdata = array();
            $response = array();
            $usuario = NULL;

            if ($this->input->is_set('id_usuario') && !empty($post['id_usuario'])) {
                $this->load->model('usuario_model');
                $usuario = $this->usuario_model->get_entry($post['id_usuario']);

                if ($post['tipo_responsavel'] == "engenheiro") {
                    $responsavel = 'engenharia';
                    $field_responsavel = 'id_resp_engenharia';
                } else {
                    $responsavel = 'fiscal';
                    $field_responsavel = 'id_resp_fiscal';
                }

                $dbdata[$field_responsavel] = $post['id_usuario'];
            } else {
                $response['error'] = '<div class="alert alert-danger"><strong>Oops!</strong> Informe o novo usuário responsável para concluir a transferência.</div>';
            }

            if ($this->input->is_set('motivo') && empty($post['motivo'])) {
                $response['error'] = '<div class="alert alert-danger"><strong>Oops!</strong> Informe o motivo para concluir a transferência de usuário.</div>';
            }

            if (empty($post['tipo_responsavel'])) {
                $response['error'] = '<div class="alert alert-danger"><strong>Oops!</strong> Selecione o tipo do novo usuário responsável para concluir a transferência.</div>';
            }

            if (empty($post['itens'])) {
                $response['error'] = '<div class="alert alert-danger"><strong>Oops!</strong> Selecione pelo menos um item para transferir.</div>';
            }

            if (!empty($response)) {
                echo json_encode($response);
                return TRUE;
            } else {
                $error = FALSE;
                // $part_numbers = '';

                foreach ($post['itens'] as $key => $item) {
                    $item = $this->item_model->get_entry($item['part_number'], sess_user_company(), $item['estabelecimento']);
                    $itemArray = (array)$item;

                    $old_usuario = null;

                    // if ($key > 0) {
                    //     $part_numbers .= ', ';
                    // }

                    // $part_numbers .= $item->part_number;

                    try {
                        $old_usuario = $this->usuario_model->get_entry($itemArray[$field_responsavel]);
                    } catch (Exception $e) {
                        $old_usuario = NULL;
                    }

                    $motivo  = 'Transferência de responsável ' . $responsavel;

                    if (!empty($old_usuario)) {
                        $motivo .= ' ' . $old_usuario->nome . ' (ID.: ' . $old_usuario->id_usuario . '),';
                    }

                    $motivo .= ' para usuário ' . $usuario->nome . ' (ID.: ' . $usuario->id_usuario . ')<br>';
                    $motivo .= '<strong>Motivo: </strong>' . $post['motivo'];

                    if (!$this->item_model->update_item($item->part_number, $item->id_empresa, $dbdata, $motivo, $item->estabelecimento)) {
                        $error = TRUE;
                    }
                }

                if ($error === FALSE) {
                    $response['success'] = '<div class="alert alert-success"><strong>OK!</strong> Transferência de responsável concluída com sucesso.</div>';

                    /*
                     * A pedidos de Paulo Momm (07/12/2015), a função de envio de e-mail
                     * está desabilitada temporariamente, devido a desconfiguração em alguns
                     * clientes de e-mail (Outlook).
                     *
                     * Desabilitado por: Thaynã Bruno Moretti

                    $this->load->library('email');

                    $this->email->to($usuario->email);
                    $this->email->from($this->config->item('mail_from_addr'), $this->config->item('mail_from_name'));
                    $this->email->subject('[Gestão Tarifária] - Transferência de responsável');

                    $data['base_url'] = config_item('online_url');
                    $data['html_message'] =
                       '<h4>Notificação de transferência de responsável</h4>
                        <p>'.$usuario->nome.', você foi designado como responsável ('.$responsavel.') pelos seguintes itens: '.$part_numbers.'.<br>
                        <strong>Motivo da transferência:</strong> '.$post['motivo'].'</p>
                        <p>Em caso de dúvidas, contate o gerente de projetos.</p>';

                    $html = $this->load->view('templates/basic_template', $data, TRUE);

                    $this->email->message($html);
                    if (! $this->email->send())
                    {
                        // print_r($this->email->print_debugger); die();
                    }

                    if (!customer_has_role('cliente_pmo', $usuario->id_usuario))
                    {
                        if ($gp = $this->usuario_model->get_gp_by_empresa(sess_user_company()))
                        {
                            $this->email->to($gp->email);
                            $this->email->from($this->config->item('mail_from_addr'), $this->config->item('mail_from_name'));
                            $this->email->subject('[Gestão Tarifária] - Transferência de responsável');

                            $data['base_url'] = config_item('online_url');
                            $data['html_message'] =
                               '<h4>Notificação de transferência de responsável</h4>
                                <p>'.$gp->nome.', o usuário '.$usuario->nome.' foi designado como responsável ('.$responsavel.') pelos seguintes itens: '.$part_numbers.'.<br>
                                <strong>Motivo da transferência:</strong> '.$post['motivo'].'<br>
                                <strong>Usuário que efetuou a transferência: </strong>'.$usuario_logado->nome.'</p>
                                <p>Em caso de dúvidas, contate o usuário responsável pela transferência.</p>';

                            $html = $this->load->view('templates/basic_template', $data, TRUE);

                            $this->email->message($html);
                            if (!$this->email->send())
                            {
                                //print_r($this->email->print_debugger()); die();
                            }
                        }
                    }*/
                }
            }
            echo json_encode($response);
            return TRUE;
        }
    }

    public function ajax_get_users_filters()
    {
        $this->load->model('usuario_model');

        $this->usuario_model->set_state('filter.id_empresa', sess_user_company());
        $this->usuario_model->set_state('order_by', array('u.id_perfil' => 'desc', 'nome' => 'asc'));
        $lista_usuarios_atribuidos = $this->usuario_model->get_entries_filtro_atribuido_para();

        $id_usuario = sess_user_id();

        $options = '<option value="' . $id_usuario . '">Meu usuário</option>
                    <option value="-1">Todos os responsáveis</option>
                    <option value="sem_responsavel">Sem responsável</option>';

        $id_perfil = null;
        foreach ($lista_usuarios_atribuidos as $k => $usuario) {
            if ($usuario->id_usuario == sess_user_id()) continue; {
                if (customer_has_role('sysadmin', $usuario->id_usuario)) continue;
                if (customer_has_role('becomex_pmo', $usuario->id_usuario)) continue;

                if (
                    customer_has_role('cliente_pmo', $usuario->id_usuario)
                    && (has_role('fiscal') || has_role('engenheiro'))
                ) {
                    $options .=
                        '<optgroup label="Gerente de Projetos">
                        <option value="' . $usuario->id_usuario . '" data-subtext="' . $usuario->email . '">' . $usuario->nome . ' (' . $usuario->total_resp . ')</option>
                    </optgroup>';
                } else {
                    if ($id_perfil == null || ($id_perfil !== $usuario->id_perfil)) {
                        $options .= ($id_perfil !== null) ? "</optgroup>" : '';
                        $options .= "<optgroup label=\"{$usuario->perfil}\">";

                        $id_perfil = $usuario->id_perfil;
                    }

                    $options .=
                        '<option ' . set_select('atribuido_para', $usuario->id_usuario, $usuario->id_usuario == $this->item_model->get_state('filter.atribuido_para')) . '
                        value="' . $usuario->id_usuario . '" data-subtext="' . $usuario->email . '">' .
                        $usuario->nome . ' (' . $usuario->total_resp . ')
                    </option>';

                    if (count($lista_usuarios_atribuidos) == $k + 1) {
                        $options .= "</optgroup>";
                    }
                }
            }
        }

        echo $options;
    }

    public function xhr_get_suframa_produtos_by_code()
    {

        try {

            $response = [
                'message' => 'sucesso!',
                'data' => [],
            ];
            $status = 200;

            $this->load->model('suframa_model');

            $code = $this->input->get('code');
            $ncm = $this->input->get('ncm');

            $response['data'] = $this->suframa_model->get_suframa_produtos_lista_by_code($code, $ncm);
        } catch (Exception $e) {
            $reponse_data['message'] = $e->getMessage();
            $status = 406;
        }

        return response_json($response, $status);
    }

    public function ajax_get_nve()
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'nve_atributo_model',
                'cad_item_nve_model'
            ));

            $ncm = $post['ncm'];
            $atributos = $this->nve_atributo_model->get_atributos_by_ncm($ncm);

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_nve', array(
                    'ncm' => $ncm,
                    'atributos' => $atributos
                ), true),
                'has_atributos' =>  empty($atributos) ? false : true
            ));
        }
    }

    public function ajax_get_li()
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'ncm_model'
            ));

            $ncm = $post['ncm'];
            $lis = !empty($ncm) ? $this->ncm_model->get_li_oracle($ncm) : [];

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_li', array(
                    'lis' => $lis,
                    'ncm' => $ncm
                ), true),
            ));
        }
    }

    private function order_ex_ii($ex_ii, $itens_relacionados_ex)
    {
        $ex_atribuidos = [];

        foreach ($itens_relacionados_ex as $relacionados) {
            if (isset($relacionados->num_ex)) {
                $ex_atribuidos[$relacionados->num_ex] = isset($ex_atribuidos[$relacionados->num_ex]) ? $ex_atribuidos[$relacionados->num_ex] + 1 : 1;
            }
        };

        if (empty($ex_atribuidos))
            return $ex_ii;

        ksort($ex_atribuidos);
        arsort($ex_atribuidos);
        $ex_ii_ordenado = [];

        foreach ($ex_atribuidos as $num_ex_attr => $ex_attr) {
            foreach ($ex_ii as $key => $ex) {
                if ($ex->num_ex == $num_ex_attr) {
                    $ex_ii_ordenado[] = $ex;
                    unset($ex_ii[$key]);
                    continue;
                }
            };
        }
        return  array_merge($ex_ii_ordenado, $ex_ii);
    }

    public function ajax_get_ex_ii()
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'ex_tarifario_model',
                'grupo_tarifario_model'
            ));

            $ncm = $post['ncm'];
            $grupo_tarifario = $post['idGrupoTarifario'];

            $ex_ii = !empty($ncm) ?
                $this->ex_tarifario_model->get_all_ex_ii_order_by_percent_by_ncm($ncm, true)
                : [];

            set_time_limit(0);
            ini_set('memory_limit', '2048M');
            $itens_relacionados_ex = $this->ex_tarifario_model->get_all_itens_related_ex($ncm, $grupo_tarifario, $ex_ii);
            $id_grupo_tarifario = $post['idGrupoTarifario'];
            $entry = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

            $possui_nao_relacionado = false;

            $naoAtende = new stdClass();
            $naoAtende->num_ex            = -1;
            $naoAtende->descricao_linha1  = "Item não atende EX.";
            $naoAtende->data_vigencia_ini = " - ";
            $naoAtende->data_vigencia_fim = " - ";
            $naoAtende->pct_ipi           = " - ";

            if (count($itens_relacionados_ex)) {
                /**
                 * Opção - Não Atende
                 */
                $naoAtendeRelacionados = new stdClass();

                $naoAtendeRelacionados->num_ex    = -1;
                $naoAtendeRelacionados->part_number = " - ";
                $naoAtendeRelacionados->descricao = "Não há itens atribuídos para este EX.";

                $itens_relacionados_ex[] = $naoAtendeRelacionados;
            }

            if (count($ex_ii)) {

                foreach ($ex_ii as $relacionados) {
                    if (isset($relacionados->num_ex) && $relacionados->num_ex == '-1') {
                        $possui_nao_relacionado = true;
                    }
                }
            }

            if ($possui_nao_relacionado == true) {
                $ex_ii[] = $naoAtende;
                $ex_ii = $this->order_ex_ii($ex_ii, $itens_relacionados_ex);
            } else {
                $ex_ii = $this->order_ex_ii($ex_ii, $itens_relacionados_ex);
                $ex_ii[] = $naoAtende;
            }

            $key_selected = null;
            $array1 = [];
            foreach ($ex_ii as $k => $ex) {
                if ($ex->num_ex == $entry->num_ex_ii) {
                    $key_selected  = $k;
                }
            }
            if ($key_selected != null) {
                $array1[] =  $ex_ii[$key_selected];
                unset($ex_ii[$key_selected]);
            }

            $array2 = $ex_ii;
            $mergedArray = array_merge($array1, $array2);

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_ex_ii', array(
                    'ex_iis' => $mergedArray,
                    'itens_relacionados_ex' => $itens_relacionados_ex,
                    'ncm' => $ncm,
                    'num_ex_ii' => $entry->num_ex_ii
                ), true),
            ));
        }
    }

    public function ajax_get_ex_ipi()
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'ex_tarifario_model',
                'grupo_tarifario_model'
            ));

            $ncm = $post['ncm'];
            $id_grupo_tarifario = $post['idGrupoTarifario'];

            $ex_ipi = $this->ex_tarifario_model->get_all_ex_ipi_by_ncm($ncm, true);

            $entry = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

            if (count($ex_ipi)) {
                /**
                 * Opção - Não Atende
                 */
                $naoAtende = new stdClass();

                $naoAtende->num_ex            =    -1;
                $naoAtende->descricao_linha1  = "Item não atende EX.";
                $naoAtende->data_vigencia_ini = " - ";
                $naoAtende->data_vigencia_fim = " - ";
                $naoAtende->pct_ipi           = " - ";

                $ex_ipi[] = $naoAtende;
            }

            $key_selected = null;
            $array1 = [];
            foreach ($ex_ipi as $k => $ex) {
                if ($ex->num_ex == $entry->num_ex_ipi) {
                    $key_selected  = $k;
                }
            }
            if ($key_selected != null) {
                $array1[] =  $ex_ipi[$key_selected];
                unset($ex_ipi[$key_selected]);
            }

            $array2 = $ex_ipi;
            $mergedArray = array_merge($array1, $array2);

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_ex_ipi', array(
                    'ex_ipis' => $mergedArray,
                    'ncm' => $ncm,
                    'num_ex_ipi' => $entry->num_ex_ipi
                ), true),
            ));
        }
    }

    public function ajax_get_classificacao_energetica()
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'ncm_model'
            ));

            $ncm = $post['ncm'];
            $items = !empty($ncm) ? $this->ncm_model->get_classificacao_energetica($ncm) : [];

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_classificacao_energetica', array(
                    'items' => $items,
                    'ncm' => $ncm
                ), true),
            ));
        }
        return response_json(array());
    }

    public function ajax_get_atributos()
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'catalogo/produto_model'
            ));

            $ncm = trim($post['ncm']);
            $atributos = !empty($ncm) ? $this->produto_model->get_attr_ncm($ncm) : [];

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_atributos', array(
                    'items' => $atributos,
                    'ncm' => $ncm,
                    'idGrupoTarifario' => $post['idGrupoTarifario']
                ), true),
                'has_itens' => (!empty($atributos->listaAtributos))
            ));
        }

        return response_json(array());
    }

    public function ajax_get_perguntas_respostas()
    {
        $post = $this->input->post();
        $partnumber = isset($post['dataItem']['part_number']) ? $post['dataItem']['part_number'] : $post['dataItem'];
        // $estabelecimento = $post['dataItem']['estabelecimento'];

        $this->load->model(array(
            'ctr_anexo_resposta_model'
        ));

        $this->ctr_pendencias_pergunta_model->set_state('filter.partnumbers', $partnumber);
        $perguntas = $this->ctr_pendencias_pergunta_model->getHistoricoPerguntas(TRUE, TRUE);

        foreach ($perguntas as $k => $pergunta) {
            $pergunta->arquivo = $this->ctr_anexo_resposta_model->getEntriesByResposta($pergunta->id_resposta);
        }

        return response_json(array(
            'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_perguntas_respostas', array(
                'perguntas' => $perguntas
            ), true)
        ));

        $post = $this->input->post();
        $ncm = trim($post['ncm']);
        $atributos = !empty($ncm) ? $this->produto_model->get_attr_ncm($ncm) : [];

        return $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_perguntas_respostas');

        if ($post = $this->input->post()) {
            $this->load->model(array(
                'catalogo/produto_model'
            ));

            $ncm = trim($post['ncm']);
            $atributos = !empty($ncm) ? $this->produto_model->get_attr_ncm($ncm) : [];

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_perguntas_respostas', array(), true),
                'has_itens' => (!empty($atributos->listaAtributos))
            ));
        }

        return response_json(array());
    }

    public function ajax_get_antidumping()
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'ncm_model'
            ));

            $ncm = $post['ncm'];
            $antidumpings = !empty($ncm) ? $this->ncm_model->get_antidumping_oracle($ncm) : [];

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_antidumping', array(
                    'antidumpings' => $antidumpings,
                    'ncm' => $ncm
                ), true),
            ));
        }
    }

    private function myconcat($text, $newText, $punct = '.')
    {
        $text = trim($text);
        $newText = trim($newText);

        $text = preg_replace('/(.+?)([.,])+$/', '\1', $text);

        if (strlen($newText) > 0) {
            $first = !empty($text) ? ($punct . ' ') : '';
            $text .= $first . $newText;
            return $text;
        }

        return $text;
    }

    public function concactenar_dados_descricao_proposta_completa($data)
    {
        $desc_completa_text = '';
        $has_suframa = false;

        $campos = $this->empresa_model->get_concatenar_campos(sess_user_company());

        foreach ($campos as $campo) {
            if (isset($campo->checked) && $campo->checked) {
                switch ($campo->slug) {
                    case 'suframa':
                        if (isset($data["suframa_descricao"]) && !empty($data["suframa_descricao"])) {
                            $has_suframa = true;
                            $desc_completa_text = $data["suframa_descricao"] . " **SUFRAMA** ";
                        }
                        break;

                    case 'descricao':
                        if (!empty($data["descricao"])) {
                            $desc_completa_text = $this->myconcat($desc_completa_text, $data['descricao'], '');
                            $has_suframa = false;
                        }
                        break;

                    case 'subsidio':
                        if (!empty($data["subsidio"])) {
                            $desc_completa_text = $this->myconcat($desc_completa_text, $data["subsidio"], ',');
                            $has_suframa = false;
                        }
                        break;

                    case 'funcao':
                        if (!empty($data["funcao"])) {
                            $desc_completa_text = $this->myconcat($desc_completa_text, "FUNCAO: " . $data["funcao"], $has_suframa ? '' : '.');
                            $has_suframa = false;
                        }
                        break;

                    case 'aplicacao':
                        if (!empty($data["aplicacao"])) {
                            $desc_completa_text = $this->myconcat($desc_completa_text, "APLICACAO: " . $data["aplicacao"], $has_suframa ? '' : '.');
                            $has_suframa = false;
                        }
                        break;

                    case 'material_constitutivo':
                        if (!empty($data["material_constitutivo"])) {
                            $desc_completa_text = $this->myconcat($desc_completa_text, "MATERIAL CONSTITUTIVO: " . $data["material_constitutivo"], $has_suframa ? '' : '.');
                            $has_suframa = false;
                        }
                        break;

                    case 'marca':
                        if (!empty($data["marca"])) {
                            $desc_completa_text = $this->myconcat($desc_completa_text, "MARCA: " . $data["marca"], $has_suframa ? '' : '.');
                            $has_suframa = false;
                        }
                        break;

                    case 'li':
                        if (isset($data["li_destaque"]) && !empty($data["li_destaque"])) {
                            $desc_completa_text = $this->myconcat($desc_completa_text, "DESTAQUE: " . $data["li_destaque"], $has_suframa ? '' : '.');
                            $has_suframa = false;
                        }
                        break;

                    case 'nve':
                        if (isset($data["nve"]) && !empty($data["nve"])) {
                            $desc_atributos_nve = "ATRIBUTOS NVE: ";

                            $i = 0;
                            foreach ($data["nve"] as $nve) {
                                $i = $i + 1;
                                if (!empty($nve['value'])) {
                                    $desc_atributos_nve .= $nve['key'] . " ";
                                    if ($i == count($data["nve"])) {
                                        $desc_atributos_nve .= $nve['value']  . " ";
                                    } else {
                                        $desc_atributos_nve .= $nve['value']  . "; ";
                                    }
                                }
                            }

                            $desc_completa_text = $this->myconcat($desc_completa_text, $desc_atributos_nve, $has_suframa ? '' : '.');
                            $has_suframa = false;
                        }
                        break;

                    case 'cod_importador':
                        if (isset($data['part_number']) && !empty($data['part_number'])) {
                            $descricao_final = "COD. IMPORTADOR: " . $data['part_number'];

                            $desc_completa_text = $this->myconcat($desc_completa_text, $descricao_final, $has_suframa ? '' : '.');
                        }
                        break;

                    case 'cod_fornecedor':
                        if (isset($data['part_number']) && !empty($data['part_number'])) {
                            $descricao_final = "COD. FORNECEDOR: " . $data['part_number'];

                            $desc_completa_text = $this->myconcat($desc_completa_text, $descricao_final, $has_suframa ? '' : '.');
                        }
                        break;

                    default:
                        # code...
                        break;
                }
            }
        }

        // if (isset($data['part_number']) && !empty($data['part_number'])) {
        //     $descricao_final = "COD. IMPORTADOR: " . $data['part_number'] . " ";
        //     $descricao_final .= "COD. FORNECEDOR: " . $data['part_number'];

        //     $desc_completa_text = $this->myconcat($desc_completa_text, $descricao_final, $has_suframa ? '' : '.');
        // }

        return $desc_completa_text;
    }

    public function pre_visualizar_email_infos_erp()
    {
        $this->load->model('usuario_model');
        $userData = $this->usuario_model->get_entry(sess_user_id());

        $this->load->model('ctr_resposta_model');

        $partnumber = urldecode($this->input->get('partNumber'));
        $estabelecimento = urldecode($this->input->get('estabelecimento'));

        $this->ctr_resposta_model->set_state('filter.partnumber', $partnumber);
        $this->ctr_resposta_model->set_state('filter.estabelecimento', $estabelecimento);

        $historicoItem = $this->ctr_resposta_model->getHistoricoItemGrupo();

        $data = array(
            'unidadeNegocio' => urldecode($this->input->get('unidadeNegocio')),
            'partNumber' => urldecode($this->input->get('partNumber')),
            'owner' => urldecode($this->input->get('owner')),
            'assunto' => urldecode($this->input->get('assunto')),
            'motivo' => urldecode($this->input->get('motivo')),
            'descricao' => urldecode($this->input->get('itemDescricao')),
            'usuario' => $userData->nome,
            'historicoItem' => $historicoItem,
            'status' => 'Revisar Informações ERP',
            'base_url' => config_item('online_url') . '/',
        );

        $this->load->view('templates/notificacao_revisar_infos_erp', $data);
    }

    public function enviar_email_infos_erp()
    {
        $id_empresa = sess_user_company();
        $this->load->model('empresa_model');
        $cnpj_raiz_logado = $this->empresa_model->get_entry_cnpj_raiz($id_empresa);

        $this->load->model('usuario_model');
        $userData = $this->usuario_model->get_entry(sess_user_id());

        $this->load->model('ctr_resposta_model');

        $item = $this->input->post('item');

        $partnumber = $item['part_number'];
        $estabelecimento = $item['estabelecimento'];


        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $this->load->model('unidade_negocio_model');
        // $unidadeNegocios = $this->unidade_negocio_model->get_unidade_negocio_by_empresa($empresa);
        $stats = "success";

        $unidadeNegocios = $this->unidade_negocio_model->get_unidade_negocio_by_item($partnumber, $id_empresa);
        // $usuariosUnidadeNegocio = $this->unidade_negocio_model->get_usuarios_unidade_negocio($unidadeNegocios);

        if (empty($unidadeNegocios)) {
            $stats = 'sem_user';
        } else {
            $usuariosUnidadeNegocio = $this->unidade_negocio_model->get_usuarios_unidade_negocio($unidadeNegocios, $cnpj_raiz_logado);
        }

        $this->ctr_resposta_model->set_state('filter.partnumber', $partnumber);
        $this->ctr_resposta_model->set_state('filter.estabelecimento', $estabelecimento);

        $historicoItem = $this->ctr_resposta_model->getHistoricoItem();
        $data = array(
            'unidadeNegocio' => $this->input->post('unidadeNegocio'),
            'partNumber' => $this->input->post('partNumber'),
            'owner' => $this->input->post('owner'),
            'assunto' => $this->input->post('assunto'),
            'motivo' => $this->input->post('motivo'),
            'descricao' => $this->input->post('itemDescricao'),
            'usuario' => $userData->nome,
            'historicoItem' => $historicoItem,
            'status' => 'Revisar Informações ERP',
            'base_url' => config_item('online_url') . '/'
        );

        $body = $this->load->view('templates/notificacao_revisar_infos_erp', $data, true);


        if (!empty($usuariosUnidadeNegocio)) {
            $email_usuarios = array();
            foreach ($usuariosUnidadeNegocio as $usuario) {
                $email_usuarios[] = $usuario->email;
            }
            if (count($email_usuarios) > 0) {
                $this->load->library("Item/Status");
                $this->status->set_status("revisar_informacoes_erp");
                $this->status->update_item($partnumber, $estabelecimento);

                $this->load->library('email');
                $this->email->from(config_item('mail_from_addr'));
                $this->email->to($email_usuarios);
                $this->email->subject($this->input->post('assunto'));
                $this->email->message($body);
                $this->email->send();
            } else {
                $stats = 'sem_user';
            }
        } else {
            $stats = 'sem_user';
        }

        try {
            $this->load->model('log_notificacao_usuario_model');

            if (!empty($usuariosUnidadeNegocio)) {
                foreach ($usuariosUnidadeNegocio as $usuarioUnidadeNegocio) {

                    $log_data['part_number'] = $partnumber;
                    $log_data['estabelecimento'] = $estabelecimento;
                    $log_data['id_empresa'] = $empresa->id_empresa;
                    $log_data['id_usuario_notificado'] = $usuarioUnidadeNegocio->id_usuario;
                    $log_data['id_usuario_origem'] = $userData->id_usuario;
                    $log_data['status_anterior'] = 'Perguntas Respondidas';
                    $log_data['status_atual'] = 'Revisar Informações ERP';
                    $log_data['motivo'] = $this->input->post('motivo');
                    $log_data['modelo'] = 'revisar_informacoes_erp';
                    $log_data['data_notificacao'] = date('Y-m-d H:i:s');
                    $log_data['tipo_notificacao'] = 'email';

                    $this->log_notificacao_usuario_model->save($log_data);
                }
            }
        } catch (Exception $e) {
            echo $e->getMessage();
        }

        echo $stats;

        return true;
    }

    private function setar_wf_status_atributos($item, $item_return)
    {
        $this->load->model('comex_model');

        $is_importado = $this->comex_model->check_imported(
            $item['part_number'],
            $item_return->id_empresa,
            $item['estabelecimento']
        );

        $this->item_model->set_wf_status_atributos(
            $item['part_number'],
            $item_return->id_empresa,
            $item['estabelecimento'],
            $is_importado ? 2 : 1
        );
    }

    public function get_list_eventos()
    {
        try {
            $this->load->model([
                'cad_item_model',
            ]);

            $filter_status = true;
            $response = $this->cad_item_model->getPacoteEventos(true, $filter_status);

            if (!$response) {
                $response = [];
            }

            // Incluir option 'sem_evento' como primeiro item na lista de eventos, para que seja exibido primeiro
            // Com o value 'sem_evento' e o label 'Sem Evento/Pacote'
            array_unshift($response, (object)['evento' => 'sem_evento', 'label' => 'Sem Evento/Pacote']);

            $response = array_map(function ($evento) {
                return [
                    'value' => $evento->evento,
                    'label' => $evento->label ?? $evento->evento
                ];
            }, $response);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar eventos: ' . $e->getMessage()
                ]));
        }
    }

    public function ajax_get_status_classificacao_fiscal()
    {
        try {
            $this->load->model('empresa_model');
            $empresa = $this->empresa_model->get_entry(sess_user_company());
            $campos_adicionais = explode('|', $empresa->campos_adicionais);
            $hasOwner = in_array('owner', $campos_adicionais);
            $hasTriagemOwner = in_array('triagem_owner', $campos_adicionais);
            $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais);

            $options = [];

            if ($hasOwner && $hasTriagemOwner) {
                $options[] = [
                    'value' => 'aguardando_definicao_responsavel',
                    'label' => 'Aguardando Definição Responsável'
                ];
            }

            $options[] = [
                'value' => 'analise',
                'label' => 'Em Análise'
            ];
            $options[] = [
                'value' => 'pendente',
                'label' => 'Pendentes de Informações'
            ];

            if ($hasDescricaoGlobal) {
                $options[] = [
                    'value' => 'aguardando_descricao',
                    'label' => 'Aguardando Descrição'
                ];
            }

            $options[] = [
                'value' => 'perguntasRespondidas',
                'label' => 'Perguntas Respondidas'
            ];

            if ($hasOwner) {
                $options[] = [
                    'value' => 'revisar_informacoes_erp',
                    'label' => 'Revisar Informações ERP'
                ];
                $options[] = [
                    'value' => 'informacoes_erp_revisadas',
                    'label' => 'Informações ERP Revisadas'
                ];
                $options[] = [
                    'value' => 'revisar_informacoes_tecnicas',
                    'label' => 'Revisar Informações Técnicas'
                ];
                $options[] = [
                    'value' => 'perguntas_respondidas_novas',
                    'label' => 'Perguntas Respondidas (Novas)'
                ];
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($options));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar status: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_owners_by_empresa()
    {
        try {
            $this->load->model([
                'empresa_model',
            ]);

            $empresa = $this->empresa_model->get_entry(sess_user_company());

            $campos_adicionais = explode("|", $empresa->campos_adicionais);
            $hasOwner =  in_array('owner', $campos_adicionais);

            $owner_user = $this->item_model->get_user_owner_codes(sess_user_id());

            if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
                $response = $this->empresa_model->get_owners_by_empresa($empresa, $owner_user);
            } else {
                $response = $this->empresa_model->get_owners_by_empresa($empresa);
            }

            if (!$response) {
                $response = [];
            }

            $response = array_map(function ($owner) {
                return [
                    'codigo' => $owner->codigo,
                    'descricao' => $owner->descricao,
                    'nomes' => $owner->nomes ?? '',
                    'value' => $owner->codigo,
                    'label' => $owner->codigo . ' - ' . $owner->descricao . ' - ' . $owner->nomes
                ];
            }, $response);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar owners: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_prioridades_by_empresa()
    {
        try {
            $this->load->model([
                'empresa_prioridades_model',
            ]);

            $response = $this->empresa_prioridades_model->get_entry(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $response = array_map(function ($prioridade) {
                return [
                    'value' => $prioridade->id_prioridade,
                    'label' => $prioridade->nome
                ];
            }, $response);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar prioridades: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_sistemas_origens()
    {
        try {

            $response = $this->item_model->get_list_sistemas_origens(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $response = array_map(function ($sistema) {
                return [
                    'value' => $sistema,
                    'label' => $sistema
                ];
            }, $response);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar sistemas de origem: ' . $e->getMessage()
                ]));
        }
    }

    public function ajax_get_usuarios_filtro()
    {
        try {
            $this->load->model('usuario_model');

            $this->usuario_model->set_state('filter.id_empresa', sess_user_company());
            $this->usuario_model->set_state('order_by', array('u.id_perfil' => 'desc', 'nome' => 'asc'));
            $lista_usuarios_atribuidos = $this->usuario_model->get_entries_filtro_atribuido_para();
            $lista_usuarios_atribuidos_ativos = array_values(array_filter($lista_usuarios_atribuidos, function ($usuario) {
                return $usuario->status == 1;
            }));
            $lista_usuarios_atribuidos_inativos = array_values(array_filter($lista_usuarios_atribuidos, function ($usuario) {
                return $usuario->status == 0;
            }));

            $data = [
                'options' => [
                    [
                        'value' => 'todos',
                        'text' => 'Todos os responsáveis'
                    ],
                    [
                        'value' => sess_user_id(),
                        'text' => 'Meu usuário'
                    ],
                    [
                        'value' => 'sem_responsavel',
                        'text' => 'Sem responsável'
                    ],
                ],
                'usuarios_ativos' => [],
                'usuarios_inativos' => []
            ];

            $id_perfil = null;
            $current_group_ativos = null;
            $current_group_inativos = null;

            foreach ($lista_usuarios_atribuidos_ativos as $usuario) {
                if (empty($usuario->id_usuario) || $usuario->id_usuario == sess_user_id()) continue;
                if (customer_has_role('sysadmin', $usuario->id_usuario)) continue;
                if (customer_has_role('becomex_pmo', $usuario->id_usuario)) continue;

                if (
                    customer_has_role('cliente_pmo', $usuario->id_usuario) &&
                    (has_role('fiscal') || has_role('engenheiro'))
                ) {
                    $data['usuarios_ativos']['optgroups'][] = array(
                        'label' => 'Gerente de Projetos',
                        'options' => array(
                            array(
                                'value' => $usuario->id_usuario,
                                'text' => $usuario->nome . ' (' . $usuario->total_resp . ')',
                                'subtext' => $usuario->email
                            )
                        )
                    );
                } else {
                    if ($id_perfil == null || ($id_perfil !== $usuario->id_perfil)) {
                        if ($current_group_ativos !== null) {
                            $data['usuarios_ativos']['optgroups'][] = $current_group_ativos;
                        }

                        $current_group_ativos = array(
                            'label' => $usuario->perfil,
                            'options' => array()
                        );

                        $id_perfil = $usuario->id_perfil;
                    }

                    $current_group_ativos['options'][] = array(
                        'value' => $usuario->id_usuario,
                        'text' => $usuario->nome . ' (' . $usuario->total_resp . ')',
                        'subtext' => $usuario->email
                    );
                }
            }

            if ($current_group_ativos !== null) {
                $data['usuarios_ativos']['optgroups'][] = $current_group_ativos;
            }

            foreach ($lista_usuarios_atribuidos_inativos as $usuario) {
                if (empty($usuario->id_usuario) || $usuario->id_usuario == sess_user_id()) continue;
                if (customer_has_role('sysadmin', $usuario->id_usuario)) continue;
                if (customer_has_role('becomex_pmo', $usuario->id_usuario)) continue;

                if (
                    customer_has_role('cliente_pmo', $usuario->id_usuario) &&
                    (has_role('fiscal') || has_role('engenheiro'))
                ) {
                    $data['usuarios_inativos']['optgroups'][] = array(
                        'label' => 'Gerente de Projetos',
                        'options' => array(
                            array(
                                'value' => $usuario->id_usuario,
                                'text' => $usuario->nome . ' (' . $usuario->total_resp . ')',
                                'subtext' => $usuario->email
                            )
                        )
                    );
                } else {
                    if ($id_perfil == null || ($id_perfil !== $usuario->id_perfil)) {
                        if ($current_group_inativos !== null) {
                            $data['usuarios_inativos']['optgroups'][] = $current_group_inativos;
                        }

                        $current_group_inativos = array(
                            'label' => $usuario->perfil,
                            'options' => array()
                        );

                        $id_perfil = $usuario->id_perfil;
                    }

                    $current_group_inativos['options'][] = array(
                        'value' => $usuario->id_usuario,
                        'text' => $usuario->nome . ' (' . $usuario->total_resp . ')',
                        'subtext' => $usuario->email
                    );
                }
            }

            if ($current_group_inativos !== null) {
                $data['usuarios_inativos']['optgroups'][] = $current_group_inativos;
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => true,
                    'data' => $data
                ]));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'error' => true,
                    'message' => 'Erro ao carregar usuários: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Aplica filtros padrão usando a Filter_Manager library
     * @param array|null $post Dados do POST
     * @return array
     */
    private function apply_default_filters($post = null)
    {
        // Configurar e aplicar filtros usando a library
        $config = Filter_Manager::get_dados_tecnicos_config();

        $result = $this->filter_manager
            ->initialize($this->item_model)
            ->configure($config)
            ->apply_default_filters($post);

        // Definir empresa sempre
        $this->item_model->set_state('filter.id_empresa', sess_user_company());

        return $result;
    }

    /**
     * Exemplo de como usar a Filter_Manager library em outras telas
     * Este método demonstra como configurar filtros específicos para diferentes contextos
     *
     * @param array|null $post Dados do POST
     * @return array
     */
    private function exemplo_filtros_customizados($post = null)
    {
        // Configuração customizada para uma tela específica
        $config_customizada = [
            'boolean' => [
                'triagem_diana_falha' => ['is_checkbox_checked'],
                'item_ativo' => ['is_checkbox_checked']  // Novo filtro boolean
            ],
            'array' => [
                'status' => ['is_not_empty'],
                'prioridade' => ['is_array', 'not_contains_negative_one'],
                'categoria' => ['is_array', 'not_contains_negative_one']  // Novo filtro array
            ],
            'simple' => [
                'item_input' => ['is_not_empty'],
                'codigo_produto' => ['is_not_empty']  // Novo filtro simples
            ],
            'custom' => [
                // Filtro customizado com callback
                'data_range' => function ($model, $input, $post) {
                    $data_inicio = $input->post('data_inicio');
                    $data_fim = $input->post('data_fim');

                    if (!empty($data_inicio) && !empty($data_fim)) {
                        $model->set_state('filter.data_inicio', $data_inicio);
                        $model->set_state('filter.data_fim', $data_fim);
                    } else {
                        $model->unset_state('filter.data_inicio');
                        $model->unset_state('filter.data_fim');
                    }
                }
            ]
        ];

        // Aplicar filtros customizados
        $result = $this->filter_manager
            ->initialize($this->item_model)
            ->configure($config_customizada)
            ->apply_default_filters($post);

        return $result;
    }

    /**
     * Carrega dados de SLA para os itens
     * @param array $items Lista de itens
     * @return array Dados de SLA
     */
    private function load_sla_data($items)
    {
        if (empty($items)) {
            return [];
        }

        // Adicionar dados de SLA diretamente aos objetos dos itens
        foreach ($items as &$item) {
            $sla_data = $this->item_model->calculate_sla_data(
                $item->part_number,
                $item->id_empresa,
                $item->estabelecimento
            );

            // Adicionar os campos de SLA ao objeto do item
            $item->horas_sla_prioridade = $sla_data['horas_sla_prioridade'];
            $item->tempo_consumido_becomex = $sla_data['tempo_consumido_becomex'];
            $item->tempo_ultimo_status_becomex = $sla_data['tempo_ultimo_status_becomex'];
            $item->tempo_restante = $sla_data['tempo_restante'];
        }

        return [];
    }

    /**
     * Aplica filtro de Farol SLA nos itens já carregados
     * Suporta múltipla seleção de cores do farol
     * @param array $itens Lista de itens com dados de SLA
     * @return array Itens filtrados
     */
    private function apply_farol_sla_filter($itens)
    {
        $farol_sla = $this->item_model->get_state('filter.farol_sla');

        // Verificar se o filtro está vazio ou é inválido
        if (empty($farol_sla) || $farol_sla === '-1' || empty($itens)) {
            return $itens;
        }

        // Garantir que $farol_sla seja sempre um array para suportar múltipla seleção
        if (!is_array($farol_sla)) {
            $farol_sla = [$farol_sla];
        }

        // Remover valores inválidos do array
        $farol_sla = array_filter($farol_sla, function ($valor) {
            return !empty($valor) && $valor !== '-1';
        });

        // Se após a limpeza não há valores válidos, retornar todos os itens
        if (empty($farol_sla)) {
            return $itens;
        }

        $itens_filtrados = [];

        foreach ($itens as $item) {
            $horas_sla_prioridade = $item->horas_sla_prioridade ?? 1;
            $tempo_consumido_total = ($item->tempo_consumido_becomex ?? 0) + ($item->tempo_ultimo_status_becomex ?? 0);

            // Evitar divisão por zero
            if ($horas_sla_prioridade == 0) {
                $horas_sla_prioridade = 1;
            }

            $percentual_consumido = ($tempo_consumido_total / $horas_sla_prioridade) * 100;

            // Determinar a cor do farol para este item
            $cor_farol_item = $this->determinar_cor_farol($percentual_consumido);

            // Verificar se a cor do farol do item está entre as cores selecionadas
            if (in_array($cor_farol_item, $farol_sla)) {
                $itens_filtrados[] = $item;
            }
        }

        return $itens_filtrados;
    }

    /**
     * Determina a cor do farol SLA baseado no percentual consumido
     * @param float $percentual_consumido Percentual de tempo consumido
     * @return string Cor do farol ('verde', 'amarelo', 'vermelho')
     */
    private function determinar_cor_farol($percentual_consumido)
    {
        if ($percentual_consumido <= 75) {
            return 'verde';
        } elseif ($percentual_consumido < 100) {
            return 'amarelo';
        } else {
            return 'vermelho';
        }
    }

    /**
     * Verifica se há um filtro de farol SLA válido aplicado
     * @param mixed $farol_sla Valor do filtro farol_sla
     * @return bool True se há filtro válido, false caso contrário
     */
    private function has_valid_farol_sla_filter($farol_sla)
    {
        if (empty($farol_sla) || $farol_sla === '-1') {
            return false;
        }

        if (is_array($farol_sla)) {
            // Remover valores inválidos e verificar se sobra algo
            $valores_validos = array_filter($farol_sla, function ($valor) {
                return !empty($valor) && $valor !== '-1';
            });
            return !empty($valores_validos);
        }

        return true;
    }



    /**
     * Método genérico para retornar opções de filtros simples
     * Útil para filtros que não precisam consultar o banco de dados
     *
     * Como usar:
     * - Para opções fixas: definir no switch case
     * - Para opções dinâmicas: consultar model e formatar
     * - URL: /atribuir_grupo/get_simple_filter_options?type=nome_do_filtro
     *
     * Exemplo de extensão para filtro dinâmico:
     * case 'meu_filtro':
     *     $dados = $this->meu_model->get_dados(sess_user_company());
     *     $options = array_map(function($item) {
     *         return ['value' => $item->id, 'label' => $item->nome];
     *     }, $dados);
     *     break;
     */
    public function get_simple_filter_options()
    {
        try {
            $filter_type = $this->input->get('type');

            $options = [];

            switch ($filter_type) {
                case 'novo_material':
                    $options = [
                        ['value' => 'S', 'label' => 'SIM'],
                        ['value' => 'N', 'label' => 'NÃO']
                    ];
                    break;

                case 'importado':
                    $options = [
                        ['value' => 'S', 'label' => 'SIM'],
                        ['value' => 'N', 'label' => 'NÃO']
                    ];
                    break;

                case 'farol_sla':
                    $options = [
                        ['value' => 'verde', 'label' => 'Verde'],
                        ['value' => 'amarelo', 'label' => 'Amarelo'],
                        ['value' => 'vermelho', 'label' => 'Vermelho']
                    ];
                    break;

                // Exemplo de filtro que consulta o banco
                case 'status_classificacao_fiscal':
                    // Este filtro já tem seu próprio método, mas poderia ser movido aqui
                    // $status_list = $this->item_model->get_status_classificacao_fiscal();
                    // $options = array_map(function($status) {
                    //     return ['value' => $status->id, 'label' => $status->nome];
                    // }, $status_list);
                    $options = [
                        ['value' => '-1', 'label' => 'Todos'],
                        ['value' => '1', 'label' => 'Pendente'],
                        ['value' => '2', 'label' => 'Em análise'],
                        ['value' => '3', 'label' => 'Aprovado']
                    ];
                    break;

                case 'estabelecimento':
                    // Exemplo: Para filtros que precisam consultar o banco,
                    // você pode chamar um método do model aqui
                    $estabelecimentos = $this->empresa_model->get_estabelecimentos(sess_user_company());
                    $options = array_merge($options, array_map(function ($estab) {
                        return ['value' => $estab, 'label' => $estab];
                    }, $estabelecimentos));

                    // $options = [
                    //     ['value' => '-1', 'label' => 'Todos'],
                    //     ['value' => '001', 'label' => '001'],
                    //     ['value' => '002', 'label' => '002'],
                    //     ['value' => '003', 'label' => '003']
                    // ];
                    break;

                default:
                    $options = [['value' => '-1', 'label' => 'Selecione...']];
                    break;
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($options));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar opções do filtro: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Método de exportação XLS otimizado para a nova tela (index_new)
     * Usa apply_default_filters para reutilizar a lógica de filtros
     */
    public function log_xls_new()
    {
        error_reporting(0);
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        try {
            // Aplicar filtros usando a mesma lógica da pesquisa
            $this->apply_default_filters($this->input->get());

            $id_empresa = sess_user_company();
            $this->load->model('empresa_model');
            $empresa = $this->empresa_model->get_entry($id_empresa);

            $campos_adicionais = explode('|', $empresa->campos_adicionais);
            $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais);
            $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $campos_adicionais);

            // Buscar dados usando o model já configurado com filtros
            $logs = $this->item_model->get_entries_for_xls($id_empresa);

            $this->load->library('Excel');
            $this->excel->setActiveSheetIndex(0);
            $this->excel->getActiveSheet()->setTitle('Log - Atribuição de grupos');

            // Definir cabeçalhos
            $this->_setExcelHeaders($hasDescricaoGlobal, $hasPnPrimarioSecundario);

            if (!empty($logs)) {
                $this->_fillExcelData($logs, $hasDescricaoGlobal, $hasPnPrimarioSecundario);
            }

            $filename = 'exportacao_dados_tecnicos_' . date('Y-m-d_H-i-s') . '.xlsx';

            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
            $objWriter->save('php://output');
        } catch (Exception $e) {
            log_message('error', 'Erro na exportação XLS: ' . $e->getMessage());
            show_error('Erro ao gerar arquivo de exportação.', 500);
        }
    }

    /**
     * Método de exportação XLS Multi Países otimizado para a nova tela
     * Segue a mesma lógica do método original xls_multipaises()
     */
    public function xls_multipaises_new()
    {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        try {
            // Aplicar filtros usando a mesma lógica da pesquisa
            $this->apply_default_filters($this->input->get());

            $id_empresa = sess_user_company();

            // Carregar models necessários (igual ao método original)
            $this->load->model(array(
                'item_pais_model'
            ));

            // Buscar itens e dados de países (igual ao método original)
            $itens = $this->item_model->get_entries_for_xls($id_empresa);
            $dados_paises = $this->item_pais_model->get_entries_for_xls($id_empresa);

            // Definir estrutura de colunas (baseada no método original)
            $headerRow = array(
                array(
                    'label' => 'PART NUMBER BRASIL',
                    'field' => 'part_number',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'ESTABELECIMENTO',
                    'field' => 'estabelecimento',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'DESCRIÇÃO',
                    'field' => 'descricao',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'SIGLA DO PAÍS',
                    'field' => 'sigla',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'CÓDIGO DO PAÍS',
                    'field' => 'codigo',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'NOME DO PAÍS',
                    'field' => 'nome',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'CÓDIGO CLASSIFICAÇÃO',
                    'field' => 'codigo_classificacao',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'DESCRIÇÃO CURTA',
                    'field' => 'descricao_curta',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'DESCRIÇÃO COMPLETA',
                    'field' => 'descricao_completa',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'L.I.',
                    'field' => 'li',
                    'col_format' => 'texto',
                    'col_width' => 30
                ),
                array(
                    'label' => 'INFORMAÇÃO ADICIONAL',
                    'field' => 'informacao_adicional',
                    'col_format' => 'texto',
                    'col_width' => 30
                )
            );

            $cabecalho = $this->extract_fields_from_cols($headerRow);

            $config = array(
                'filename'      => "paises_dados_tecnicos_" . date('Y-m-d_H-i-s'),
                'titulo'        => "Paises Dados Técnicos",
                'nome_planilha' => "Paises Dados Técnicos",
                'colunas'       => $headerRow,
                'cabecalho'     => $cabecalho,
                'filter_suffix' => "HOM_"
            );

            require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';
            $relatorio = new XlsxGenerator($config['filename']);
            $writer = $relatorio->init($config['filename']);

            $relatorio->filename = $config['filename'];

            // Estrutura XLSX
            $widths = $relatorio->getWidth($config);
            $fields = array();

            $writer->addImage(FCPATH . 'assets/img/header/logo.png', 1, array('widths' => $widths));

            if (isset($config['colunas'])) {
                foreach ($config['colunas'] as $coluna) {
                    if (isset($coluna['label']))
                        $fields[$coluna['label']] = isset($coluna['col_format']) ? $coluna['col_format'] : 'string';
                }
            }

            $headerStyle = array(
                'font' => 'Arial',
                'font-size' => 12,
                'font-style' => 'bold',
                'color' => '#000000',
                'fill' => '#F9FF00',
                'halign' => 'center',
                'valign' => 'center',
                'border' => 'bottom',
                'wrap_text' => 'true',
                'widths' => $widths
            );

            $writer->writeSheetHeader($config['nome_planilha'], $fields, $headerStyle);

            if (isset($config['colunas']) && (count($config['colunas']) <= 24) && (count($config['colunas']) >= 10)) {
                $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
            } else if (isset($config['colunas']) && count($config['colunas']) <= 24) {
                $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, 24);
            } else {
                $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) - 1);
            }

            $defaultStyle = array(
                'font' => 'Arial',
                'font-size' => 11
            );

            // Processar dados combinando itens com dados de países (lógica do método original)
            $this->_processMultipaisesData($itens, $dados_paises, $headerRow, $writer, $config, $defaultStyle);

            return $relatorio->download();
        } catch (Exception $e) {
            log_message('error', 'Erro na exportação XLS Multi Países: ' . $e->getMessage());
            show_error('Erro ao gerar arquivo de exportação.', 500);
        }
    }

    /**
     * Status para exportação padrão da nova tela
     */
    public function ajax_xls_log_status_new()
    {
        // Método obrigatório para controle de download
        echo json_encode(['success' => true]);
    }

    /**
     * Status para exportação multipaises da nova tela
     */
    public function ajax_xls_log_status_multipaises_new()
    {
        // Método obrigatório para controle de download
        echo json_encode(['success' => true]);
    }

    /**
     * Método auxiliar para definir cabeçalhos do Excel
     */
    private function _setExcelHeaders($hasDescricaoGlobal, $hasPnPrimarioSecundario)
    {
        $headers = [
            'A1' => 'Part number',
            'B1' => 'Descrição do item',
            'C1' => 'Data de Criação',
            'D1' => 'Data de Modificação',
            'E1' => 'Motivo',
            'F1' => 'Estabelecimento',
            'G1' => 'Part number similar',
            'H1' => 'NCM',
            'I1' => 'NCM Fornecedor',
            'J1' => 'Evento',
            'K1' => 'TAG',
            'L1' => 'Status',
            'M1' => 'Descrição completa',
            'N1' => 'Função',
            'O1' => 'Aplicação',
            'P1' => 'Marca',
            'Q1' => 'Material constitutivo',
            'R1' => 'Memória de classificação',
            'S1' => 'Informações adicionais',
            'T1' => 'Observações',
            'U1' => 'Peso',
            'V1' => 'Prioridade',
            'W1' => 'Perguntas & Respostas',
            'X1' => 'Status do item',
            'Y1' => 'Owner',
            'Z1' => 'Novo Material',
            'AA1' => 'Responsável Fiscal',
            'AB1' => 'Responsável Engenharia',
            'AC1' => 'Responsável Perguntas'
        ];

        $nextColumn = 'AD';
        if ($hasDescricaoGlobal) {
            $headers[$nextColumn++ . '1'] = 'Descrição global';
        }

        $headers[$nextColumn++ . '1'] = 'Indicador (COMEX)';
        $headers[$nextColumn++ . '1'] = 'DI (COMEX)';
        $headers[$nextColumn++ . '1'] = 'Data da Última DI (COMEX)';
        $headers[$nextColumn++ . '1'] = 'Drawback (COMEX)';
        $headers[$nextColumn++ . '1'] = 'NCM (COMEX)';

        if ($hasPnPrimarioSecundario) {
            $headers[$nextColumn++ . '1'] = 'PN Secundário';
        }

        foreach ($headers as $cell => $value) {
            $this->excel->getActiveSheet()->setCellValue($cell, $value);
        }

        // Aplicar formatação
        $lastColumn = $this->_getColumnFromIndex(count($headers) - 1);
        $this->excel->getActiveSheet()->getStyle('A1:' . $lastColumn . '1')->getFont()->setBold(true);
        $this->excel->getActiveSheet()->getStyle('A1:' . $lastColumn . '1')->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');

        // Auto dimensionar colunas
        foreach (range('A', $lastColumn) as $columnID) {
            $this->excel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
        }
    }

    /**
     * Método auxiliar para preencher dados no Excel
     */
    private function _fillExcelData($logs, $hasDescricaoGlobal, $hasPnPrimarioSecundario)
    {
        $key = 0;
        $horizontal_left = [
            'alignment' => [
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
            ],
        ];

        while ($log = $logs->unbuffered_row()) {
            $key++;
            $i = $key + 1;

            // Processar perguntas e respostas
            $perguntasRespostas = $this->_getPerguntasRespostas($log->part_number, $log->estabelecimento);

            // Processar campos
            $log->descricao = html_entity_decode($log->descricao, ENT_QUOTES);
            $log->motivo = strip_tags(str_replace('<br>', "\n", $log->motivo));
            $log->motivo = html_entity_decode($log->motivo, ENT_QUOTES);

            $dat_criacao = !empty($log->dat_criacao) ? date("d/m/Y", strtotime($log->dat_criacao)) : "";
            $data_modificacao = !empty($log->data_modificacao) ? date("d/m/Y", strtotime($log->data_modificacao)) : "";
            $data_di = $log->data_di ? date('d/m/Y', strtotime($log->data_di)) : null;

            $status_map = [
                '1' => 'Pendente de Homologação',
                '2' => 'Homologado',
                '3' => 'Reprovado',
                '4' => 'Inativo',
                '5' => 'Em Revisão',
                '6' => 'Em Análise',
                '7' => 'Pendente de Informações',
                '8' => 'Perguntas Respondidas',
                '9' => 'Revisar Informações ERP',
                '10' => 'Homologado em Revisão',
                '11' => 'Revisar Informações Técnicas',
                '12' => 'Informações ERP Revisadas',
                '13' => 'Aguardando definição responsável',
                '14' => 'Aguardando Descrição',
                '15' => 'Perguntas Respondidas (Novas)'
            ];

            $status_fiscal = isset($status_map[$log->id_status]) ? $status_map[$log->id_status] : '';

            // Preencher dados básicos
            $this->_fillBasicExcelRow($i, $log, $dat_criacao, $data_modificacao, $status_fiscal, $perguntasRespostas['texto']);

            // Preencher dados específicos
            $this->_fillSpecificExcelRow($i, $log, $hasDescricaoGlobal, $hasPnPrimarioSecundario, $data_di);
        }

        // Aplicar formatação
        if ($key > 0) {
            $this->excel->getActiveSheet()->getStyle('A1:A' . ($key + 1))->applyFromArray($horizontal_left);
        }
    }

    /**
     * Método auxiliar para obter perguntas e respostas
     */
    private function _getPerguntasRespostas($part_number, $estabelecimento)
    {
        $perguntasRespostas = "";
        $responsavelPergunta = "";

        $this->ctr_resposta_model->set_state('filter.partnumber', $part_number);
        $this->ctr_resposta_model->set_state('filter.estabelecimento', $estabelecimento);

        $historicoItem = $this->ctr_resposta_model->getHistoricoItem();

        if (!empty($historicoItem)) {
            $id_responsavel_pergunta = [];

            foreach ($historicoItem as $k => $historico) {
                $perguntasRespostas .= ($k + 1) . " - {$historico->pergunta}\n";
                $perguntasRespostas .= "R: " . (!empty($historico->resposta) ? "$historico->resposta \n" : " \n");

                if (!empty($historico->id_responsavel_pergunta) && !in_array($historico->id_responsavel_pergunta, $id_responsavel_pergunta)) {
                    $id_responsavel_pergunta[] = $historico->id_responsavel_pergunta;
                    $responsavelPergunta .= $historico->nome_responsavel_pergunta . " - " . $historico->email_responsavel_pergunta . "; \n";
                }
            }
        }

        return [
            'texto' => $perguntasRespostas,
            'responsavel' => $responsavelPergunta
        ];
    }

    /**
     * Preencher linha básica do Excel
     */
    private function _fillBasicExcelRow($i, $log, $dat_criacao, $data_modificacao, $status_fiscal, $perguntasRespostas)
    {
        $nomeResponsavelFiscal = $log->resp_fiscal_nome ?: 'Usuário Não Encontrado';
        $emailResponsavelFiscal = $log->resp_fiscal_email ?: '';
        $nomeResponsavelEngenharia = $log->resp_engenharia_nome ?: 'Usuário Não Encontrado';
        $emailResponsavelEngenharia = $log->resp_engenharia_email ?: '';

        $cells = [
            'A' => $log->part_number,
            'B' => $log->descricao,
            'C' => $dat_criacao,
            'D' => $data_modificacao,
            'E' => $log->motivo,
            'F' => $log->estabelecimento,
            'G' => $log->part_number_similar,
            'H' => $log->ncm,
            'I' => $log->ncm_fornecedor,
            'J' => $log->evento,
            'K' => $log->tag,
            'L' => $log->status,
            'M' => $log->descricao_proposta_completa,
            'N' => $log->funcao,
            'O' => $log->aplicacao,
            'P' => $log->marca,
            'Q' => $log->material_constitutivo,
            'R' => $log->memoria_classificacao,
            'S' => $log->inf_adicionais,
            'T' => $log->observacoes,
            'U' => $log->peso,
            'V' => $log->empresa_prioridade,
            'W' => $perguntasRespostas,
            'X' => $status_fiscal,
            'Y' => $log->owner_codigo . ' - ' . $log->owner_descricao . ' - ' . $log->responsaveis_gestores_nomes,
            'Z' => isset($log->integracao_novo_material) ? (($log->integracao_novo_material == 'S') ? 'Sim' : 'Não') : null,
            'AA' => $nomeResponsavelFiscal . " - " . $emailResponsavelFiscal,
            'AB' => $nomeResponsavelEngenharia . " - " . $emailResponsavelEngenharia,
            'AC' => '' // Responsável Perguntas - será preenchido se necessário
        ];

        foreach ($cells as $column => $value) {
            $this->excel->getActiveSheet()->setCellValueExplicit($column . $i, $value, PHPExcel_Cell_DataType::TYPE_STRING);
        }

        // Aplicar quebra de texto onde necessário
        $this->excel->getActiveSheet()->getStyle('W' . $i)->getAlignment()->setWrapText(true);
    }

    /**
     * Preencher dados específicos do Excel
     */
    private function _fillSpecificExcelRow($i, $log, $hasDescricaoGlobal, $hasPnPrimarioSecundario, $data_di)
    {
        $nextColumn = 'AD';

        if ($hasDescricaoGlobal) {
            $this->excel->getActiveSheet()->setCellValueExplicit($nextColumn++ . $i, $log->descricao_global, PHPExcel_Cell_DataType::TYPE_STRING);
        }

        $this->excel->getActiveSheet()->setCellValueExplicit($nextColumn++ . $i, $log->indicador_ecomex, PHPExcel_Cell_DataType::TYPE_STRING);
        $this->excel->getActiveSheet()->setCellValueExplicit($nextColumn++ . $i, $log->num_di, PHPExcel_Cell_DataType::TYPE_STRING);
        $this->excel->getActiveSheet()->setCellValueExplicit($nextColumn++ . $i, $data_di, PHPExcel_Cell_DataType::TYPE_STRING);
        $this->excel->getActiveSheet()->setCellValueExplicit($nextColumn++ . $i, isset($log->ind_drawback) ? (($log->ind_drawback == 1 || $log->ind_drawback == 'S') ? 'S' : 'N') : null, PHPExcel_Cell_DataType::TYPE_STRING);
        $this->excel->getActiveSheet()->setCellValueExplicit($nextColumn++ . $i, $log->ncm_ecomex, PHPExcel_Cell_DataType::TYPE_STRING);

        if ($hasPnPrimarioSecundario) {
            $this->excel->getActiveSheet()->setCellValueExplicit($nextColumn++ . $i, $log->pn_secundario_ipn, PHPExcel_Cell_DataType::TYPE_STRING);
        }
    }

    /**
     * Processar dados combinando itens com dados de países
     * Implementa a mesma lógica do método original xls_multipaises()
     */
    private function _processMultipaisesData($itens, $dados_paises, $headerRow, $writer, $config, $defaultStyle)
    {
        $itemRows = array();
        $dados_paises_buffer = $dados_paises->result();

        $k = 0;
        $key = 0;

        // Loop principal igual ao método original
        while ($item = $itens->unbuffered_row()) {
            $tem_paises = 0;

            // Para cada item, verificar se há dados de países correspondentes
            foreach ($dados_paises_buffer as $dado_pais) {
                if ($item->part_number == $dado_pais->part_number) {
                    $key++;
                    $k = $key + 1;

                    $itemRows[$k] = array(
                        $dado_pais->part_number,
                        $dado_pais->estabelecimento,
                        $item->descricao,
                        $dado_pais->sigla,
                        $dado_pais->codigo,
                        $dado_pais->nome,
                        $dado_pais->codigo_classificacao,
                        $dado_pais->descricao_curta,
                        $dado_pais->descricao_completa,
                        $dado_pais->li,
                        $dado_pais->informacao_adicional
                    );
                    $tem_paises++;
                }
            }

            // Se não tem países, adicionar linha com dados vazios (igual ao original)
            if ($tem_paises == 0) {
                $key++;
                $k = $key + 1;
                $itemRows[$k] = array(
                    $item->part_number,
                    $item->estabelecimento,
                    $item->descricao,
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - ',
                    ' - '
                );
            }
        }

        // Escrever dados no Excel (igual ao método original)
        $k = 2;
        foreach ($itemRows as $item) {
            $row = array();
            foreach ($headerRow as $i => $value) {
                $row += array($headerRow[$i]['field'] => $itemRows[$k][$i]);
            }
            $k++;
            $writer->writeSheetRow($config['nome_planilha'], $row, $defaultStyle);
        }
    }

    /**
     * Obter letra da coluna a partir do índice
     */
    private function _getColumnFromIndex($index)
    {
        $columns = range('A', 'Z');
        if ($index < 26) {
            return $columns[$index];
        }

        // Para colunas AA, AB, etc.
        $first = intval($index / 26) - 1;
        $second = $index % 26;
        return $columns[$first] . $columns[$second];
    }
}
