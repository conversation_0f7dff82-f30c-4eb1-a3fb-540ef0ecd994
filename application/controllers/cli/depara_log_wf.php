<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Depara_log_wf extends CI_Controller {

    private $status_wf_atributos_table = [
        1 => 'Item nacional',
        2 => 'Análise de atributos - Fiscal',
        3 => 'Preenchimento/Validação Engenharia',
        4 => 'Homologação da Classificação Fiscal',
        5 => 'Em revisão',
        6 => 'Em revisão por alteração no PUCOMEX',
        7 => 'Homologados'
    ];

    public function __construct()
    {
        parent::__construct();
    }

   private function get_status_id_by_name($status_name)
    {
        // Usa a função array_search para buscar a chave (ID) pelo valor (Nome do Status)
        $id = array_search($status_name, $this->status_wf_atributos_table);
        
        // Retorna o ID (inteiro) ou NULL se a busca não encontrar o nome.
        return ($id !== FALSE) ? $id : NULL;
    }

    public function get_entries()
    {
        $this->db->where('ativo', '1');
        $query = $this->db->get('empresa');
        return $query->result();
    }

    /**
     * Função principal: Busca todos os PNs criados na data alvo e processa cada um.
     */
    public function analisar()
    {

        $empresas = $this->get_entries();

        $data_alvo_inicio = '2025-09-26 00:00:00';
        $data_alvo_fim = '2025-09-26 23:59:59';
        $status_alvo = 'Em revisão por alteração no PUCOMEX';


        foreach ($empresas as $empresa)
        {
            
            // 1. QUERY INICIAL: Busca todos os part_numbers distintos criados na data alvo
            $this->db->select('DISTINCT part_number, id_empresa, estabelecimento', FALSE); // <--- LINHA CORRIGIDA
            $this->db->from('log_wf_atributos');
            $this->db->where('criado_em >=', $data_alvo_inicio);
            $this->db->where('criado_em <=', $data_alvo_fim);
            $this->db->where('id_empresa', $empresa->id_empresa);
            
            
            $query_pns = $this->db->get();
            $itens = $query_pns->result_array();
    
            if (empty($itens))
            {
                log_message('info', "Nenhum part_number encontrado com 'criado_em' em 2025-09-26.");
                echo "Nenhum part_number encontrado para processar.";
                return;
            }
    
            echo "Total de Part Numbers encontrados na data: **" . count($itens) . "**<hr>";
    
            // 2. Itera sobre cada part_number encontrado
            foreach ($itens as $pn_item)
            {
                $part_number = $pn_item['part_number'];
                $id_empresa = $pn_item['id_empresa'];
                $estabelecimento = $pn_item['estabelecimento'];
                echo "<h2>Analisando Item: " . $part_number . " (Empresa: " . $id_empresa . " / Estab.: " . $estabelecimento . ")</h2>";
    
                if (empty($id_empresa))
                {
                    continue;
                }
    
                if (empty($part_number))
                {
                    continue;
                }
                // Chama a função que aplica a lógica de análise original para cada PN
                $this->analisar_log_do_item($part_number, $id_empresa, $estabelecimento, $status_alvo, $data_alvo_inicio);
    
    
                echo "<br>";
            }
    
            echo "<hr>Processamento concluído.";
        }
    }

    /**
     * Lógica de análise de registros mais recentes para um PN específico.
     * Esta função é reutilizada do script anterior, adaptada.
     */
    private function analisar_log_do_item($part_number, $id_empresa, $estabelecimento, $status_alvo, $data_alvo_inicio)
    {
        // 1. QUERY: Busca os dois registros mais recentes para o PN atual
        $this->db->select('status_atual, criado_em');
        $this->db->from('log_wf_atributos');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->order_by('criado_em', 'DESC');
        $this->db->limit(2);
        
        $query = $this->db->get();
        $registros = $query->result_array();

        if (empty($registros))
        {
            // Isso não deve acontecer se a query inicial funcionou, mas é um bom safe-check.
            echo "Erro: Nenhum registro de log encontrado para o PN: " . $part_number . "<br>";
            return; 
        }

        $registro_recente = $registros[0];
        
        // Extrai a data do registro
        $data_registro = substr($registro_recente['criado_em'], 0, 10);
        $data_alvo_curta = substr($data_alvo_inicio, 0, 10);

        // 2. LÓGICA: Verifica as condições do registro mais recente
        $condicao_status = ($registro_recente['status_atual'] === $status_alvo);
        $condicao_data = ($data_registro === $data_alvo_curta);

        echo "Status mais recente: **" . $registro_recente['status_atual'] . "** | Criado em: **" . $data_registro . "**<br>";
        
        if ($condicao_status && $condicao_data)
        {
            // O registro mais recente atende aos critérios
            
            // 3. Verifica se existe um registro anterior
            if (count($registros) >= 2)
            {
                $registro_anterior = $registros[1];
                $status_anterior = $registro_anterior['status_atual'];
                
                echo "Condição ATENDIDA. Status Anterior: " . $status_anterior . "<br>";
                
                // 4. Chama a função de tratamento
                $this->funcaoComStatusAnterior($status_anterior, $part_number, $id_empresa, $estabelecimento);

            }
            else
            {
                // 4. Chama a função de tratamento sem status anterior
                echo "Condição ATENDIDA. Não há registro anterior (primeiro registro).<br>";
                $this->funcaoSemStatusAnterior($part_number, $id_empresa, $estabelecimento);

            }
        }
        else
        {
            // O registro mais recente não atende aos critérios
            echo "O registro mais recente **NÃO** atende aos critérios (Status ou Data). Próximo PN.<br>";
        }
    }


    // --- Funções de Tratamento Solicitadas (Atualizadas para receber o PN) ---

    private function funcaoComStatusAnterior($status, $part_number, $id_empresa, $estabelecimento)
    {
        
        $status_id = $this->get_status_id_by_name($status);

 
        echo "<span style='color: green;'>-> AÇÃO 1 (Com Status Anterior): Status: **" . $status . "** (ID: **" . $status_id . "**)</span><br>";

        $update_data = array(
            'wf_status_atributos' => $status_id
        );
 
        $this->db->where('part_number', $part_number);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('wf_status_atributos', 6); 
        
        $this->db->update('item', $update_data);

    }

    private function funcaoSemStatusAnterior($part_number, $id_empresa, $estabelecimento)
    {
 
         echo "<span style='color: orange;'>-> AÇÃO 2 (Sem Status Anterior): Primeiro registro do PN.</span><br>";

         
        $update_data = array(
            'wf_status_atributos' => 7
        );
 
        $this->db->where('part_number', $part_number);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('wf_status_atributos', 6);  
        
        $this->db->update('item', $update_data);
    }
}
// Fim do arquivo Partnumber_processador.php