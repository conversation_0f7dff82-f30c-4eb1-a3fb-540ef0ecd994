<?php

class Migration_Add_Permissao_Revisao_Sla extends CI_Migration
{
    public function up()
    {
        $this->db->where('slug', 'visualizar_bi');
        $g_query = $this->db->get('permissao', 1);

        if (!$g_query->num_rows()) {
            $this->db->insert('permissao', array(
                'descricao' => 'Visualizar Bi',
                'slug'      => 'visualizar_bi'
            ));
        }
    }

    public function down()
    {
        $this->db->where_in('slug', 'visualizar_bi');
        $query = $this->db->get('permissao');

        $id_permissao = array();

        foreach ($query->result() as $row) {
            $id_permissao[] = $row->id_permissao;
        }
        if (count($id_permissao)) {
            $this->db->where_in('id_permissao', $id_permissao);
            $this->db->delete('permissao');
        }
    }
}