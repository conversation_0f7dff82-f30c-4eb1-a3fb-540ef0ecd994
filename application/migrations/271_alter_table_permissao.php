<?php

if (!defined('BASEPATH'))
{
    exit('No direct script access allowed');
}

class Migration_Alter_Table_Permissao extends CI_Migration
{
    private $_table = "permissao";
    private $_table_user = "usuario";

    public function up()
    {

        if (!$this->db->field_exists('permissao_especial', $this->_table)) {

            $fields = [
                'permissao_especial' => [
                    'type' => 'INT',
                    'constraint' => 1,
                    'default' => 0,
                    'null' => FALSE
                ]
            ];

            $this->dbforge->add_column($this->_table, $fields);
        }

        $this->db->where('slug', 'alterar_criticidade');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Alterar Criticidade',
                'slug'      => 'alterar_criticidade',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'alterar_owner');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Triagem/Alterar Owner',
                'slug'      => 'alterar_owner',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'atribuir_grupo_tarifario_part_number');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Atribuir grupo tarifario aos Part Number',
                'slug'      => 'atribuir_grupo_tarifario_part_number',
                'permissao_especial'      => '1'
            ));
        }


        $this->db->where('slug', 'criar_grupos_perguntas');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Criar Grupos Perguntas',
                'slug'      => 'criar_grupos_perguntas',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'devolver_pn_ajuste_descricao');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Devolver PNs ajuste de descrição',
                'slug'      => 'devolver_pn_ajuste_descricao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'excluir_perguntas_indevidas');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Excluir Perguntas Indevidas',
                'slug'      => 'excluir_perguntas_indevidas',
                'permissao_especial'      => '1'
            ));
        }


        $this->db->where('slug', 'selecionar_grupo_perguntas');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Selecionar Grupo de Perguntas',
                'slug'      => 'selecionar_grupo_perguntas',
                'permissao_especial'      => '1'
            ));
        }
 

        $this->db->where('slug', 'homologar_atributos_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Homologar Atributos (Workflow)',
                'slug'      => 'homologar_atributos_workflow',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'homologar_atributos_homologacao');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Homologar Atributos (Homologação)',
                'slug'      => 'homologar_atributos_homologacao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Preencher Atributos (Workflow)',
                'slug'      => 'preencher_atributos_workflow',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_dados_tecnicos');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Preencher Atributos (Dados Técnicos)',
                'slug'      => 'preencher_atributos_dados_tecnicos',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_homologacao');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Preencher Atributos (Homologação)',
                'slug'      => 'preencher_atributos_homologacao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'movimentar_itens_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Movimentar Itens (Atributos - Workflow)',
                'slug'      => 'movimentar_itens_workflow',
                'permissao_especial'      => '1'
            ));
        }
 
        if (!$this->db->field_exists('ultimo_login', $this->_table_user)) {
            $this->dbforge->add_column($this->_table_user, array(
                'ultimo_login' => array(
                    'type' => 'DATETIME',
                    'null' => TRUE
                )
            ));
        }


    }

    public function down()
    {
 
        $this->db->where('slug', 'alterar_criticidade');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Alterar Criticidade',
                'slug'      => 'alterar_criticidade',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'alterar_owner');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Alterar owner',
                'slug'      => 'alterar_owner',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'atribuir_grupo_tarifario_part_number');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Atribuir grupo tarifario aos Part Number',
                'slug'      => 'atribuir_grupo_tarifario_part_number',
                'permissao_especial'      => '1'
            ));
        }


        $this->db->where('slug', 'criar_grupos_perguntas');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Criar Grupos Perguntas',
                'slug'      => 'criar_grupos_perguntas',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'devolver_pn_ajuste_descricao');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Devolver PNs ajuste de desc. do setor',
                'slug'      => 'devolver_pn_ajuste_descricao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'excluir_perguntas_indevidas');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Excluir Perguntas Indevidas',
                'slug'      => 'excluir_perguntas_indevidas',
                'permissao_especial'      => '1'
            ));
        }
 


        $this->db->where('slug', 'selecionar_grupo_perguntas');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Selecionar Grupo de Perguntas',
                'slug'      => 'selecionar_grupo_perguntas',
                'permissao_especial'      => '1'
            ));
        }
 

        $this->db->where('slug', 'homologar_atributos_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Homologar Atributos (Workflow)',
                'slug'      => 'homologar_atributos_workflow',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'homologar_atributos_homologacao');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Homologar Atributos (Homologação)',
                'slug'      => 'homologar_atributos_homologacao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Preencher Atributos (Workflow)',
                'slug'      => 'preencher_atributos_workflow',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_dados_tecnicos');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Preencher Atributos (Dados Técnicos)',
                'slug'      => 'preencher_atributos_dados_tecnicos',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_homologacao');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Preencher Atributos (Homologação)',
                'slug'      => 'preencher_atributos_homologacao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'movimentar_itens_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Movimentar Itens (Atributos - Workflow)',
                'slug'      => 'movimentar_itens_workflow',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'movimentar_itens');
        $query = $this->db->get('permissao', 1);

        if (!$query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Movimentar itens  (Atributos)',
                'slug'      => 'movimentar_itens'
            ));
        }

        $this->db->where('slug', 'preencher_atributos');
        $query = $this->db->get('permissao', 1);

        if (!$query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Preencher Atributos',
                'slug'      => 'preencher_atributos'
            ));
        }

        $this->db->where('slug', 'homologar_atributos');
        $query = $this->db->get('permissao', 1);

        if (!$query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Homologar Atributos',
                'slug'      => 'homologar_atributos'
            ));
        }


        if ($this->db->field_exists('permissao_especial', $this->_table)) {
            $this->dbforge->drop_column($this->_table, 'permissao_especial');
        }
        
    }
}
